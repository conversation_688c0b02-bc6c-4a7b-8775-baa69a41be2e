import Vue from "vue";
import Vuex from "vuex";
import createPersistedstate from "vuex-persistedstate";

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    menuIdx: 2,
    userInfo: {},
  },
  getters: {},
  actions: {
    changeMenu(context, value) {
      context.commit("ChangeMenu", value);
    },
    changeUserInfo(context, value) {
      context.commit("ChangeUserInfo", value);
    },
  },
  mutations: {
    ChangeMenu(state, index) {
      state.menuIdx = index;
    },
    ChangeUserInfo(state, value) {
      state.userInfo = value;
    },
  },
  plugins: [
    // createPersistedstate(),
    createPersistedstate({
      // 默认使用localstorage
      storage: localStorage,
      reducer(val) {
        // console.log(val);
        return {
          menuIdx: val.menuIdx,
          userInfo: val.userInfo,
        };
      },
    }),
  ],
});

export default store;
