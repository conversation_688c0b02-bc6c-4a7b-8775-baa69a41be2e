import green from "@/assets/img/topo/green.png";
import yellow from "@/assets/img/topo/yellow.png";
import red from "@/assets/img/topo/red.png";
let imgMap = {
  0: green,
  1: red,
  2: yellow,
};

let dashStyle = {
  lineDash: [10, 5],
  endArrow: true,
};

let data = {
  nodes: [
    {
      id: "1",
      label: "请求回放1（开始）",
      img: imgMap[0],
    },
    {
      id: "2",
      label: "交易创建2",
      img: imgMap[0],
    },
    {
      id: "3",
      label: "请求回放3",
      img: imgMap[0],
    },
    {
      id: "4",
      label: "请求回放4",
      img: imgMap[0],
    },
    {
      id: "5",
      label: "请求回放5",
      img: imgMap[0],
    },
    {
      id: "6",
      label: "请求回放6",
      img: imgMap[0],
    },
    {
      id: "7",
      label: "请求回放2（结束）7",
      img: imgMap[0],
    },
    {
      id: "8",
      label: "请求回放8",
      img: imgMap[0],
    },
    {
      id: "9",
      label: "请求回放9",
      img: imgMap[0],
    },
    {
      id: "10",
      label: "请求回放10",
      img: imgMap[0],
      x: 100,
      y: 200,

      // 该节点可选的连接点集合，该点有两个可选的连接点
      anchorPoints: [
        [0, 0.5],
        [1, 0.5],
        [0, 0.3],
        [0, 0.7],
        [1, 0.3],
        [1, 0.7],
      ],
      shape: "rect",
    },
    {
      id: "11",
      label: "请求回放11",
      img: imgMap[0],
      //   type: "end",
      x: 300,
      y: 400,
      // 该节点可选的连接点集合，该点有两个可选的连接点
      anchorPoints: [
        [0, 0.5],
        [1, 0.5],
        [0, 0.3],
        [0, 0.7],
        [1, 0.3],
        [1, 0.7],
      ],
      shape: "rect",
    },
  ],

  edges: [
    {
      source: "1",
      target: "2",
      type: "line-arrow",
    },
    {
      source: "1",
      target: "3",
      style: dashStyle,
    },
    {
      source: "2",
      target: "5",
      type: "line-arrow",
    },
    {
      source: "5",
      target: "6",
      type: "line-arrow",
    },
    {
      source: "6",
      target: "7",
      type: "line-arrow",
    },
    {
      source: "3",
      target: "4",
      style: dashStyle,
    },
    {
      source: "4",
      target: "7",
      style: dashStyle,
    },
    {
      source: "7",
      target: "8",
      type: "line-arrow",
    },
    {
      source: "7",
      target: "9",
      style: dashStyle,
    },
    {
      source: "8",
      target: "10",
      type: "line-arrow",
    },
    {
      source: "9",
      target: "10",
      style: dashStyle,
    },
    {
      source: "10",
      target: "11",

      // 该边连入 source 点的第 0 个 anchorPoint，
      sourceAnchor: 4,
      targetAnchor: 2,
      type: "line-arrow",
    },
    {
      source: "10",
      target: "11",

      // 该边连入 source 点的第 0 个 anchorPoint，
      sourceAnchor: 5,
      targetAnchor: 3,
      style: dashStyle,
    },
  ],
};

export { data };
