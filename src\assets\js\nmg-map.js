/**
 * <AUTHOR>
 * 内蒙古自治区地图数据
 * {
 *   label: '地市名',
 *   value: '地市四位区号',
 *   [ transCode: '地市三位编码' ]
 *   [ coords: [默认经，纬度] ]
 *   json: { echarts渲染对应地市的json } 
 * }
 */
import nmgMap from '../json/json/nm-map.json'

import hlbeMap from '../json/json/hulunbeier.json' // 呼伦贝尔市
import hhhtMap from '../json/json/huhehaote.json' // 呼和浩特市
import btMap from '../json/json/baotou.json' // 包头市
import whMap from '../json/json/wuhai.json' // 乌海市
import wlcbMap from '../json/json/wulanchabu.json' // 乌兰察布市
import tlMap from '../json/json/tongliao.json' // 通辽市
import cfMap from '../json/json/chifeng.json' // 赤峰市
import eedsMap from '../json/json/eerduosi.json' // 鄂尔多斯市
import bmMap from '../json/json/bameng.json' // 巴盟市
import xlglMap from '../json/json/ximeng.json' // 锡林郭勒盟
import xamMap from '../json/json/xinganmeng.json' // 兴安盟市
import alsMap from '../json/json/alashan.json' // 阿拉善市

export const innerMongoliaMap = [{
    label: '内蒙古',
    value: '10',
    cityZoom: 0.95,
    json: nmgMap,
    uuid: "DISTRICT-00001-00010"
}, {
    label: '呼伦贝尔市',
    value: '0470',
    cityZoom: 0.55,
    json: hlbeMap
}, {
    label: '呼和浩特市',
    value: '0471',
    cityZoom: 0.34,
    json: hhhtMap,
    uuid: "DISTRICT-00001-00010-00001"
}, {
    label: '包头市',
    value: '0472',
    cityZoom: 0.34,
    json: btMap
}, {
    label: '乌海市',
    value: '0473',
    cityZoom: 0.33,
    json: whMap
}, {
    label: '乌兰察布市',
    value: '0474',
    cityZoom: 0.46,
    json: wlcbMap
}, {
    label: '通辽市',
    value: '0475',
    cityZoom: 0.45,
    json: tlMap
}, {
    label: '赤峰市',
    value: '0476',
    cityZoom: 0.4,
    json: cfMap
}, {
    label: '鄂尔多斯市',
    value: '0477',
    cityZoom: 0.5,
    json: eedsMap,
    uuid: "DISTRICT-00001-00010-00012"
}, {
    label: '巴彦淖尔市',
    value: '0478',
    cityZoom: 0.62,
    json: bmMap
}, {
    label: '锡林郭勒盟',
    value: '0479',
    cityZoom: 0.58,
    json: xlglMap
}, {
    label: '兴安盟',
    value: '0482',
    cityZoom: 0.4,
    json: xamMap
}, {
    label: '阿拉善盟',
    value: '0483',
    cityZoom: 0.55,
    json: alsMap
}]