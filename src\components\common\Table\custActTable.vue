<template>
	<!-- 自定义列表 -->
	<div class="cust-act-table hf">
		<!-- height="26vh" -->
		<el-table
		ref="table"
			:data="tableList"
			:header-cell-style="{
				color: '#19D6FF',
				'background-color': 'rgba(15, 52, 124, 0)',
				'text-align': 'center',
				'font-size': '20px',
                 'font-family':' Alibaba PuHuiTi',
                 'font-weight': '500',
				'border-right': '1px rgba(15, 0, 137, .8) solid',
				'border':'none'
			}"
			border
			style="width: 100%;max-height: 38vh; 
			min-height: 200px;
			border-radius: 4px 4px 0 0;
			border:none;"
			height="90%"
			v-loading="loading"
			element-loading-text="拼命加载中..."
			:highlight-current-row="false"
			class="table-part wf"
			:rowKey="(record) => record.index"
			@row-click="rowClick"
			:row-class-name="setRowClassName"
			@cell-mouse-enter="mouseEnter"
            @cell-mouse-leave="mouseLeave"
		>
			<!-- 列渲染 -->
			<el-table-column type="index" width="60" label="序号" align="center" v-if="showIdx"></el-table-column>
			<el-table-column
				v-for="(i, idx) in colConfigs"
				:key="'col' + idx"
				:prop="i.prop"
				:label="i.label"
				:align="i.align"
				:width="i.width"
				:show-overflow-tooltip="true"
				filter-placement="top"
			>
				<template slot-scope="scope">
					<div>
						<el-link  v-if="i.prop === 'circuitName' && istopo == true" @click="goTopo(scope.row)" style="color: #05e1ff">{{ scope.row.circuitName}}</el-link>
						<el-link v-else-if="i.prop === 'workOrderNumber'" @click="goDetail(scope.row)"  :style="scope.row.dispatchedStatus == 0 ? 'color:red' : 'color:#05e1ff'">{{ scope.row.workOrderNumber }}</el-link>
						<span v-else>{{ scope.row[i.prop] ? scope.row[i.prop] : "" }}</span>
						
					</div>
				</template>
			</el-table-column>
			<el-table-column
              label="操作"
			  v-if="showBigIdx"
			  align="center">
			  <template>  
				  <el-link type="primary">查看详情</el-link>       
              </template>
            </el-table-column>
			<!-- 
              fixed="right" -->
			<el-table-column
              label="操作"
			  align="center"
              width="100"
			  v-if="showIdx">
              <template slot-scope="scope">                
				<el-button type="text" size="small" :disabled="scope.row.workOrderNumber ? true : false" @click="manualDispatch(scope.row)" v-permission="'100028'">派单</el-button>
                <el-button type="text" size="small" @click="clearAlarm(scope.row)" v-permission="'100029'">清除</el-button>
              </template>
            </el-table-column>

			<div slot="empty">
				<i class="el-icon-warning fs16"></i>
				<span class="ml10 fs16">暂无数据！</span>
			</div>
		</el-table>
		<!-- 分页 -->
		<el-pagination
		    class="page-list"
			@size-change="handleSizeChange"
			@current-change="handleCurrentChange"
			:current-page.sync="currentPage"
			:page-sizes="[1,10, 20, 30, 40, 50]"
			:page-size="size"
			background
			layout="total, prev, pager, next, sizes"
			:total.sync="allPage"
			v-if="pagin"
		>
		</el-pagination>
	</div>
</template>

<script>
	import permission from '../permission.js';
    export default {
    directives: {
      permission
    },
		name: "custActTable",
		props: {
			istopo: {
				type: Boolean,
				default: () => false,
			},
			showIdx: {
				type: Boolean,
				default: () => false,
			},
			showBigIdx: {
				type: Boolean,
				default: () => false,
			},
			list: {
				type: Array,
				default: () => [],
			},
			colConfigs: {
				type: Array,
				default: () => [],
			},
			loading: {
				type: Boolean,
				default: () => false,
			},
			rKey: {
				type: String,
				default: () => "",
			},
			tableType: {
				type: String,
				default: () => "",
			},
			pageNum: {
				type: Number,
				default: () => 1,
			},
			pageSize: {
				type: Number,
				default: () => 10,
			},
			total: {
				type: Number,
				default: () => 0,
			},
			pagin: {
				type: Boolean,
				default: () => true,
			},
			resourceName: {
				type: String,
				default: () => "",
			},
		},
		data() {
			return {
				tableList: [],
				columns: [],
				// resourceName: "",
				currentPage: 1, // 当前页
				size: 10, // 每页条数
				allPage: 0, // 总条数
			};
		},
		watch: {
			list: {
				handler(n, o) {
					n.map((i, idx) => {
						i.idx = idx + 1;
					});
					this.tableList = n;
				},
				deep: true,
			},
			colConfigs: {
				handler(n, o) {
					this.columns = n;
				},
				deep: true,
			},
			rKey(n) {},
			tableType(n) {},
			pageNum(n) {
				if (n) {
					this.currentPage = n;
				}
			},
			pageSize(n) {
				if (n) {
					this.size = n;
				}
			},
			total(n) {
				if (n) {
					this.allPage = n;
				}
			},
		},
		mounted() {
			console.log(this.tableList);
			let list = this.list;
			list.map((i, idx) => {
				i.idx = idx + 1;
			});
			setTimeout(() => {
				// this.tableList = list ? list : [];
				// this.allPage = this.total ? this.total : 0;
				this.columns = this.colConfigs;
			}, 1000);
			this.autoTable()
		},
		methods: {
			// table自动滚动
            autoTable() {
              const table = this.$refs.table
              const divData = table.bodyWrapper
              this.tableInterval = setInterval(() => {
                  // 元素自增距离顶部1像素
                  divData.scrollTop += 1
                  // 判断元素是否滚动到底部(可视高度+距离顶部=整个高度)
                  if (divData.clientHeight + divData.scrollTop == divData.scrollHeight) {
                      // 重置table距离顶部距离
                      divData.scrollTop = 0
                  }
              }, 100)
            },
            //鼠标移入
            mouseEnter() {
              clearInterval(this.tableInterval)
              this.tableInterval = null
            },
            //鼠标移出
            mouseLeave() {
              this.autoTable()
            },
			// 手动清除
			clearAlarm(row){
				this.$emit("clearAlarm", row);
			},
			// 派单
			manualDispatch(row){
				this.$emit("manualDispatch", row);
			},
			/**
			 * <AUTHOR>
			 * 点击列表回调
			 */
			rowClick(row) {
				if (this.tableType != "alarmMonitor") {
					this.resourceName = row.resourceName;
					this.$emit("getSelectedRow", row);
				}
			},
			/**
			 * <AUTHOR>
			 * 设置选中行样式
			 */
			setRowClassName({ row }) {
				if (this.resourceName === row.bizType) {
					return "current-row";
				}
			},
			/**
			 * <AUTHOR>
			 * 编辑
			 */
			goEdit(row) {
				this.$emit("getEdit", row);
			},
			/**
			 * <AUTHOR>
			 * 删除
			 */
			goDelete(row) {
				this.$emit("getDelete", row);
			},
			/**
			 * <AUTHOR>
			 * 点击查看详情
			 */
			goDetail(row) {
				row.resourceName = row.bizType;
				this.rowClick(row);
				this.$emit("getDetail", row);
			},
			goTopo(row){
				// console.log(row);
				this.$emit("getTopoData", row);
			},
			/**
			 * <AUTHOR>
			 * 监听每页条数选择
			 */
			handleSizeChange(val) {
				this.size = val;
				this.$emit("changePage", {
					pageNum: this.currentPage,
					pageSize: val,
				});
			},
			/**
			 * <AUTHOR>
			 * 监听当前页
			 */
			handleCurrentChange(val) {
				this.currentPage = val;
				this.$emit("changePage", {
					pageNum: val,
					pageSize: this.size,
				});
			},
		},
	};
</script>

<style scoped lang="less">
	.cust-act-table {
		::v-deep {
			// .el-table__body {
			// 	tr {
			// 		cursor: pointer;
			// 	}
			// }
		.el-table, .el-table__expanded-cell {
          background-color: transparent !important;
        }
        .el-table thead.is-group th.el-table__cell{
          background-color: transparent !important;
        }
        /*定义滚动条轨道 内阴影+圆角*/
         .el-table__body-wrapper::-webkit-scrollbar-track {
          box-shadow: inset 0 0 0Px #163479 !important;
          border-radius: 10Px;
          background-color: #163479 !important;
        }
        .el-table tr {
			color: #fff;
          background-color: transparent;
          cursor: pointer;
        }
        .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
          background-color: #06529d !important;
          cursor: pointer;
        }
        .el-table--striped .el-table__body tr.el-table__row--striped:hover> td.el-table__cell{
          background-color: #06529d !important;
          cursor: pointer;
        }
        .el-table--striped {
          background-color: rgba(12,33,87,0)!important;
        }
        .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
          background: rgba(12,21,69,1);
        }
         .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
          width: 10Px;
        }
        
        /*定义滑块 内阴影+圆角*/
         .el-table__body-wrapper::-webkit-scrollbar-thumb {
          border-radius: 10Px;
          box-shadow: inset 0 0 0Px #4C72C9;
          background: linear-gradient(-55deg, #4C72C9 0%, #4C72C9 100%);
        }
        .el-table__body-wrapper::-webkit-scrollbar {
          width: 8Px; // 横向滚动条
          height: 8Px; // 纵向滚动条 必写
        }
        .el-table--border::after, .el-table--group::after, .el-table::before{
          background-color:transparent;
        }
        .el-table--border th.el-table__cell.gutter{
          display: none;
        }
        /* 表格鼠标悬浮时的样式（高亮） */
        .el-table--enable-row-hover .el-table__body tr:hover {
          background-color: rgba(255, 255, 255, 0);
        }
        /*表格鼠标悬停的样式（背景颜色）*/
         .el-table tbody tr:hover > td {
          background-color: rgba(255, 255, 255, 0);
        }
        .el-table__body .el-table__row.hover-row td{
           background-color: rgba(255, 255, 255, 0);
        }
		.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
			border-bottom:transparent;
		}
		.el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
            border-right: transparent;
        }
		.el-table tr:nth-child(even) {
            background: rgba(40, 72, 121, 0.29);
        }
		.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li {
			background-color: transparent;
			border: 1px solid rgba(4, 56, 226, 0.58);
		}
		.el-pagination__total {
			color: #fff;
		}
		.el-input__inner {
			color: #fff;
			background-color: transparent;
			border: 1px solid rgba(4, 56, 226, 0.58);
		}
		
		.el-table__fixed-right { // 右固定列
	    	background-color: #080808e0 !important;
	    }
		.el-table__fixed-right-patch {
			background-color: transparent;
			border: none;
		}
		.el-table__fixed-right::before, .el-table__fixed::before {
			background-color: transparent;
		}




			.el-tooltip__popper {
				top: 0;
				left: 0;
			}
			.el-loading-spinner {
				.el-loading-text,
				.path {
					color: #02dbff;
					stroke: #02dbff;
				}
			}
		}
		.level1 {
			color: #ff4349;
		}
		.level2 {
			color: #ff8b00;
		}
		.level3 {
			color: #87d067;
		}
		.level4 {
			color: #0086e5;
		}
		.green {
			color: #18be6b;
		}
		.red {
			color: #ed4014;
		}
	}
</style>

<style lang="less">
	.table-part {
		// /deep/ .el-table--group {
        //   border:0 solid #EBEEF5;
        // }
        // /deep/ .el-table__body-wrapper::-webkit-scrollbar:hover {
        //   width: 12Px !important;
        // }
        /deep/.el-table, .el-table__expanded-cell {
          background-color: #0C2157;
        }
        /deep/.el-table thead.is-group th.el-table__cell{
          background-color: #0C2157;
        }
        /*定义滚动条轨道 内阴影+圆角*/
        /deep/ .el-table__body-wrapper::-webkit-scrollbar-track {
          box-shadow: inset 0 0 0Px #163479;
          border-radius: 10Px;
          background-color: #163479;
        }
        /deep/.el-table tr {
          background-color: #092156 !important;
          cursor: pointer;
        }
        /deep/.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
          background-color: #06529d;
          cursor: pointer;
        }
        /deep/.el-table--striped .el-table__body tr.el-table__row--striped:hover> td.el-table__cell{
          background-color: #06529d;
          cursor: pointer;
        }
        /deep/.el-table--striped {
          background-color: rgba(12,33,87,0)!important;
        }
        /deep/.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
          background: rgba(12,21,69,1);
        }
        /deep/ .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
          width: 10Px;
        }
        
        /*定义滑块 内阴影+圆角*/
        /deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
          border-radius: 10Px;
          box-shadow: inset 0 0 0Px #4C72C9;
          background: linear-gradient(-55deg, #4C72C9 0%, #4C72C9 100%);
        }
        /deep/ .el-table__body-wrapper::-webkit-scrollbar {
          width: 8Px; // 横向滚动条
          height: 8Px; // 纵向滚动条 必写
        }
        /deep/ .el-table--border::after, .el-table--group::after, .el-table::before{
          background-color:transparent;
        }
        /deep/ .el-table--border th.el-table__cell.gutter{
          display: none;
        }
        /* 表格鼠标悬浮时的样式（高亮） */
        /deep/ .el-table--enable-row-hover .el-table__body tr:hover {
          background-color: rgba(255, 255, 255, 0);
        }
        /*表格鼠标悬停的样式（背景颜色）*/
        /deep/ .el-table tbody tr:hover > td {
          background-color: rgba(255, 255, 255, 0);
        }
        /deep/ .el-table__body .el-table__row.hover-row td{
           background-color: rgba(255, 255, 255, 0);
        }
		// .el-table__cell {
		// 	background: rgba(21, 100, 154, 1) !important;
		// 	span {
		// 		color: #e1e9f9 !important;
		// 	}
		// }
		// &:hover {
		// 	color: #e1e9f9 !important;
		// 	background: rgba(21, 100, 154, 1) !important;
		// 	.el-table__cell {
		// 		background: rgba(21, 100, 154, 1) !important;
		// 		span {
		// 			color: #e1e9f9 !important;
		// 		}
		// 	}
		// }
	}
	::v-deep .el-table__fixed { // 左固定列
    height: auto !important;
    bottom: 40px !important; // 改为自动高度后，设置与父容器的底部距离，高度会动态改变，值可以设置比滚动条的高度稍微大一些,这个根据实际情况改
  }
	
.page-list {
    text-align: right;
    width: 100%;
    color:#5299C9;
  }
  ::v-deep .page-list .el-pagination__total {
    color: #5299C9 !important;
  }
</style>