<template>
  <div class="carousel">
    <i class="el-icon-back prevComponent" @click="prevComponent"></i>
    <i class="el-icon-right nextComponent" @click="nextComponent"></i>
    <div @mouseenter="toggleCarousel" @mouseleave="toggleCarousel">
      <transition name="fade" mode="out-in">
        <cndResourceAlarm v-if="currentComponent === 'cndResourceAlarm'" />
        <trsResourceAlarm v-if="currentComponent === 'trsResourceAlarm'" />
        <cndPropertyAlarm v-if="currentComponent === 'cndPropertyAlarm'" />
        <trsPropertyAlarm v-if="currentComponent === 'trsPropertyAlarm'" />
      </transition>
    </div>
  </div>
</template>

<script>
import cndResourceAlarm from './cndResourceAlarm.vue';
import trsResourceAlarm from './trsResourceAlarm.vue';
import cndPropertyAlarm from './cndPropertyAlarm.vue';
import trsPropertyAlarm from './trsPropertyAlarm.vue';

export default {
  components: {
    cndResourceAlarm,
    trsResourceAlarm,
    cndPropertyAlarm,
    trsPropertyAlarm
  },
  data() {
    return {
      currentComponent: 'cndResourceAlarm',
      componentsList: ['cndResourceAlarm', 'trsResourceAlarm', 'cndPropertyAlarm', 'trsPropertyAlarm'],
      intervalId: null,
      isPaused: false,
    };
  },
  mounted() {
    this.$bus.$on('typeYY', (val) => {
      this.currentComponent = val;
    });
    this.startCarousel();
  },
  beforeDestroy() {
    this.stopCarousel();
  },
  methods: {
    startCarousel() {
      if (!this.isPaused) {
        this.intervalId = setInterval(() => {
          const currentIndex = this.componentsList.indexOf(this.currentComponent);
          const nextIndex = (currentIndex + 1) % this.componentsList.length;
          this.currentComponent = this.componentsList[nextIndex];
        }, 30000); // 10秒
      }
    },
    stopCarousel() {
      clearInterval(this.intervalId);
    },
    toggleCarousel() {
      if (this.isPaused) {
        // 如果已经暂停，点击后继续轮播
        this.isPaused = false;
        this.startCarousel();
      } else {
        // 如果没有暂停，点击后暂停
        this.isPaused = true;
        this.stopCarousel();
      }
    },
    prevComponent() {
      const currentIndex = this.componentsList.indexOf(this.currentComponent);
      const prevIndex = (currentIndex - 1 + this.componentsList.length) % this.componentsList.length;
      this.currentComponent = this.componentsList[prevIndex];
    },
    nextComponent() {
      if (this.isPaused) {
        // 如果已经暂停，点击后继续轮播
        this.isPaused = false;
        this.startCarousel();
      }
      const currentIndex = this.componentsList.indexOf(this.currentComponent);
      const nextIndex = (currentIndex + 1) % this.componentsList.length;
      this.currentComponent = this.componentsList[nextIndex];
    }
  }
};
</script>

<style lang="less" scoped>
/* 过渡效果样式 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
.carousel{
  position: relative;
  .prevComponent{
    position: absolute;
    top: -5%;
    right: 40px;
    color: #00C9FD;
    font-size: 30px;
    cursor: pointer;
    user-select: none;
    &:hover{
      color: #009ED5;
    }
  }
  .nextComponent{
    position: absolute;
    right: 10px;
    top: -5%;
    color: #00C9FD;
    font-size: 30px;
    cursor: pointer;
    user-select: none;
    &:hover{
      color: #009ED5;
    }
  }

}
</style>