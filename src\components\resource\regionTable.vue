<template>
<div style="height: 70%;">
  <div class="nc-query">
    <el-form :inline="true" :model="queryForm" label-width="100px" size="small">
      <el-form-item class="formItem" id="loadMoreSelect" v-for="field in queryFields" :key="field.columnName"
        :label="field.displayName">
        <template v-if="field.type == 'input'">
          <el-input v-model="queryForm[field.columnName]" clearable></el-input>
        </template>
        <template v-if="field.type == 'string'">
          <el-input v-model="queryForm[field.columnName]" clearable></el-input>
        </template>
        <template v-if="field.type == 'enum'">
          <el-select v-model="queryForm[field.columnName]" remote clearable filterable reserve-keyword
            :multiple-limit="1" @focus="getOptionList(field.columnName)">
            <div class="infinite-list" style="overflow:auto;height:calc(20vh)">
              <el-option class="infinite-list-item" v-for="option in options" :key="option.value" :label="option.label"
                :value="option.value"></el-option>
            </div>
            <!-- v-infinite-scroll="loadmore"
                           :remote-method="remoteMethod" -->
          </el-select>
        </template>
        <!-- 其他类型的表单控件 -->
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button type="primary" @click="getList">查询</el-button>
      </el-form-item>
    </el-form>

  </div>
  <div style="margin:0 0 15px 0">
    <!-- <el-button size="mini" @click="handleTemplateExport()">导出当前页</el-button> -->
    <!-- <el-button size="mini" @click="handleTemplateExport">导出</el-button> -->
    <el-button size="mini" :disabled='multipleSelection.length !== 1' @click="custInfor">确认</el-button>
  </div>
  <el-table ref="multipleTable" v-loading="loading" border :data="resourceData" :header-cell-style="{
    color: '#9ED8FF',
    'background-color': 'rgba(15, 52, 124, 1)',
    'text-align': 'center',
    'font-size': '14px',
    'border-right': '1px rgba(15, 0, 137, .8) solid'
  }" height="420" style="border-radius: 4px 4px 0 0;
      border:none;" @selection-change="handleSelectionChange">
    <el-table-column class-name="tabSel" type="selection" width="55" align="center">
    </el-table-column>
    <el-table-column class-name="tabSel" v-if="classId == 'pubCust' || classId == 'trsCircuit'"
      :type="classId == 'pubCust' || classId == 'trsCircuit' ? 'selection' : 'index'" width="55" align="center">
    </el-table-column>
    <!-- <el-table-column v-if="resourceData && resourceData.length>0" type="index" width="70" label="序号"
                           align="center" show-overflow-tooltip></el-table-column> -->
    <el-table-column v-for="(i, idx) in tableColumns" :key="idx" :label="i.displayName" :prop="i.columnName"
      show-overflow-tooltip>
    </el-table-column>
  </el-table>

  <!-- 分页组件 -->
  <el-pagination class="page-list" @size-change="handleSizeChange" @current-change="handleCurrentChange"
    :current-page.sync="pageNumber" :page-sizes="[10, 20, 30, 40, 50]" :page-size="pageSize" background
    layout="total, prev, pager, next, sizes" :total.sync="total" style="float: right">
  </el-pagination>
</div>
</template>

<script>
export default {
  props: {
    regionDialog: {
      type: Boolean, //要求传递的类型,大写开头
      required: true, //强制传递,否则报错
    },
  },
  watch: {
    regionDialog(val) {
      this.multipleSelection=[];
      if (!val) {
        this.resetQuery()
      }
    }
  },
  data() {
    return {
      loading: false,
      form: {},
      total: 10,
      pageSize: 10,
      pageNumber: 1,
      tableColumns: [{
        displayName: '区域名称',
        columnName: 'district'
      }, {
        displayName: '行政级别',
        columnName: 'disLevel'
      },{
        displayName: '所属管理区域编码',
        columnName: 'districtId'
      }],
      multipleSelection: [],
      resourceData: [],
      classId: 'spcDistrict',
      options: [],
      queryForm: {}, // 查询表单数据
    }
  },
  created() {
    this.initColumn('spcDistrict');
    this.getList()
  },
  methods: {
    initColumn(classId) {
      this.queryForm = {};
      this.pageSize = 10;
      this.pageNumber = 1;
      const jsonData = require('../../assets/json/resource/' + classId + '.json');
      this.tableColumnAll = jsonData.tableConfig.columns;
      this.queryFields = jsonData.queryConfig.columns;
      this.className = jsonData.queryConfig.className;
      let options = [];
      jsonData.tableConfig.columns.map((i, ) => {
        options.push(i.columnName);
      });
      this.allOptions = options;
      this.cities = jsonData.tableConfig.columns;
      this.checkedCities = jsonData.tableConfig.selectColumns;
      this.checkedCitiesa = jsonData.tableConfig.selectColumns;
    },
    getList() {
      let scope = this;
      this.loading = true;
      console.log(this.queryForm);
      
      let params = {
        "districtName": this.queryForm.districName,
        "disLevel": this.queryForm.disLevel,
        "pageNum": this.pageNumber,
        "pageSize": this.pageSize
      }
      this.$api.coreApi.getTransCircuitAZSelectData(params)
      .then((result) => {
        let res = result;
        console.log(res.data.records);
        
        scope.total = res.data.total ? res.data.total : 0;
        // scope.pageNumber = res.pagination.currentPage ? res.pagination.currentPage : 1;
        if (res.data.records && res.data.records.length > 0) {
          scope.resourceData = res.data.records;
          scope.loading = false;
        } else {
          scope.resourceData = [];
          scope.loading = false;
        }
      })
      .catch((err) => {
        console.error(err);
        scope.loading = false;
        scope.$message({
          showClose: true,
          duration: 2000,
          type: "error",
          message: "数据加载失败，系统异常!",
        });
      })
    },
    // 输入框获取焦点时查询下拉列表
    getOptionList(classId) {
      this.personLoading = true;
      this.options = [];
      let queryField = []
      queryField = this.queryFields.filter((i, ) => {
        return i.columnName == classId
      })
      this.classIdS = classId;
      let options = {
        current: this.count,
        isQueryCount: true
      }
      let columnName = queryField[0].columnName
      if (this.query) {
        options.where = {
          [columnName]: {
            $like: this.query
          }
        }
      }

      let params = {
        options: JSON.stringify(options),
      }
      this.$api.slaApi.queryField(queryField[0].url, params)
        .then((result) => {
          this.options = result.data
          this.lists = result.data
          this.personLoading = false;
        });
    },
    gettableColumn(key, queryFields) {
      const foundItem = queryFields.find(i => i.columnName === key);
      return foundItem ? foundItem.tableColumnName : null;
    },
    gettableType(key, queryFields) {
      const foundItem = queryFields.find(i => i.columnName === key);
      return foundItem ? foundItem.type : null;
    },
    // 表格复选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    custInfor() {
      this.$emit('regionFrom', this.multipleSelection[0])
    },
    transformData(data) {
      let transformedData = { where: {} };
      data.forEach(item => {
        for (let key in item) {
          let value = item[key]; // 假设统一使用$like作为条件 
          transformedData.where[key] = value;
        }
      });
      return transformedData;
    },
    // 重置表单
    resetQuery() {
      this.queryForm = {};
      this.pageSize = 10;
      this.pageNumber = 1;
      this.getList()
    },
    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList()
    },
    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getList()
    },
    handleTemplateExport() {
      let scope = this;
      this.loading = true;
      this.checkedCitiesa = this.checkedCities;
      const filteredObjs = scope.tableColumnAll.filter((obj) => scope.checkedCities.includes(obj.columnName));
      this.tableColumns = filteredObjs;
      let queryParam = "";//后续封装查询条件
      let options = {
        current: this.pageNumber,
        limit: this.pageSize,
        isQueryCount: true,
      }
      let arr = []
      Object.keys(scope.queryForm).forEach(key => {
        arr.push({
          talcolumn: scope.gettableColumn(key, scope.queryFields),
          value: scope.queryForm[key]
        })
      })
      let obj = arr.map((i, ) => {
        if (i.value) {
          return { [i.talcolumn]: { $like: i.value } }
        }

      })
      queryParam = this.transformData(obj).where
      let res = [];
      res = this.cities.filter(el => {
        return this.checkedCities.find(element => {
          return element === el.columnName;
        });
      });
      let showColums = res
      if (queryParam) {
        options.where = queryParam;
      }
      let params = {
        options: JSON.stringify(options),
        showColums: JSON.stringify(showColums),
        type: "xlsx"
      }
      this.$api.slaApi.exports(this.classId, params)
      .then(res => {
        // 文件下载
        const blob = new Blob([res], {
          // type: 'application/vnd.ms-excel' // 定义格式
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
        });
        let link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', scope.className + '数据.xlsx');
        link.click();
        link = null;

        this.$message({
          message: '下载成功',
          type: 'success',
          duration: 2000,
          showClose: true
        });
        this.dialogExport = false;
        this.loading = false;
      })
      .catch(() => {
        this.$message({
          message: '下载失败',
          type: 'error',
          duration: 2000,
          showClose: true
        });
        this.dialogExport = false;
        this.loading = false;
      });
    },
  }
}
</script>

<style lang="less" scoped>
.nc-query {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
  height: 85px;
  //   height: 10vh;
}

/deep/ .el-checkbox__inner {
  background-color: transparent;
  border: 1px solid #02dbff;
}

::v-deep {
  .el-table__fixed-right-patch {
    background-color: rgba(23, 70, 137, 1);
  }

  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch {
    border: none;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }

  .el-dialog {
    // height: 30vh;
    // width: 60%;
    background: url("../../assets/images/dialogbg.png") center center no-repeat;
    background-size: 100% 100%;
    margin-top: 10vh !important;
  }

  .el-dialog__header {
    justify-content: left;
    background: none;
  }

  .el-dialog__title {
    justify-content: left;
    line-height: 20px;
    font-weight: 800;
    height: 20px;
    display: flex;
    color: rgba(255, 255, 255, 1);
    font-size: 22px;
  }

  .el-dialog__title::before,
  .el-dialog__title::after {
    content: none;
  }

  .el-dialog__headerbtn {
    width: 30px;
    height: 30px;
    float: right;
    top: 10px;
    right: 10px;
    background: url("../../assets/images/x.png") center center no-repeat;
    background-size: 100% 100%;
  }

  .el-dialog__body {
    height: 85%;

    .detail-info {
      max-height: 800px;
      height: 100%;
    }

    .el-form {
      min-height: 100px;
      width: 95%;
      // margin: 30px auto 20px;
      margin-top: 30px;
      text-align: center;

      .el-form-item__label {
        color: #fff;
      }

      .el-input__inner {
        color: #02dbff;
        background: none;
        border: 1px solid rgba(37, 190, 247, 0.5);
        // height: 24px !important;
        line-height: 24px;
      }

      .el-switch__label,
      .el-checkbox {
        color: #fff;
      }

      .el-button--primary {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;

        &:focus {
          background-color: #409EFF;
          border-color: #409EFF;
        }

        &:hover {
          background-color: #409EFF;
          border-color: #409EFF;
        }
      }

      .el-select-dropdown__item:hover {
        color: #409EFF;
        background: #40a0ff27;
      }

      .el-button--default {
        color: #409EFF;
        background: transparent;
        border-color: #409EFF;
      }
    }
  }
}
</style>