export default {
  data() {
    return {
      /**
       * 响应式配置
       */
      pageH: 0,
      pageW: 0,
      // 设计稿高宽
      design: {
        width: 1920,
        height: 1080
      },
      // 宽高比
      widthHeightRatio: 1,
      // 字体可缩放的最小尺寸
      minWidth: 1400,
      minHidth: 820,
      // 实际开发与设计稿比率
      contrastRadio: 1
    }
  },
  mounted() {
    this.$_initResizeEvent()
  },
  beforeDestroy() {
    this.$_destroyResizeEvent()
  },
  methods: {
    $_initResizeEvent() {
      window.addEventListener('resize', this.$_resizeHandler)
    },
    $_destroyResizeEvent() {
      window.removeEventListener('resize', this.$_resizeHandler)
    }
  }
}
