<template>
  <div
    id="performance-map"
    class="performance-map"
    ref="performanceMap"
    :style="{height: pHeight, width: pWidth}"
  ></div>
</template>

<script>
import echarts from "echarts";
import { innerMongoliaMap } from '@/assets/js/nmg-map.js'
export default {
	name: "performanceMap",
	props: {
    // 引用地图的父级页面名称，用来区分不同的渲染方式
    mapType: {
      type: String,
      default: () => ""
    },
		// 宽度
    width: {
      type: String,
      default: () => "100%"
    },
    // 高度
    height: {
      type: String,
      default: () => ""
    },
    // 指定缩放比例
    custZoom: {
      type: String,
      default: () => "0.5"
    },
    mapData: {
      type: Array,
      default: () => []
    },
    curLine: {
      type: Object,
      default: () => {}
    }
	},
	data() {
		return {
			uploading: false,
      pHeight: '35vh',
      pWidth: '100%',
      dotData: [],
      lineData: [],
      mapInfo: []
		};
	},
	watch: {
    mapType(n) {},
    curLine: {
      handler(n, o) {
        let arr = [];
        arr.push(n)
        this.mapInfo = [...arr];
        this.initMap(arr);
      },
      deep: true
    },
    mapData: {
      handler(n, o) {
        if(n && n.length>0) {
          let data = n;
          this.initMap(data);
        }
      },
      deep: true
    },
    width(n) {
      this.pWidth = n;
      echarts.init(document.getElementById('performance-map')).dispose();
      this.pHeight = this.pHeight;
      this.pWidth = n;
      let data = []
      this.initMap(data);
    },
    height(n) {
      echarts.init(document.getElementById('performance-map')).dispose();
      this.pHeight = n;
      this.pWidth = this.pWidth;
      let data = []
      this.initMap(data);
    },
    custZoom(n) {
      this.custZoom = n;
    },
    innerMongoliaMap: {
      handler(n, o) {
        if(n && n.length > 0) {
          this.cityList = n.map(i => ({
            key: i.value,
            ...i
          }));
          this.changeMap('内蒙古自治区', 0);
          let data = []
          this.initMap(data);
        }
      },
      deep: true
    }
	},
	mounted() {
    // this.getDotData([]);
    // this.getLinesData([]);
    this.cityInfo = innerMongoliaMap[0];
    this.$nextTick(() => {
      this.pHeight = this.height;
      this.pWidth = this.width;
    })
		echarts.registerMap(this.cityInfo.label, this.cityInfo.json);
    let data = [...this.mapInfo]
    this.initMap(data);
    window.addEventListener('resize', this.setHeight());
	},
	methods: {
    /**
     * <AUTHOR>
     * 设置高度
     */
    setHeight() {
      let winH = window.innerHeight-180;
      echarts.init(document.getElementById('performance-map')).dispose();
      this.pHeight = this.height;
      this.pWidth = this.width;
      this.custZoom = this.custZoom;
      let data = []
      this.initMap(data);
    },
    /**
     * <AUTHOR>
     * 初始化地图
     */
    initMap(data) {
      console.log('initMapdata', data);
      let dotDatas = this.getDotData(data);
      let lineDatas = this.getLinesData(data);
      let cityName = this.cityInfo.label ? this.cityInfo.label : null;
      let cityZoom = this.custZoom ? this.custZoom : 0.56;
      let width = document.getElementById('performance-map').clientWidth;
      let option = {
        title: {
          show: false,
        },
        tooltip: {
          show: true,
          trigger: "item",
          triggerOn: 'mousemove|click',
          // textStyle: {
          //   color: "#fff",
          // },
          // extraCssText: "box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
          // triggerTooltip: true,
          // confine: true,  // 将 tooltip 框限制在图表的区域内
          // formatter: (params) => {
          //   let str = `<div>
          //     <p>${params.name}</p>
          //   </div>`;
          //   return str
          // }
        },
        geo: [
          { // 地图背景层
            type: 'map',
            map: cityName,
            zLevel: -3,
            zoom: cityZoom,
            aspectScale: 1,
            layoutCenter: ['50%', '51.5%'],
            layoutSize: width,
            roam: false,
            scaleLimit: { //滚轮缩放的极限控制
              min: 0.5,
              max: 5
            },
            silent: true, // 图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件
            label: { // 城市名称
              show: false,
              emphasis: { // 鼠标经过样式
                show: false,
              }
            },
            itemStyle: {
              areaColor: '#1E4791',
              emphasis: {
                label: {
                  show: false,
                },
                borderWidth: 0,
                areaColor: '#1E4791',
              }
            },
          }
        ],
        series: [
          { // 地图表层
            type: 'map',
            map: cityName,
            zLevel: -1,
            layoutCenter: ["50%", "50%"], 
            layoutSize: width, 
            aspectScale: 1, // 地图的长宽比
            zoom: cityZoom, //当前视角的缩放比例
            roam: false, //是否开启平游或缩放 默认不开启。如果只想要开启缩放或者平移，可以设置成'scale'或者'move'。设置成true为都开启
            scaleLimit: { //滚轮缩放的极限控制
              min: 0.5,
              max: 5
            },
            silent: false, // 图形是否不响应和触发鼠标事件，默认 false，即响应和触发鼠标事件
            label: {// 显示城市名称
              show: true,
              textStyle: {
                color: "#021B30",
                fontSize: 12,
              },
              emphasis: { // 鼠标经过样式
                color: '#021B30',
                show: true,
                fontSize: 14,
                fontWeight: 'bold',
              }
            },
            // 区域颜色
            itemStyle: {
              areaColor: "#25BEF7",
              borderWidth: 1, // 区块的边界宽度
              borderColor: "rgba(162, 231, 255, 0.77)", // 区块的边框颜色
              // opacity: 1, // 支持从 0 到 1 的数字，为 0 时不绘制该图形
              // 高亮时区域的样式
              emphasis: {
                borderWidth: 2, // 区块的边界颜色
                areaColor: "#FFCB31",
                borderColor: "rgba(162, 231, 255, 0.77)", // 区块的边框颜色
              },
            },
            tooltip: {
              show: true,
              trigger: "item",
              triggerOn: 'mousemove|click',
              backgroundColor: "rgba(0, 10, 49, 0.8)",
              extraCssText: "box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
              formatter: (params) => {
                this.currentArea = params.data;
                let name = params.name;
                let str = `<div>
                  <p><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FFCB31;"></span> ${name}</p>
                </div>`;
                return str
              }
            },
          },
          { // 时延散点地图
            name: "时延散点地图",
            type: 'effectScatter',
            coordinateSystem: 'geo',
            showEffectOn: 'render',
            zlevel: 2,
            rippleEffect: {
              number: 2, // 波纹的数量。
              period: 5, // 动画的周期，秒数。
              scale: 4, // 动画中波纹的最大缩放比例。
              brushType: 'stroke', // 波纹的绘制方式，可选 'stroke' 和 'fill'。
            },
            tooltip: { // 时延撒点鼠标经过显示的提示框
              trigger: 'item',
              triggerOn: 'mousemove|click',
              backgroundColor: 'rgba(0, 10, 49, 0.8)',
              extraCssText: 'z-index:100;color:#fff;box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75);',
              confine: true, //是否将 tooltip 框限制在图表的区域内
              formatter: (params, ticket, callback) => {
                let res = '';
                if(this.mapType !== 'alarmMonitor') {
                  res = `<div style="padding:2px 5px;">
                    <div style='color:yellow; font-size: 14px;'>${params.name}</div>
                    <div style="display: flex; align-items: center;padding-top: 6px;">
                      <div style="height: 6px; width: 6px; border-radius: 50%; background:yellow; margin-right: 10px;"></div>
                      <span style='color:#fff;font-size: 12px;margin-right: 20px;'>坐标</span>
                      <span style="font-size: 12px;font-family: 'PangMenZhengDao'">[${params.value}]</span>
                    </div>
                  </div>`;
                } else {
                  res = `<div style="padding:2px 5px;">
                    <div style='color:yellow; font-size: 14px;'>${params.name}</div>
                    <div style="display: flex; align-items: center;padding-top: 6px;">
                      <div style="height: 6px; width: 6px; border-radius: 50%; background:yellow; margin-right: 10px;"></div>
                      <span style='color:#fff;font-size: 12px;margin-right: 20px;'>告警数：</span>
                      <span style="font-size: 14px;font-family: 'PangMenZhengDao'">${params.data.alarmNum != '' ? params.data.alarmNum : 0} 次</span>
                    </div>
                  </div>`;
                }
                return res;
              },
            },
            symbolSize: 4,
            label: {
              formatter: "{b}",
              position: [26, -12],
              color: "red",
              fontSize: 16,
              fontWeight: 600,
              textBorderWidth: 0,
              textShadowBlur: 0,
              borderWidth: 1,
              borderRadius: 6,
              borderColor: "#41ADCC",
              padding: [6, 10, 6, 10],
            },
            itemStyle: {
              color: "yellow",
            },
            data: this.getDotData(data)
          },
          {// 飞线 1
            type: "lines", // 绘制连线
            zlevel: 1,
						symbol: ["none", "arrow"],
						symbolSize: 5,
            effect: {
              show: true,
              period: 2, // 箭头指向速度，值越小速度越快
              trailLength: 0.02, // 特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: 'arrow',
              symbolSize: 3, // 图标大小
            },
            lineStyle: {
              color: '#ffd000',
              type: 'dotted',
              width: 2, // 线条宽度
              opacity: 0.1, // 尾迹线条透明度
              curveness: -0.3, // 尾迹线条曲直度
            },
            label: {
              show: false,
              position: "middle",
              color: "#ffd000",
              fontSize: 16,
              fontWeight: 300,
              textBorderWidth: 0,
              textShadowBlur: 0,
              borderRadius: 5,
              padding: [6, 10, 6, 10],
            },
            tooltip: {
              show: true,
              trigger: "item",
              triggerOn: 'mousemove|click',
              backgroundColor: "rgba(0, 10, 49, 0.8)",
              extraCssText: "box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
              formatter: (params) => {
                this.currentArea = params.data;
                let name = params.name;
                let str = `<div style="display:inline-flex;justify-content:space-between;">
                  <p style="margin:8px 10px 0 0;border-radius:10px;width:8px;height:8px;background-color:#02dbff;"></p> 
                  <p style="font-size:14px;color:#ffffff;font-family: 'PingFangSC-Semibold';">${name}</p>
                </div>`;
                return str
              }
            },
            data: this.getLinesData(data)
            // data: [
              //   {
              //     fromName: "呼和浩特市 内蒙古自治区国土资源信息院",
              //     toName: "阿拉善盟 大数据中心",
              //     coords: [[111.73, 40.83], [105.67, 38.83]],
              //     name: "111"
              //   }
            // ]
          },
        ]
      };

      let echart = echarts.init(document.getElementById('performance-map'))
      echart.setOption(option);

      // echart.on("mousemove", (params) => {
          // console.log('params', params);
          // echart.dispatchAction({
          //   type: 'showTip',
          //   geoIndex: 0,
          //   seriesIndex: 0,
          //   name: params.name,
          // })
      // })
      echart.off("click");
      // 地图点击事件
      echart.on("click", (params) => {
        // console.log('地图点击事件params', params);
      })
    },
    /**
     * <AUTHOR>
     * 获取散点数据
     */ 
		getDotData(data) {
      let resData = [];
      if(this.mapType == 'alarmMonitor') {
        data.map((i) => {
          resData.push(
            {
              name: i.name,
              value: i.value,
              ...i
            }
          );
        });
      } else {
        if(data && data.length != 0) {
          data.map((v) => {
            if(v && v.acity && v.zcity && v.apoint && v.zpoint) {
              resData.push(
                {
                  name: v.acity ? v.acity : "",
                  value: v.apoint,
                },
                {
                  name: v.zcity ? v.zcity : "",
                  value: v.zpoint,
                }
              );
            } else {
              resData = []
            }
          });
        } else {
          resData = []
        }
      }
      return resData;
		},
    /**
     * <AUTHOR>
     * 获取飞线数据
     */
		getLinesData(data) {
      if(this.mapType == 'alarmMonitor') {
        return []
      } else {
        let resData = [];
        if(data && data.length != 0) {
          data.map((v) => {
            if(v && v.bissId && v.bizType && v.apoint && v.zpoint) {
              resData.push({
                id: v.bissId,
                name: v.bizType ? v.bizType : "",
                coords: [v.apoint, v.zpoint],
              });
            } else {
              resData = []
            }
          });
        } else {
          resData = []
        }
        
        return resData;
      }
		},
	},
};
</script>

<style scoped>
.performance-map {
  width: 100%;
  height: 100%;
}
</style>
