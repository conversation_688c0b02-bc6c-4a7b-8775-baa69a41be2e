<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2456344" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">网页</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">运营</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60d;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe60d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">报表</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">防汛应急指挥</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69f;</span>
                <div class="name">网络调度视图</div>
                <div class="code-name">&amp;#xe69f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe620;</span>
                <div class="name">02-综和运营监控</div>
                <div class="code-name">&amp;#xe620;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8bd;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe8bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">区域监控调度中心大屏</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea3f;</span>
                <div class="name">会议</div>
                <div class="code-name">&amp;#xea3f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8fa;</span>
                <div class="name">327全屏</div>
                <div class="code-name">&amp;#xe8fa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8fb;</span>
                <div class="name">328退出全屏</div>
                <div class="code-name">&amp;#xe8fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">笑脸</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">群组</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">视频</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">在线用户</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">通信录</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75c;</span>
                <div class="name">延迟</div>
                <div class="code-name">&amp;#xe75c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec33;</span>
                <div class="name">延迟刷新</div>
                <div class="code-name">&amp;#xec33;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">群聊</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">负责人</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">联系方式</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">周边资源</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">类型</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60c;</span>
                <div class="name">经纬度</div>
                <div class="code-name">&amp;#xe60c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">头像</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe669;</span>
                <div class="name">48小时-01</div>
                <div class="code-name">&amp;#xe669;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">在途量-01</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66b;</span>
                <div class="name">受理量-01</div>
                <div class="code-name">&amp;#xe66b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">竣工量-01</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66d;</span>
                <div class="name">注销率-01</div>
                <div class="code-name">&amp;#xe66d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66e;</span>
                <div class="name">24小时-01</div>
                <div class="code-name">&amp;#xe66e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">关闭共享屏幕</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">共享屏幕</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">好友-01</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">控制-01</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">摄像头-关闭</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">摄像头</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">共享屏幕关闭-01</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe96c;</span>
                <div class="name">麦克风,静音</div>
                <div class="code-name">&amp;#xe96c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">全屏-01</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">好友列表</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">共享屏幕</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">静音</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">消息-01</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">会议邀请</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">录制-01</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">中屏</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">大屏</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">小屏</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1649858044867') format('woff2'),
       url('iconfont.woff?t=1649858044867') format('woff'),
       url('iconfont.ttf?t=1649858044867') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconWebpage"></span>
            <div class="name">
              网页
            </div>
            <div class="code-name">.iconWebpage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyunying"></span>
            <div class="name">
              运营
            </div>
            <div class="code-name">.iconyunying
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyingyong"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.iconyingyong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyingyong1"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.iconyingyong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbaobiao"></span>
            <div class="name">
              报表
            </div>
            <div class="code-name">.iconbaobiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfangxunyingjizhihui"></span>
            <div class="name">
              防汛应急指挥
            </div>
            <div class="code-name">.iconfangxunyingjizhihui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwangluotiaodushitu"></span>
            <div class="name">
              网络调度视图
            </div>
            <div class="code-name">.iconwangluotiaodushitu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zongheyunyingjiankong"></span>
            <div class="name">
              02-综和运营监控
            </div>
            <div class="code-name">.icon-zongheyunyingjiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaoxi1"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.iconxiaoxi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquyujiankongtiaoduzhongxindaping"></span>
            <div class="name">
              区域监控调度中心大屏
            </div>
            <div class="code-name">.iconquyujiankongtiaoduzhongxindaping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhuiyi"></span>
            <div class="name">
              会议
            </div>
            <div class="code-name">.iconhuiyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquanping"></span>
            <div class="name">
              327全屏
            </div>
            <div class="code-name">.iconquanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontuichuquanping"></span>
            <div class="name">
              328退出全屏
            </div>
            <div class="code-name">.icontuichuquanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaolian"></span>
            <div class="name">
              笑脸
            </div>
            <div class="code-name">.iconxiaolian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqunzu"></span>
            <div class="name">
              群组
            </div>
            <div class="code-name">.iconqunzu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.iconxiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconvideo"></span>
            <div class="name">
              视频
            </div>
            <div class="code-name">.iconvideo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzaixianyonghu"></span>
            <div class="name">
              在线用户
            </div>
            <div class="code-name">.iconzaixianyonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontongxinlu"></span>
            <div class="name">
              通信录
            </div>
            <div class="code-name">.icontongxinlu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyanchi"></span>
            <div class="name">
              延迟
            </div>
            <div class="code-name">.iconyanchi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyanchishuaxin"></span>
            <div class="name">
              延迟刷新
            </div>
            <div class="code-name">.iconyanchishuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqunliao"></span>
            <div class="name">
              群聊
            </div>
            <div class="code-name">.iconqunliao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfuzeren"></span>
            <div class="name">
              负责人
            </div>
            <div class="code-name">.iconfuzeren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlianxifangshi"></span>
            <div class="name">
              联系方式
            </div>
            <div class="code-name">.iconlianxifangshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhoubianziyuan"></span>
            <div class="name">
              周边资源
            </div>
            <div class="code-name">.iconzhoubianziyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconleixing"></span>
            <div class="name">
              类型
            </div>
            <div class="code-name">.iconleixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjingweidu"></span>
            <div class="name">
              经纬度
            </div>
            <div class="code-name">.iconjingweidu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontouxiang"></span>
            <div class="name">
              头像
            </div>
            <div class="code-name">.icontouxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon48xiaoshi-01"></span>
            <div class="name">
              48小时-01
            </div>
            <div class="code-name">.icon48xiaoshi-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzaituliang-01"></span>
            <div class="name">
              在途量-01
            </div>
            <div class="code-name">.iconzaituliang-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouliliang-01"></span>
            <div class="name">
              受理量-01
            </div>
            <div class="code-name">.iconshouliliang-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjungongliang-01"></span>
            <div class="name">
              竣工量-01
            </div>
            <div class="code-name">.iconjungongliang-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhuxiaoshuai-01"></span>
            <div class="name">
              注销率-01
            </div>
            <div class="code-name">.iconzhuxiaoshuai-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon24xiaoshi-01"></span>
            <div class="name">
              24小时-01
            </div>
            <div class="code-name">.icon24xiaoshi-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguanbigongxiangpingmu"></span>
            <div class="name">
              关闭共享屏幕
            </div>
            <div class="code-name">.iconguanbigongxiangpingmu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongongxiangpingmu1"></span>
            <div class="name">
              共享屏幕
            </div>
            <div class="code-name">.icongongxiangpingmu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhaoyou-01"></span>
            <div class="name">
              好友-01
            </div>
            <div class="code-name">.iconhaoyou-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconkongzhi-01"></span>
            <div class="name">
              控制-01
            </div>
            <div class="code-name">.iconkongzhi-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshexiangtou-guanbi"></span>
            <div class="name">
              摄像头-关闭
            </div>
            <div class="code-name">.iconshexiangtou-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshexiangtou"></span>
            <div class="name">
              摄像头
            </div>
            <div class="code-name">.iconshexiangtou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongongxiangpingmuguanbi-01"></span>
            <div class="name">
              共享屏幕关闭-01
            </div>
            <div class="code-name">.icongongxiangpingmuguanbi-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmic-off-full"></span>
            <div class="name">
              麦克风,静音
            </div>
            <div class="code-name">.iconmic-off-full
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquanping-01"></span>
            <div class="name">
              全屏-01
            </div>
            <div class="code-name">.iconquanping-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhaoyouliebiao"></span>
            <div class="name">
              好友列表
            </div>
            <div class="code-name">.iconhaoyouliebiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongongxiangpingmu"></span>
            <div class="name">
              共享屏幕
            </div>
            <div class="code-name">.icongongxiangpingmu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjingyin"></span>
            <div class="name">
              静音
            </div>
            <div class="code-name">.iconjingyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaoxi-01"></span>
            <div class="name">
              消息-01
            </div>
            <div class="code-name">.iconxiaoxi-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhuiyiyaoqing"></span>
            <div class="name">
              会议邀请
            </div>
            <div class="code-name">.iconhuiyiyaoqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconluzhi-01"></span>
            <div class="name">
              录制-01
            </div>
            <div class="code-name">.iconluzhi-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconsousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.iconsousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongping"></span>
            <div class="name">
              中屏
            </div>
            <div class="code-name">.iconzhongping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondaping"></span>
            <div class="name">
              大屏
            </div>
            <div class="code-name">.icondaping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaoping"></span>
            <div class="name">
              小屏
            </div>
            <div class="code-name">.iconxiaoping
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconWebpage"></use>
                </svg>
                <div class="name">网页</div>
                <div class="code-name">#iconWebpage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyunying"></use>
                </svg>
                <div class="name">运营</div>
                <div class="code-name">#iconyunying</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyingyong"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#iconyingyong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyingyong1"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#iconyingyong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbaobiao"></use>
                </svg>
                <div class="name">报表</div>
                <div class="code-name">#iconbaobiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfangxunyingjizhihui"></use>
                </svg>
                <div class="name">防汛应急指挥</div>
                <div class="code-name">#iconfangxunyingjizhihui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwangluotiaodushitu"></use>
                </svg>
                <div class="name">网络调度视图</div>
                <div class="code-name">#iconwangluotiaodushitu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zongheyunyingjiankong"></use>
                </svg>
                <div class="name">02-综和运营监控</div>
                <div class="code-name">#icon-zongheyunyingjiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoxi1"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#iconxiaoxi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquyujiankongtiaoduzhongxindaping"></use>
                </svg>
                <div class="name">区域监控调度中心大屏</div>
                <div class="code-name">#iconquyujiankongtiaoduzhongxindaping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhuiyi"></use>
                </svg>
                <div class="name">会议</div>
                <div class="code-name">#iconhuiyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquanping"></use>
                </svg>
                <div class="name">327全屏</div>
                <div class="code-name">#iconquanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontuichuquanping"></use>
                </svg>
                <div class="name">328退出全屏</div>
                <div class="code-name">#icontuichuquanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaolian"></use>
                </svg>
                <div class="name">笑脸</div>
                <div class="code-name">#iconxiaolian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqunzu"></use>
                </svg>
                <div class="name">群组</div>
                <div class="code-name">#iconqunzu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#iconxiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconvideo"></use>
                </svg>
                <div class="name">视频</div>
                <div class="code-name">#iconvideo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzaixianyonghu"></use>
                </svg>
                <div class="name">在线用户</div>
                <div class="code-name">#iconzaixianyonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontongxinlu"></use>
                </svg>
                <div class="name">通信录</div>
                <div class="code-name">#icontongxinlu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyanchi"></use>
                </svg>
                <div class="name">延迟</div>
                <div class="code-name">#iconyanchi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyanchishuaxin"></use>
                </svg>
                <div class="name">延迟刷新</div>
                <div class="code-name">#iconyanchishuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqunliao"></use>
                </svg>
                <div class="name">群聊</div>
                <div class="code-name">#iconqunliao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfuzeren"></use>
                </svg>
                <div class="name">负责人</div>
                <div class="code-name">#iconfuzeren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlianxifangshi"></use>
                </svg>
                <div class="name">联系方式</div>
                <div class="code-name">#iconlianxifangshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhoubianziyuan"></use>
                </svg>
                <div class="name">周边资源</div>
                <div class="code-name">#iconzhoubianziyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconleixing"></use>
                </svg>
                <div class="name">类型</div>
                <div class="code-name">#iconleixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjingweidu"></use>
                </svg>
                <div class="name">经纬度</div>
                <div class="code-name">#iconjingweidu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontouxiang"></use>
                </svg>
                <div class="name">头像</div>
                <div class="code-name">#icontouxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon48xiaoshi-01"></use>
                </svg>
                <div class="name">48小时-01</div>
                <div class="code-name">#icon48xiaoshi-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzaituliang-01"></use>
                </svg>
                <div class="name">在途量-01</div>
                <div class="code-name">#iconzaituliang-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouliliang-01"></use>
                </svg>
                <div class="name">受理量-01</div>
                <div class="code-name">#iconshouliliang-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjungongliang-01"></use>
                </svg>
                <div class="name">竣工量-01</div>
                <div class="code-name">#iconjungongliang-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhuxiaoshuai-01"></use>
                </svg>
                <div class="name">注销率-01</div>
                <div class="code-name">#iconzhuxiaoshuai-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon24xiaoshi-01"></use>
                </svg>
                <div class="name">24小时-01</div>
                <div class="code-name">#icon24xiaoshi-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguanbigongxiangpingmu"></use>
                </svg>
                <div class="name">关闭共享屏幕</div>
                <div class="code-name">#iconguanbigongxiangpingmu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongongxiangpingmu1"></use>
                </svg>
                <div class="name">共享屏幕</div>
                <div class="code-name">#icongongxiangpingmu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhaoyou-01"></use>
                </svg>
                <div class="name">好友-01</div>
                <div class="code-name">#iconhaoyou-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconkongzhi-01"></use>
                </svg>
                <div class="name">控制-01</div>
                <div class="code-name">#iconkongzhi-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshexiangtou-guanbi"></use>
                </svg>
                <div class="name">摄像头-关闭</div>
                <div class="code-name">#iconshexiangtou-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshexiangtou"></use>
                </svg>
                <div class="name">摄像头</div>
                <div class="code-name">#iconshexiangtou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongongxiangpingmuguanbi-01"></use>
                </svg>
                <div class="name">共享屏幕关闭-01</div>
                <div class="code-name">#icongongxiangpingmuguanbi-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmic-off-full"></use>
                </svg>
                <div class="name">麦克风,静音</div>
                <div class="code-name">#iconmic-off-full</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquanping-01"></use>
                </svg>
                <div class="name">全屏-01</div>
                <div class="code-name">#iconquanping-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhaoyouliebiao"></use>
                </svg>
                <div class="name">好友列表</div>
                <div class="code-name">#iconhaoyouliebiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongongxiangpingmu"></use>
                </svg>
                <div class="name">共享屏幕</div>
                <div class="code-name">#icongongxiangpingmu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjingyin"></use>
                </svg>
                <div class="name">静音</div>
                <div class="code-name">#iconjingyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoxi-01"></use>
                </svg>
                <div class="name">消息-01</div>
                <div class="code-name">#iconxiaoxi-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhuiyiyaoqing"></use>
                </svg>
                <div class="name">会议邀请</div>
                <div class="code-name">#iconhuiyiyaoqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconluzhi-01"></use>
                </svg>
                <div class="name">录制-01</div>
                <div class="code-name">#iconluzhi-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconsousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#iconsousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongping"></use>
                </svg>
                <div class="name">中屏</div>
                <div class="code-name">#iconzhongping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondaping"></use>
                </svg>
                <div class="name">大屏</div>
                <div class="code-name">#icondaping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoping"></use>
                </svg>
                <div class="name">小屏</div>
                <div class="code-name">#iconxiaoping</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
