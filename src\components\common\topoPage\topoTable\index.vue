<template>
  <!-- 自定义列表 -->
  <div class="cust-table1 hf">
    <el-table
      :data="tableList"
      :header-cell-style="{
        color: '#73777A',
        'background-color': '#F0F2F5',
        'text-align': 'center',
      }"
      v-loading="loading"
      element-loading-text="拼命加载中..."
      class="table-part fs18 wf"
      @row-click="rowClick"
      :rowKey="(record) => record.index"
      :row-class-name="setRowClassName"
      :show-overflow-tooltip="true"
    >
      <!-- :rowKey="record => record.resourceName || record.svcTitle" -->
      <!-- 列渲染 -->
      <el-table-column
        v-for="(i, idx) in colConfigs"
        :key="'col' + idx"
        :prop="i.prop"
        :label="i.label"
        :align="i.align"
        :width="i.width"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <div>
            <span
              v-if="i.prop === 'num' && scope.row.num !== null"
              :style="
                Math.round(parseFloat(scope.row.num) * 1000) / 1000 >=
                custPercent
                  ? 'color: red;'
                  : ''
              "
            >
              <i
                class="el-icon-warning"
                v-if="
                  scope.row.num &&
                  Math.round(scope.row.num * 1000) / 1000 >= custPercent
                "
              ></i>
              <span class="ml10">{{
                scope.row.num
                  ? Math.round(parseFloat(scope.row.num) * 1000) / 1000 + `%`
                  : null
              }}</span>
              <!-- <span> {{ scope.row[i.prop] ? scope.row[i.prop] : "" }} </span> -->
            </span>
            <span
              v-else
              :style="
                scope.row.num &&
                Math.round(parseFloat(scope.row.num) * 1000) / 1000 >=
                  custPercent
                  ? 'color: red;'
                  : ''
              "
              >{{ scope.row[i.prop] ? scope.row[i.prop] : "" }}</span
            >
          </div>
        </template>
      </el-table-column>

      <div slot="empty">
        <i class="el-icon-warning fs16"></i>
        <span class="ml10 fs16">暂无数据！</span>
      </div>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: "topoTable",
    props: {
      list: {
        type: Array,
        default: () => [],
      },
      colConfigs: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: () => false,
      },
      selRow: {
        type: Object,
        default: () => {},
      },
      percent: {
        type: Number,
        default: () => null,
      },
      tabName: {
        type: String,
        default: () => "",
      },
      rKey: {
        type: String,
        default: () => "",
      },
    },
    data() {
      return {
        tableList: [],
        columns: [],
        resourceName: "",
      };
    },
    watch: {
      list: {
        handler(n, o) {
          n.map((i, idx) => {
            i.idx = idx + 1;
          });
          this.tableList = n;
        },
        deep: true,
      },
      colConfigs: {
        handler(n, o) {
          this.columns = n;
        },
        deep: true,
      },
      selRow: {
        handler(n, o) {
          this.$nextTick(() => {
            this.rowClick(n);
          });
        },
        deep: true,
      },
      percent(n) {
        this.custPercent = n;
      },
      tabName(n) {
        if (n) {
          this.resourceName = n;
        }
      },
      rKey(n) {},
    },
    mounted() {
      let list = this.list;
      list.map((i, idx) => {
        i.idx = idx + 1;
      });
      this.$nextTick(() => {
        this.tableList = list ? list : [];
        this.columns = this.colConfigs ? this.colConfigs : [];
        this.custPercent = this.percent ? this.percent : null;
      });
    },
    methods: {
      /**
       * <AUTHOR>
       * 点击列表回调
       */
      rowClick(row) {
        this.resourceName = row.resourceName;
        this.$emit("getSelectedRow", row.resourceName);
      },
      /**
       * <AUTHOR>
       * 设置选中行样式
       */
      setRowClassName({ row }) {
        if (this.resourceName === row.resourceName) {
          return "custRow";
        }
      },
    },
  };
</script>

<style scoped lang="less">
  .cust-table1 {
    ::v-deep {
      .el-table {
        thead {
          font-size: 14px;
          font-family: "PingFangSC-Regular, PingFang SC";
          color: #73777a;
        }
        .el-table__body-wrapper {
          overflow-y: scroll;
          overflow-x: hidden;
          position: relative;
          max-height: 220px;
          .el-table__row {
            background: #ffffff;
          }
          .el-table__body {
            tr {
              cursor: pointer;
              &:hover {
                .el-table__cell {
                  color: #606266;
                  background: #fff2ea;
                }
              }
              &.el-table__row {
                td:hover {
                  color: #373d41;
                  background: #fff2ea;
                }
                &.custRow {
                  td {
                    background: #06529d;
                    &:hover {
                      color: #ffffff;
                      background: #06529d;
                    }
                    span {
                      color: #ffffff !important;
                    }
                  }
                }
              }
            }
          }
        }
        .cell {
          font-weight: 500;
          font-size: 14px;
        }
        .el-table__empty-block {
          min-height: 200px;
        }
      }
      .el-loading-spinner {
        .el-loading-text,
        .path {
          color: #06529d;
          stroke: #06529d;
        }
      }
      .red {
        color: red;
      }
    }
  }
</style>
