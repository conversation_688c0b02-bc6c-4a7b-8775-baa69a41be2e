<template>
  <div style="height: 70%;">
      
      <el-table 
      v-loading="loading" 
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
        border 
        :data="tableBody" 
        :header-cell-style="{
				color: '#9ED8FF',
			  	'background-color': 'rgba(15, 52, 124, 1)',
			  	'text-align': 'center',
			  	'font-size': '14px',
			  	'border-right': '1px rgba(15, 0, 137, .8) solid'
			  }"
        height="150"
        style="border-radius: 4px 4px 0 0;
			  border:none;"
              @selection-change="handleSelectionChange">
              <!-- <el-table-column
        class-name="tabSel"
          type="selection"
          width="55"
          align="center">
        </el-table-column> -->
          <el-table-column 
          align='center'
          v-for="(i, idx) in tableColumns" 
          :key="idx" 
          :label="i.displayName" 
          :prop="i.columnName" 
          width="150">
          </el-table-column>
        </el-table>
        <!-- <el-pagination
            class="page-list"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageNumber"
            :page-sizes="[1,10, 20, 30, 40, 50]"
            :page-size="pageSize"
            background
            layout="total, prev, pager, next, sizes"
            :total.sync="total"
            style="float: right;"
        >
        </el-pagination> -->
        <div style="height: calc(50vh)">
            <panel-con/>
        </div>
  </div>
</template>

<script>
import panelCon from "./circuitTopo.vue";
export default {
  props: {
      tableBody: {
        type: Array, //要求传递的类型,大写开头
      },
    },
    watch:{
    },
    components:{panelCon},
data(){
    return {
      loading: false,
        visible: false,
        detailModal: false,
        form: {},
        formData: {},
        total: 10,
        pageSize: 10,
        pageNumber: 1,
        allCityData:  [],
        getCircuitRate: [],
        allCustUserRoles: [],
        roleList:[],
        custIdList: [],
        tableColumns: [
      {
        "displayName": "电路名称",
        "type": "string",
        "columnName": "circuitName"
      },
      {
        "displayName": "电路别名",
        "type": "string",
        "columnName": "alias"
      },
      {
        "displayName": "电路编号",
        "type": "string",
        "columnName": "circuitNo"
      },
      {
        "displayName": "电路类型",
        "type": "string",
        "columnName": "circuitType"
      },
      {
        "displayName": "电路状态",
        "type": "string",
        "columnName": "oprStateId"
      },
      {
        "displayName": "电路级别",
        "type": "string",
        "columnName": "circuitGrade"
      },
      {
        "displayName": "网络类型",
        "type": "string",
        "columnName": "netWo"
      },
      {
        "displayName": "业务类型",
        "type": "string",
        "columnName": "suffix"
      },
      {
        "displayName": "是否重保",
        "type": "string",
        "columnName": "isImportportect"
      },
      {
        "displayName": "电路速率",
        "type": "string",
        "columnName": "circuitRate"
      },
      {
        "displayName": "保护类型",
        "type": "string",
        "columnName": "protectMode"
      },
      {
        "displayName": "A端装机地址",
        "type": "string",
        "columnName": "aAddress"
      },
      {
        "displayName": "A端设备名称",
        "type": "string",
        "columnName": "aTrsNeId"
      },
      {
        "displayName": "A端端口名称",
        "type": "string",
        "columnName": "aPortId"
      },
      {
        "displayName": "Z端装机地址",
        "type": "string",
        "columnName": "zAddress"
      },
      {
        "displayName": "Z端设备名称",
        "type": "string",
        "columnName": "zTrsNeId"
      },
      {
        "displayName": "Z端端口名称",
        "type": "string",
        "columnName": "zPortId"
      },
      {
        "displayName": "A端业务设备端口",
        "type": "string",
        "columnName": "aBuzPort"
      },
      {
        "displayName": "Z端业务设备端口",
        "type": "string",
        "columnName": "zBuzPort"
      },
      {
        "displayName": "开通时间",
        "type": "string",
        "columnName": "openTime"
      },
      {
        "displayName": "调单编号",
        "type": "string",
        "columnName": "dispatchNo"
      },
    ],
        userInformation: [],
        customerAdded: [],
        noCustomerAdded: [],
        customerName: '',
        deleteCustomer: [],
        multipleSelection: []
    }
},
created() {
},
methods: {
  
    // 表格复选框
    handleSelectionChange(val) {
        this.multipleSelection = val;
        // console.log(this.multipleSelection);
      },
      custInfor(){
        this.$emit('equipFrom',this.multipleSelection[0])
      },
      getCust(){
          let options = {
            current: this.pageNumber,
            limit: this.pageSize,
            isQueryCount: true,
          }
      },
    getAllCounties(){
        this.$api.slaApi.getCircuitRate()
      .then(res=>{
          // console.log(res);
          this.getCircuitRate = res.data
      }) 
    },
    selectAllUserInfo(){
    //   this.loading = true;
    //     let scope = this;
    //     let queryParam = "";
    //   let options = {
    //     current: this.pageNumber,
    //     limit: this.pageSize,
    //     isQueryCount: true,
    //   }
    //     let arr = []
    //   Object.keys(this.form).forEach(key => {
    //    arr.push({
    //      type:'string',
    //      talcolumn:key,
    //      value:scope.form[key]
    //    })
    //   })
    //   let obj = arr.map((i,idx)=>{
    //     if (i.value) {
    //       if (i.type == 'enum') {
    //         return {[i.talcolumn]:{$eq:i.value}}
    //       } else {
    //         return {[i.talcolumn]:{$like:i.value}}
    //       }
          
    //     }
        
    //   })
    //   queryParam = this.transformData(obj).where
    //   if(queryParam){
    //     options.where=queryParam;
    //   }
    //   let params = {
    //     options: JSON.stringify(options),
    //   }
    //     this.$api.slaApi.getEqpPort(params)
    //   .then(res=>{
    //       this.tableBody = res.data;
    //       this.total = res.pagination.total;
    //       this.loading = false;
    //   })
    },

transformData(data) { 
  let transformedData = { where: {} }; 
  data.forEach(item => { 
    for (let key in item) { 
      let value = item[key]; // 假设统一使用$like作为条件 
        transformedData.where[key] = value; 
      } 
      }); 
      return transformedData; 
      } ,
    // 深拷贝
    extendCopy(p) {
      var c = {};
      for (var i in p) {
        c[i] = p[i];
      }
      c.uber = p;
      return c;
    },
    // 重置表单
    resetQuery(){
      this.form = {};
        this.pageSize = 10;
        this.pageNumber = 1;
        this.tableBody = [];
        this.total= 0;
        // this.selectAllUserInfo()
    },
    addUser(row){
      this.addCustomer()
      if (row.roleId) {
        this.formData = this.extendCopy(row);
      } else {
        this.formData = {};
      }
      this.visible = true;
    },
    subForm(formData) {
      if (this.formData.roleId) {
        this.$refs.formData.validate(valid => {
          if(valid) {
            this.subEditForm(formData);
          }
        });
      } else {
        this.$refs.formData.validate(valid => {
          if(valid) {
            this.subAddForm(formData);
          }
        });

      }
    },
    subAddForm(){
      this.$refs['formData'].validate((valid) => {
          if (valid) {
            console.log(this.formData);
             this.$api.slaApi.addRole(this.formData)
             .then(res=>{
               console.log(res);
               if (res.resultCode == 200) {
                 this.$message({
                   message: res.data,
                   type: 'success'
                 });
                 this.visible = false;
               } else {
                 this.$message.error('新增失败');
               }
               this.selectAllUserInfo()
             })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    subEditForm(){
      this.$refs['formData'].validate((valid) => {
          if (valid) {
             this.$api.slaApi.updateRole(this.formData)
             .then(res=>{
               if (res.resultCode == 200) {
                 this.$message({
                   message: res.data,
                   type: 'success'
                 });
                 this.visible = false;
               } else {
                 this.$message.error('修改失败');
               }
               this.selectAllUserInfo()
             })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    // 删除角色
    deleteRole(row){
        // deleteRole
        this.$api.slaApi.deleteRole({roleId: row.roleId} )
             .then(res=>{
                 if (res.resultCode == 200) {
                 this.$message({
                   message: res.data,
                   type: 'success'
                 });
                 this.visible = false;
               } else {
                 this.$message.error('删除失败');
               }
               this.selectAllUserInfo()
             })
    },
    resetForm(form) {
    //   this.$refs.formData.resetFields();
    this.formData.roleName = ''
    },
    closeModel() {
    //   this.dialogFormData = {};
      this.visible = false;
    },
    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.selectAllUserInfo();
    },
    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.selectAllUserInfo();
    },
    bindCustomer(row){
        this.detailModal = true;
        this.userInformation = row;
        this.customerAdded = row.custIdList;
        this.deleteCustomer = [];
        this.addCustomer()
    },
    addCustomer(){
      this.noCustomerAdded = [];
        let params = {
          options: JSON.stringify({"current":1,
          "limit":10,
          "isQueryCount":true,
          "where":{
              "custName":{
                  "$like":this.customerName
            }}})}
        this.$api.slaApi.query('pubCust',JSON.stringify(params))
        .then((result) => {
            // console.log(result.data);
            result.data.map(item => {
              this.noCustomerAdded.push({
                custId: item.custId, 
                custName: item.custName 
              })
            })
            // this.selectAllRoleInfo()
            // this.noCustomerAdded = result.data;
        })
    },
    updateCustAccount(){
      let newArr = [...this.customerAdded,...this.userInformation]
      var obj = {};
      newArr = newArr.reduce((current, next) => {
        obj[next.custId] ? "" : obj[next.custId] = true && current.push(next);
        return current;
      }, []);
      this.customerAdded = newArr;
    },
    // handleSelectionChange(val) {
    //   this.deleteCustomer.push(val)
    // },
    // handleSelectionChange2(val) {
    //   // console.log(val);
    //   let userInformation = []
    //   val.map(item => {
    //     userInformation.push({
    //       "userId": this.userInformation.id, 
    //       "custId": item.custId, 
    //       "custName": item.custName
    //     })
    //   })
    //   this.userInformation = userInformation
    // },
    // 删除客户
    deleteCustomers(){
      // console.log(this.customerAdded,this.deleteCustomer);
      this.customerAdded = this.customerAdded.filter(item => {
          return this.deleteCustomer[0].every(item2 => {
              return item.custId != item2.custId;
          })
      });
      // console.log(this.customerAdded);
    },
    // 保存客户信息
    saveCustInformation(){
      // console.log(this.customerAdded);
      let isNull = [{
        "userId": this.userInformation.id, 
          "custId": '', 
          "custName": ''
      }]
      this.$api.slaApi.updateCustAccount({accountCustList: this.customerAdded.length > 0 ? this.customerAdded : isNull})
      .then(res=>{
        console.log(res);
        if (res.resultCode == 200) {
          this.$message({
            message: res.data,
            type: 'success'
          });
          this.detailModal = false;
        } else {
          this.$message.error('绑定失败');
        }
        this.selectAllUserInfo()
      })
    },
    handleTemplateExport(){
      this.loading = true;
      let scope = this;
        let queryParam = "";
      let options = {
        // current: this.pageNumber,
        // limit: this.pageSize,
        isQueryCount: true,
      }
        let arr = []
      Object.keys(this.form).forEach(key => {
       arr.push({
         type:'string',
         talcolumn:key,
         value:scope.form[key]
       })
      })
      let obj = arr.map((i,idx)=>{
        if (i.value) {
          if (i.type == 'enum') {
            return {[i.talcolumn]:{$eq:i.value}}
          } else {
            return {[i.talcolumn]:{$like:i.value}}
          }
          
        }
        
      })
      queryParam = this.transformData(obj).where
      if(queryParam){
        options.where=queryParam;
      }
      let params = {
        options: JSON.stringify(options),
        showColums: JSON.stringify(this.tableColumns),
        type: "xlsx",
        isPage:"0"
      }
      this.$api.slaApi.exportCust(params)
          .then(res =>{
            // 文件下载
            const blob = new Blob([res], {
              // type: 'application/vnd.ms-excel' // 定义格式
              type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
            });
            let link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', 'oss3.0客户数据.xlsx');
            link.click();
            link = null;

            this.$message({
              message: '下载成功',
              type: 'success',
              duration: 2000,
              showClose: true
            });
            this.dialogExport = false;
            this.loading = false;
          })
          .catch(mgs =>{
            this.$message({
              message: '下载失败',
              type: 'error',
              duration: 2000,
              showClose: true
            });
            this.dialogExport = false;
            this.loading = false;
          });
    }
}

}
</script>

<style lang="less" scoped>
.nc-query {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
//   height: 10vh;
}
/deep/ .el-checkbox__inner {
    background-color: transparent;
    border: 1px solid #02dbff;
}
::v-deep {
  .el-table, .el-table__expanded-cell {
          background-color: transparent !important;
        }
        .el-table thead.is-group th.el-table__cell{
          background-color: transparent !important;
        }
//         /*定义滚动条轨道 内阴影+圆角*/
         .el-table__body-wrapper::-webkit-scrollbar-track {
          box-shadow: inset 0 0 0Px #163479 !important;
          border-radius: 10Px;
          background-color: #163479 !important;
        }
        .el-table tr {
			color: #fff;
          background-color: transparent;
          cursor: pointer;
        }
        .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
          background-color: #06529d !important;
          cursor: pointer;
        }
        .el-table--striped .el-table__body tr.el-table__row--striped:hover> td.el-table__cell{
          background-color: #06529d !important;
          cursor: pointer;
        }
        .el-table--striped {
          background-color: rgba(12,33,87,0)!important;
        }
        .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
          background: rgba(12,21,69,1);
        }
         .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
          width: 10Px;
        }
        
//         /*定义滑块 内阴影+圆角*/
         .el-table__body-wrapper::-webkit-scrollbar-thumb {
          border-radius: 10Px;
          box-shadow: inset 0 0 0Px #4C72C9;
          background: linear-gradient(-55deg, #4C72C9 0%, #4C72C9 100%);
        }
        .el-table__body-wrapper::-webkit-scrollbar {
          width: 8Px; // 横向滚动条
          height: 8Px; // 纵向滚动条 必写
        }
        .el-table--border::after, .el-table--group::after, .el-table::before{
          background-color:transparent;
        }
        .el-table--border th.el-table__cell.gutter{
          display: none;
        }
//         /* 表格鼠标悬浮时的样式（高亮） */
        .el-table--enable-row-hover .el-table__body tr:hover {
          background-color: rgba(255, 255, 255, 0);
        }
//         /*表格鼠标悬停的样式（背景颜色）*/
         .el-table tbody tr:hover > td {
          background-color: rgba(255, 255, 255, 0);
        }
        .el-table__body .el-table__row.hover-row td{
           background-color: rgba(255, 255, 255, 0);
        }
		.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
			// border-bottom: 1px solid rgba(15, 52, 124, .5);
      border: 1px solid transparent;
		}
		.el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
            // border-right: 1px solid rgba(15, 52, 124, .5);
            border: 1px solid transparent;
        }
        .el-table tr:nth-child(even) {
            background: rgba(40, 72, 121, 0.29);
        }
	}
  .addCustomer {
    height: 5%;
    width: 6%;
    float: left;
    position: relative;
    top: 45%;
    left: 4%;
    background: url("../../../assets/images/addCustomer.png") center center
      no-repeat;
    background-size: 100% 100%;
    transform: rotate(180deg);
  }
</style>