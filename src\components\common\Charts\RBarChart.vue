<template>
	<!-- 横向柱状图 -->
	<div
		ref="bar"
		class="bar"
		v-loading="uploading"
		element-loading-text="数据加载中"
		element-loading-background="rgba(0, 0, 0, 0)"
		:style="{ height, width }"
	/>
</template>
<script>
import echarts from "echarts";
export default {
	name: "rBar<PERSON><PERSON>",
	props: {
		initData: {
			type: Object,
			default: () => {},
		},
		barType: {
			type: Boolean,
			default: () => false,
		},
		tabType: {
			type: String,
			default: () => "",
		},
		selectCity: {
			type: String,
			default: () => "",
		},
    // 宽度
    width: {
      type: String,
      default: "100%"
    },
    // 高度
    height: {
      type: String,
      default: "28vh"
    },
	},
	data() {
		return {
			uploading: false,
			total: null,
		};
	},
	watch: {
		initData: {
			handler(n, o) {
				if (n) {
					this.barChartData = n;
					this.setMyEchart();
				}
			},
			deep: false,
		},
		barType(n) {
			// 控制 y 轴是否显示 TOP 排名
		},
		tabType(n) {
			// 控制 y 轴地图选中地市是否切换柱状图样式
		},
		selectCity(n) {
			if (n) {
				this.setMyEchart();
			}
		},
	},
	mounted() {
		self = this;
		this.$nextTick(() => {
			this.barChartData = this.initData;
			this.setMyEchart();
		});
	},
	methods: {
		setMyEchart(cityList, indexValue) {
			// this.uploading = true;
			const myChart = this.$refs.bar;
			let colors =
				this.barChartData.colors && this.barChartData.colors.length
					? this.barChartData.colors
					: ["#22C2DF","#22C2DF","#22C2DF","#22C2DF","#22C2DF","","",""];
			let xName =
				this.barChartData.xName && this.barChartData.xName.length
					? this.barChartData.xName
					: ["", "", "", "", "", "", "", ""];
			let xData =
				this.barChartData.xData && this.barChartData.xData.length
					? this.barChartData.xData
					: [0, 0, 0, 0, 0, 0, 0, 0];
			let yData =
				this.barChartData.yData && this.barChartData.yData.length
					? this.barChartData.yData
					: [ "TOP8", "TOP7", "TOP6", "TOP5", "TOP4", "TOP3", "TOP2", "TOP1"];
			let selectCity = this.selectCity ? this.selectCity : "";
			let maxNum = xData && Math.max(...xData) > 0 ? Math.max(...xData) : 50;
			let bgData = [];
			xData.forEach((i) => {
				bgData.push({ value: maxNum * 1.2, label: {show: false} });
			});
			if (myChart) {
				const barChart = echarts.init(myChart);
				const option = {
					tooltip:{
						show: true,
						trigger: "axis",
						triggerOn: 'mousemove|click',
						backgroundColor: "rgba(0, 10, 49, 0.8)",
						extraCssText: "box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
						formatter: (params) => {
							this.dataIdx = params[0].dataIndex;
							let barName = xName[params[0].dataIndex];
							let yName = params[0].name;
							let marker = params[0].marker;
							let val = params[0].value;
							if (this.barType === true && params[0].componentSubType == "pictorialBar") {
								let str = `<div>
                  <p v-if="yName" style="color:#02F6FF;">${yName ? yName : ""}</p>
                  <p><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px; background-color:#22C2DF;"></span> ${barName ? barName : ""}</p>
                  <p>${marker} 网元告警 ${val ? val : 0} 次</p>
                </div>`;
								return str;
							} else {
								let str = `<div>
                  <p v-if="yName">${yName ? yName : ""}</p>
                  <p>${marker} 网元告警 ${val ? val : 0} 次</p>
                </div>`;
								return str;
							}
						},
					},
					grid: {
						top: "10%",
						right: "5%",
						bottom: "5%",
						left: "5%",
						containLabel: true,
					},
					xAxis: {
						max: maxNum * 1.2,
						// max: xData.length - 1,
						type: "value",
						axisLabel: {
							show: false,
						},
						axisTick: {
							show: false,
						},
						axisLine: {
							show: false,
						},
						splitLine: {
							show: false,
						},
					},
					yAxis: [
						{
							type: "category",
							data: yData,
							axisTick: {
								show: false,
							},
							axisLabel: {
								show: true,
								textStyle: {
									color: "#00F6FF",
									fontSize: 10,
								},
							},
							axisLine: {
								show: false,
							},
							axisPointer: {
								type: "shadow",
								shadowStyle: {
									shadowColor: "rgba(255,255,255,0.9)",
								},
							},
							splitLine: {
								show: false,
							},
						},
						{
							name: "单位：次",
							nameTextStyle: {
								color: "#FFFFFF",
								fontSize: 12,
								padding: [0, 40, 0, 0],
							},
							nameGap: 5,
							nameLocation: "end",
							axisLine: {
								show: false,
							},
							axisLabel: {
								show: false,
							},
						},
					],
					series: [
						{
							// 内
							type: "pictorialBar",
							legendHoverLink: false,
							itemStyle: {
								color: function (params) {
									colors.splice(
										xData.length - 3,
										3,
										"#FFAF39", // 黄
										"#F6714E", // 橙
										"#FF4343" // 红
									);
									let color = colors[params.dataIndex];
									if (
										selectCity &&
										selectCity == params.name
									) {
										color =
											new echarts.graphic.LinearGradient(
												0,
												0,
												0,
												1,
												[
													{
														offset: 0,
														color: "#FF7E22",
													},
													{
														offset: 1,
														color: "#FE3B42",
													},
												]
											);
									}
									return color;
								},
							},
							label: {
								show: false,
								position: [10, 0],
								padding: [2, 0],
								fontSize: 12,
								color: "#fff",
								formatter: "{c}",
							},
							data: xData,
							z: 1,
							animationEasing: "elasticOut",
							symbolRepeat: "fixed",
							symbolMargin: 2,
							symbol: "rect",
							symbolClip: true,
							symbolSize: [5, 14],
							symbolPosition: "start",
							symbolOffset: [0, 1],
							data: xData,
							z: 1,
						},
						{
							// 背景样式
							name: "背景",
							type: "bar",
							barWidth: 18,
							itemStyle: {
								normal: {
									color: "rgba(28, 56, 83, 0.4)",
								},
							},
							data: bgData,
							z: 0,
						},
					],
				};

				setTimeout(() => {
					this.uploading = false;
				}, 500);
				barChart.setOption(option);
				window.addEventListener("resize", function () {
					barChart.resize();
				});
				barChart.on("mousemove", (params) => {
					barChart.getZr().setCursorStyle("pointer");
				});
			} else {
				setTimeout(() => {
					this.uploading = false;
				}, 500);
			}
		},
	},
};
</script>

<style lang='less' scoped>
.bar {
	width: 100%;
	height: 100%;
	// min-width: 300px;
	// min-height: 400px;
}
</style>
