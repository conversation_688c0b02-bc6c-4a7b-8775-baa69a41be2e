<template>
	<div
		ref="rdChart"
		class="rd-chart"
		v-loading="uploading"
		element-loading-text="数据加载中"
	/>
		<!-- element-loading-background="rgba(0, 0, 0, 0)" -->
</template>
<script>
import echarts from "echarts";
export default {
	name: "RadarChart",
	props: {
		availability: {
			type: Array,
			default: () => [],
		},
    // 宽度
    width: {
      type: String,
      default: "100%"
    },
    // 高度
    height: {
      type: String,
      default: "200px"
    },
	},
	data() {
		return {
			uploading: false,
		};
	},
	watch: {
		availability: {
			handler(n, v) {
				this.uploading = false;
				this.setMyEchart(n);
			},
			immediate: true,
			deep: true,
		},
	},
	mounted() {
		this.setMyEchart(this.availability);
	},
	methods: {
		setMyEchart(data) {
			const myChart = this.$refs.rdChart;
			let units = ["%", "ms", "%", "", ""];
			let indicator = [
				{ text: "流入带宽利用率" },
				{ text: "时延" },
				{ text: "流出带宽利用率" },
				{ text: "接包数" },
				{ text: "发包数" },
			];
			let datas = data.map((i, idx) => ({
				text: indicator[idx].text,
				value: data[idx] !== "" ? data[idx] + units[idx] : "",
			}));
			if (myChart) {
				const radarChart = echarts.init(myChart);
				const option = {
          color: '#FFD34D',
					tooltip: {
						trigger: "item",
						backgroundColor: "rgba(26, 47, 108, 0.5)",
						textStyle: {
							color: "#fff",
						},
						borderWidth: 1,
						borderColor: "#1A2F6C",
						formatter: (params) => {
							let htmlStr = "";
							for (let i = 0; i < datas.length; i++) {
								let temp = `<div>
                  <p><span>${params.marker}</span>${datas[i].text}:&nbsp;&nbsp;${datas[i].value}</p>
                </div>`;
								htmlStr += temp;
							}
							return htmlStr;
						},
					},
					radar: [
						{
							indicator: indicator,
							center: ["50%", "55%"],
							radius: ["0%", "70%"],
							name: {
								textStyle: {
									fontSize: 10,
									color: "#E4EFFF",
								},
							},
							nameGap: 8,
							splitNumber: 4,
							splitArea: {
								show: false,
							},
							splitLine: {
								lineStyle: {
									color: "#324892"
								},
							},
							axisLine: {
								lineStyle: {
									color: '#324892',
								},
							},
              axisName: {
                color: '#FFFFFF'
              }
						},
					],
					series: [
						{
							type: "radar",
							symbol: "none",
							emphasis: {
								lineStyle: {
									width: 2,
								},
							},
							lineStyle: {
								width: 2,
                color: 'rgba(242, 190, 2, 1)'
							},
							data: [
								{
									value: data,
									name: "指标",
									areaStyle: {
										color: {
											type: "radial",
											r: 1,
											colorStops: [
												{
													offset: 0,
													color: "rgba(242, 190, 2, 0.6)", // 0% 处的颜色
												},
												{
													offset: 0.6,
													color: "rgba(242, 190, 2, 0.15)", // 100% 处的颜色
												},
												{
													offset: 1,
													color: "rgba(242, 190, 2, 0)", // 100% 处的颜色
												},
											],
										},
									},
								},
							],
						},
					],
				};
				this.uploading = false;
				radarChart.setOption(option);
				window.addEventListener("resize", function () {
					radarChart.resize();
				});
			}
		},
	},
};
</script>

<style lang='less' scoped>
.rd-chart {
	width: 100%;
	height: 100%;
	display: flex;
  justify-content: center;
}
</style>
