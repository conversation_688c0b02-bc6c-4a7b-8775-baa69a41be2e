import Router from "vue-router";

//解决vue-router3.0以上跳转报错问题
// const originalPush = Router.prototype.push;
// Router.prototype.push = function push(location) {
//   return originalPush.call(this, location).catch((err) => err);
// };

let routerList = [
  {
    path: "/",
    redirect: "/login",
    meta: {
      hidden: true,
    },
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login.vue"),
    meta: {
      title: "登录",
      hidden: true,
    },
  },
  {
    path: "/homePage",
    name: "homePage",
    component: () => import("@/views/homePage.vue"),
    meta: { title: "重保大屏", icon: "el-icon-search", hidden: false },
    children: [
      {
        path: "/systemManagement",
        name: "systemManagement",
        meta: { title: "系统管理" },
        component: () => import("@/components/systemManagement/index.vue"),
      },
      {
        path: "/topoPage",
        name: "topoPage",
        meta: { title: "业务拓扑" },
        component: () => import("@/components/topoPage/ResourceTopologyTop"),
      },
      {
        path: "/resource",
        name: "resource",
        meta: { title: "资源管理" },
        component: () => import("@/components/resource"),
      },
      {
        path: "/AlarmMonitoring",
        name: "AlarmMonitoring",
        meta: { title: "告警监控" },
        component: () => import("@/components/AlarmMonitoring"),
      },
      {
        path: "/bigPictureCircuitName",
        name: "bigPictureCircuitName",
        meta: { title: "全局拓扑" },
        component: () => import("@/components/bigPictureCircuitName/index"),
      },
    ],
  },

  {
    path: "/error",
    name: "error",
    component: () => import("@/views/error.vue"),
  },
  {
    path: "/gis",
    name: "gis",
    component: () => import("@/views/gisMap/index.vue"),
  }
];

const router = new Router({
  mode: "hash",
  fallback: false,
  routes: routerList,
});

//路由守卫
router.beforeEach((to, from, next) => {
  // 忽略 "NavigationDuplicated" 警告
  // console.log(to, from);
  let token = localStorage.getItem("nmgjpwToken");
  if (to.name !== "login" && !token) {
    next({ name: "login" });
  } else {
    next();
  }
});

export default router;

export { routerList };
