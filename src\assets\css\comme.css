* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body,
html {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

.container {
    width: 100%;
    height: 100%;
}

.pagecontainer {
    width: 100%;
    height: 100%;
    position: relative;
    box-sizing: border-box;
}

.borderfls.dv-border-box-8 {
    position: relative;
    padding: 8px;
    box-sizing: border-box;
}
/* 大屏数字字体 */

@font-face {
    font-family: 'DigifaceWide';
    src: url('./fonts/digifaw.ttf');
}


/* 大屏标题字体 */

@font-face {
    font-family: 'MFBanHei';
    src: url('./fonts/MFBanHei.ttf');
}


/* 大屏字体 */

@font-face {
    font-family: 'YouSheBiaoTiHei';
    src: url('./fonts/YouSheBiaoTiHei.ttf');
}

.numfont {
    font-family: DigifaceWide;
}

.titlefont {
    font-family: MFBanHei;
}

.infofont {
    font-family: YouSheBiaoTiHei;
}

ul,
li {
    list-style: none;
}

.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

.pull-left {
    float: left;
}

.pull-right {
    float: right;
}

.bcolor {
    color: #19A4FF;
    font-weight: bold;
}

.gcolor {
    color: #48DE90;
    font-weight: bold;
}

.rcolor {
    color: #FE5F8D;
}


/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/

::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    background-color: #060a1b79;
}


/*定义滚动条轨道 内阴影+圆角*/

::-webkit-scrollbar-track {
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
    border-radius: 0;
    background-color: #F0F2F5;
}


/*定义滑块 内阴影+圆角*/

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 3px rgba(218, 205, 205, 0.1);
    -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, .1);
    /* background-color: #8babe2; */
    background-color: #D9D9D9;
    cursor: grab;
}

/*申明弹性布局*/
.flex{
    display:flex;
}
/*交叉轴上居中*/
.acenter{
    align-items: center;
}
/*在主轴上居中*/
.jcenter{
    justify-content: center;
}
.jbetween{
    justify-content: space-between;
}
/*正居中*/
.jacenter{
    justify-content: center;
    align-items: center;
}
/*x轴水平布局*/
.flexRow{
    flex-direction: row;
}
/*y轴垂直布局*/
.flexColumn{
    flex-direction:column
}
/*一行过多进行换行*/
.flexWrap{
    flex-wrap: wrap;
}
/*常见弹性正居中*/
.flexCenter{
    display: flex;
    justify-content: center;
    align-items: center;
}
/*主轴居中，交叉轴方向靠下布局*/
.justEnd{
    display: flex;
    align-items: flex-end;
    justify-content: center;
}
/*主轴靠结束位置，交叉轴居中*/
.alignEnd{
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.flexEnd{
    justify-content: flex-end;
}
/*主轴水平居中*/
.justCenter{
    display: flex;
    justify-content: center;
}
/*交叉轴居中，主轴两边对齐*/
.justBetween{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.justAround{
    display: flex;
    align-items: center;
    justify-content: space-around;
}
/*交叉轴居中*/
.alignCenter{
    display: flex;
    align-items: center;
}
/*y轴方向布局，正居中*/
.columnCenter{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction:column
}