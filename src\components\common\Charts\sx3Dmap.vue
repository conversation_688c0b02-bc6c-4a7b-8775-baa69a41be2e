<template>
  <div class="sx-map">
    <!-- <div style="margin-left: 10px" class="ano">
      <el-button  plain type="primary" size="mini" @click="goBack" class="export" v-show="mapName !== '陕西省'">返回</el-button>
      <el-button  plain type="primary" size="mini" @click="changeMapInterval('1')" class="export">陕西省</el-button>
      <el-button  plain type="primary" size="mini" @click="changeMapInterval('2')" class="export">暂停轮播</el-button>
      <el-button  plain type="primary" size="mini" @click="changeMapInterval('3')" class="export">开始轮播</el-button>
    </div> -->
    <div id="map3D" ref="map3D" style="width: 100%; height: 100%" />
  </div>
</template>

<script>
import { nextTick } from 'vue';
import * as THREE from "three";  //导入threeJs
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { CSS3DRenderer,CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer.js';
// 引入渲染器通道RenderPass
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
// 引入OutlinePass通道
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass'
import { CopyShader } from 'three/examples/jsm/shaders/CopyShader'
import { BloomPass } from 'three/examples/jsm/postprocessing/BloomPass'

import { ClearMaskPass, MaskPass } from 'three/examples/jsm/postprocessing/MaskPass'

import * as d3 from "d3";
import {changePosition, changeCity, cylinderCity} from "@/utils/repairCity";
import sxJson from "@/assets/sxMap/map/shanxi_full.json";  // 陕西省
import ank from "@/assets/sxMap/map/ank.json";  // 安康市
import bj from "@/assets/sxMap/map/bj.json";  // 宝鸡市
import hz from "@/assets/sxMap/map/hz.json";  // 汉中市
import sl from "@/assets/sxMap/map/sl.json";  // 商洛市
import tc from "@/assets/sxMap/map/tc.json";  // 铜川市
import wn from "@/assets/sxMap/map/wn.json";  // 渭南市
import xa from "@/assets/sxMap/map/xa.json";  // 西安市
import xy from "@/assets/sxMap/map/xy.json";  // 咸阳市
import ya from "@/assets/sxMap/map/ya.json";  // 延安市
import yl from "@/assets/sxMap/map/yl.json";  // 榆林市

export default {
  name: "Sx3DMap",
  data(){
    return {
      camera:null,
      scene:null,
      bloomComposer:null,
      renderer:null,
      controls:null,
      previousObj : {material: [{color: new THREE.Color(0x0000ff)}]},
      group : new THREE.Group(),
      groupTag : new THREE.Group(),
      pillarGroup : new THREE.Group(),
      pillarGroup2: new THREE.Group(),
      cylinderMeshGroup: new THREE.Group(),
      cylinderMesh3Group: new THREE.Group(),
      cylinderMeshArr:[],
      lineGroup : new THREE.Group(),
      labelRenderer:null,
      clock : new THREE.Clock(),
      mouse : new THREE.Vector2(),
      raycaster : new THREE.Raycaster(),
      groupSprite : new THREE.Group(),
      cylinderRadius : 0,
      cylinderOpacity : 1,
      mapName: '陕西省',
      sxJson : sxJson,
      mapCenter : [],
      mapJson : {},
      cityMap: {
        安康市: ank,
        宝鸡市: bj,
        汉中市: hz,
        商洛市: sl,
        铜川市: tc,
        渭南市: wn,
        西安市: xa,
        咸阳市: xy,
        延安市: ya,
        榆林市: yl,
      },
      mapNameCity:[
        "安康市",
        "宝鸡市",
        "汉中市",
        "商洛市",
        "铜川市",
        "渭南市",
        "西安市",
        "咸阳市",
        "延安市",
        "榆林市",
      ],
      rangeChange: -0.5,
      rangeChangeShow : false,
      scaleBlowUp : 450,
      scaleBlowUp2 : 450,
      topMap : '60px',
      leftMap : '65px',
      curLocationType: '1',  //1陕西省，2地市，3区县
      countCity:0,
      countCity2:0,
      countCityShow : false,
      downCityShow :true,
    }
  },
  async mounted() {
    await this.initMap3D();
    this.regionsCityIntervalFunc();
  },

  methods: {
    changeMapInterval(param) {
      let _this = this;
      if (param == "1") {
        _this.goBack();
        _this.countCity2 = 0;
        _this.curLocationType = '1';
        _this.$emit("curLocationType", '陕西省', '1');
        _this.group.children[_this.countCity-1].material[0].color.set("#058dbd");
        _this.group.children[_this.countCity-1].position.z=0;
        _this.clearIntervalFunc();
      }else if (param == "2") {
        if(_this.countCity2 >0 ){
          _this.countCity2 = _this.countCity-1;
        }else {
          _this.countCity2 = _this.countCity;
        }
        _this.clearIntervalFunc();
      }else if (param == "3") {
        _this.countCity = _this.countCity2;
        if( _this.curLocationType == '3' ){
          _this.downCityShow = false;
        }else if(_this.curLocationType == '2'|| _this.curLocationType == '1' ){
          _this.downCityShow = true;
        }
        _this.regionsCityIntervalFunc();
      }
    },
    regionsCityIntervalFunc() {
      let _this = this;
      _this.regionsCityIntervalContent();
      _this.regionsCity = setInterval(()=>{
        _this.regionsCityIntervalContent();
      }, 10000);
    },
    regionsCityIntervalContent() {
      let _this = this;
      if (_this.group.children.includes(_this.group.children[_this.countCity])) {
        _this.group.children[_this.countCity].material[0].color.set("#17efdd");
        _this.group.children[_this.countCity].position.z=1;
        if(_this.countCity >0 && !_this.countCityShow){
          _this.group.children[_this.countCity-1].material[0].color.set("#058dbd");
          _this.group.children[_this.countCity-1].position.z=0;
        }else if(_this.countCity === 0 && _this.countCityShow){
          _this.group.children[_this.group.children.length-1].material[0].color.set("#058dbd");
          _this.group.children[_this.group.children.length-1].position.z=0;
          _this.countCityShow = false;
        }
        _this.countCity++;
        /*       _this.curLocationType = '2',*/
        if(!this.downCityShow){
          _this.curLocationType = '3';
          _this.$emit("curLocationType",_this.group.children[_this.countCity-1].name, '3');
        }else {
          _this.curLocationType = '2';
          _this.$emit("curLocationType", _this.group.children[_this.countCity-1].name, '2');
        }
        _this.curIntervalName = _this.group.children[_this.countCity-1].name;
        if (_this.countCity === _this.group.children.length) {
          _this.countCity = 0;
          _this.countCityShow = true;
        }
      }
    },
    unique(arr,val) {
      const res = new Map();
      return arr.filter(item => !res.has(item[val]) && res.set(item[val], 1))
    },
    goBack() {
      let _this = this;
      /*      _this.$emit("curLocationType", _this.mapName, '2');*/
      _this.mapName = "陕西省";
      //先全部置空
      _this.removeAllGroup();
      _this.mapJson = _this.sxJson;
      _this.mapCenter = [109.032231, 35.412112];
      _this.scaleBlowUp = 450;
      _this.scaleBlowUp2 = 450;
      _this.topMap = '60px';
      _this.leftMap = '65px';
      _this.initCSS3D();
      //加载地图
      let mapCenter;
      let mapJson;
      if (this.mapName === "陕西省") {
        mapCenter = [109.032231, 35.412112];
        mapJson = _this.sxJson;
      }
      _this.camera.fov = 179;
      _this.camera.updateProjectionMatrix()
      _this.initMap(mapCenter, mapJson);
      _this.group.children = this.unique(this.group.children,'name');
      _this.group.scale.y = 1.2;
      _this.scene.add(this.group);
      _this.lineGroup.scale.y = 1.2;
      _this.outlinePass(this.group);
      _this.scene.add(this.lineGroup);
      _this.clearIntervalFunc();
      _this.downCityShow = true;
      _this.regionsCityIntervalFunc();
    },
    //定时器重置
    clearIntervalFunc(){
      this.countCity = 0;
      this.countCityShow = false;
      clearInterval(this.regionsCity);
      this.regionsCity = null;
      this.downCityShow = true;
    },
    initMap3D() {
      this.container = document.getElementById('map3D');
      this.innerWidth = this.container.offsetWidth;
      this.innerHeight = this.container.offsetHeight;
      // 渲染器
      this.renderer = new THREE.WebGLRenderer({antialias: true, alpha: true});   //参数是去掉背景相机黑色
      this.container.appendChild(this.renderer.domElement);
      //加载css3d渲染器
      this.initCSS3D();

      this.scene = new THREE.Scene()
      this.camera = new THREE.PerspectiveCamera(179, this.innerWidth * 0.87 / this.innerHeight * 0.905, 0.6, 1500);
      this.renderer.setSize(this.innerWidth, this.innerHeight);
      this.renderer.logarithmicDepthBuffer = true;
      this.camera.position.set(5, -53, 80);
      this.camera.lookAt(this.scene.position);
      this.renderer.outputEncoding = THREE.sRGBEncoding;
      this.renderer.setPixelRatio(window.devicePixelRatio);
      this.controls = new OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.enablePan = true;
      let curTime = new Date().getHours();
      if (curTime >= 18 || curTime < 8) {
        this.lightY = -20;
      } else {
        /*        this.lightY=-60;*/
        this.lightY = -20;
      }
      this.setLight(23, this.lightY, 50);
      this.regionsLightYFunc()

      //开启坐标轴
      /*      const axesHepler = new THREE.AxesHelper(500);  //x(红)y(绿)z(蓝)
      this.scene.add(axesHepler);*/

      // 创建后处理对象EffectComposer，WebGL渲染器作为参数
      this.bloomComposer = new EffectComposer(this.renderer)
      this.animate(this.container);
      //加载地图
      let mapCenter;
      let mapJson;
      if (this.mapName === "陕西省") {
        mapCenter = [109.032231, 35.412112];
        mapJson = this.sxJson;
      }
      this.initMap(mapCenter, mapJson);
      this.group.children = this.unique(this.group.children,'name');
      this.group.scale.y = 1.2;
      this.scene.add(this.group);
      this.lineGroup.scale.y = 1.2;
      this.scene.add(this.lineGroup);
      this.outlinePass(this.group);
      let _this = this;
      window.addEventListener('resize', function () {
        _this.container = document.getElementById('map3D');
        _this.camera.aspect=_this.container.offsetWidth * 0.87 / _this.container.offsetHeight* 0.905;
        _this.camera.updateProjectionMatrix();
        /*        _this.renderer.setSize(_this.container.offsetWidth, _this.container.offsetHeight);
                _this.labelRenderer.setSize(_this.container.offsetWidth, _this.container.offsetHeight);*/
        _this.topMap = '60px',
            _this.leftMap = '65px',
            _this.initCSS3D();
      });
      nextTick(() => {
        this.container.addEventListener('mousemove', this.handleMousemove, false);
        this.container.addEventListener('click', this.handleClick, false);
      });
    },
    initCSS3D(){
      this.labelRenderer = new CSS3DRenderer();
      this.labelRenderer.setSize(this.innerWidth* 0.87, this.innerHeight* 0.905);
      this.labelRenderer.domElement.style.position = 'absolute';
      this.labelRenderer.domElement.style.top = this.topMap;
      this.labelRenderer.domElement.style.left = this.leftMap;
      this.labelRenderer.domElement.style.pointerEvents = 'none';
      this.container.appendChild(this.labelRenderer.domElement);  //加载再同一个标签下
    },
    initMap(mapCenter, mapJson) {
      let map = new THREE.Object3D();
      const projection = d3.geoMercator().center(mapCenter).scale(this.scaleBlowUp).translate([5, -6]);
      const projection2 = d3.geoMercator().center(mapCenter).scale(this.scaleBlowUp2).translate([5, 8]);
      mapJson.features.forEach(elem => {
        const province = new THREE.Object3D();
        const coordinates = elem.geometry.coordinates;
        coordinates.forEach(multiPolygon => {
          multiPolygon.forEach(polygon => {
            const shape = new THREE.Shape();
            const lineMaterial = new THREE.LineBasicMaterial({
              color: "#e0e0e0",
              linewidth: 10,
            });
            const lineGeometry = new THREE.BufferGeometry();
            const pointsArray = new Array();
            for (let i = 0; i < polygon.length; i++) {
              const [x, y] = projection(polygon[i]);
              if (i === 0) {
                shape.moveTo(x, -y);
              }
              shape.lineTo(x, -y);
              pointsArray.push(new THREE.Vector3(x, -y, 3.01));
            }
            lineGeometry.setFromPoints(pointsArray);
            const extrudeSettings = {
              depth: 3.0,
              bevelEnabled: false
            };
            let texture = this.loadFile('./image/gz-map2.png');
            texture.wrapS = texture.wrapS = THREE.RepeatWrapping;
            texture.wrapT = texture.wrapT = THREE.RepeatWrapping;
            texture.flipY = texture.flipY = false;
            texture.rotation = texture.rotation = THREE.MathUtils.degToRad(45);
            const scale = 0.05;
            texture.repeat.set(scale, scale);
            const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
            const material = new THREE.MeshPhongMaterial({
              transparent: true,
              color: "#058dbd",
              opacity: 0.99,
              specularMap: texture,
              shininess: 20,
              specular: "#ffffff"
            });
            const material1 = new THREE.MeshPhongMaterial({
              transparent: false,
              /*     color: "#068bf8",*/
              emissive: "#068bf8",
            });
            let mesh = new THREE.Mesh(geometry, [material, material1]);
            let line = new THREE.Line(lineGeometry, lineMaterial);
            mesh.name = elem.properties.name;
            line.name = elem.properties.name;
            this.group.add(mesh);
            this.lineGroup.add(line);
          });
        });
        province.properties = elem.properties;
        if (elem.properties.contorid) {
          const [x, y] = projection(elem.properties.contorid);
          province.properties._centroid = [x, y];
        }
        map.add(province);
        let name = province.properties.name;
        if (name) {
          let center = province.properties.center;
          let tag3D = this.tag(name);
          let centerChannge = changePosition(center, name);
          let position = projection2(centerChannge);
          tag3D.position.set(position[0], -position[1], 0.01);
          tag3D.scale.set(0.1, 0.1, 0.1);//根据相机渲染范围适当缩放
          this.groupTag.add(tag3D);
          let position2 = projection(centerChannge);
          this.ConeMesh(1.0, position2[0], -position2[1], name); //加柱子
        }
      });
      this.scene.add(map);
    },
    setLight(x, y, z) {
      let ambientLight = new THREE.AmbientLight("#ffffff", 0.2) // 环境光
      const light = new THREE.DirectionalLight("#ffffff", 0.5) // 平行光
      light.position.set(5, -53, 80)
      light.castShadow = true
      light.shadow.mapSize.width = this.innerWidth;
      light.shadow.mapSize.height = this.innerHeight;
      // 半球光  (5,-53,80)
      let hemiLight = new THREE.HemisphereLight('#a5cff5', '#7eacf5', 0.1)
      // 这个也是默认位置
      hemiLight.position.set(5, -23, 80)
      this.scene.add(hemiLight)

      const pointLight2 = new THREE.PointLight("#ffffff", 0.3)
      pointLight2.position.set(x, y, z)   //改变值，进行关照迁移
      pointLight2.castShadow = true
      pointLight2.shadow.mapSize.width = 1024
      pointLight2.shadow.mapSize.height = 1024
      this.scene.add(ambientLight)
      this.scene.add(light)
      this.scene.add(pointLight2)
    },
    ConeMesh(size, x, y, name) {
      let length = 8;
      let length2 = 6;
      const geometry = new THREE.CapsuleGeometry(0.8, length, 4, 8);
      const geometry2 = new THREE.CapsuleGeometry(0.8, length2, 4, 8);
      let texture = this.loadFile('./image/柱子.png');
      /*      texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
            texture.repeat.set(1, 1);
            texture.needsUpdate = true;*/
      const material = new THREE.MeshLambertMaterial({
        color: "#00ff34",
        transparent: true,
        // 前面 FrontSide  背面：BackSide 双面：DoubleSide
        side: THREE.FrontSide,
        opacity: 0.9
      });
      const material2 = new THREE.MeshLambertMaterial({
        color: "#ff0202",
        transparent: true,
        side: THREE.FrontSide,
        opacity: 0.9
      });
      const boxMesh = new THREE.Mesh(geometry, material);
      if (name === "榆林市" || name === "神木市" || name === "定边县" || name === "靖边县" ) {
        const boxMesh2 = new THREE.Mesh(geometry2, material2);
        boxMesh2.rotation.set(-Math.PI / 2, 0, Math.PI);
        boxMesh2.lookAt(0, 90, 10)
        boxMesh2.position.set(x - 1.8, y, length2 - length2 / 2);
        this.pillarGroup2.add(boxMesh2);
        this.cityPointMesh2(x, y)
        this.cityPointMesh3(x, y);
      }
      boxMesh.rotateX(Math.PI / 2)
      boxMesh.lookAt(0, 90, 10)
      boxMesh.position.set(x, y, length - length / 2);
      this.pillarGroup.add(boxMesh)
    },
    cityPointMesh2(x, y) {
      const geometry = new THREE.PlaneGeometry(10, 10);
      let texture = this.loadFile('./image/rippleStatic2.png');
      texture.wrapS = texture.wrapT = THREE.RepeatWrapping;
      texture.repeat.set(1, 1);
      /*    texture.needsUpdate = true;*/
      let cityWaveMaterial = new THREE.MeshBasicMaterial({
        color: "#ff0202",
        map: texture,
        side: THREE.DoubleSide,
        transparent: true, //使用背景透明的png贴图，注意开启透明计算
        opacity: 1.0,
        depthWrite: false, //禁止写入深度缓冲区数据
      });
      let mesh = new THREE.Mesh(geometry, cityWaveMaterial);
      /*      mesh.rotation.set(-Math.PI / 2, 0, Math.PI);*/
      mesh.position.set(x - 1.8, y, 3.01);
      mesh.size = 0.4;
      mesh.scale.set(mesh.size, mesh.size, mesh.size);//根据相机渲染范围适当缩放
      mesh._s = Math.random() * 1.0 + 1.0;
      this.mesh = mesh;
      this.cylinderMeshGroup.add(mesh);
      this.cylinderMeshArr.push(mesh);
    },
    cityPointMesh3(x, y) {
      let geometry = new THREE.CylinderGeometry(0, 1, 1, 240);
      let texture = this.loadFile('./image/圆柱.png');
      texture.wrapS = texture.wrapT = THREE.RepeatWrapping; //每个都重复
      texture.repeat.set(1, 1);
      /*   texture.needsUpdate = true;*/
      let materials = [
        //圆柱侧面材质，使用纹理贴图
        new THREE.MeshBasicMaterial({
          map: texture,
          color: "#ff0202",
          opacity: 0.9,
          transparent: true
        }),
        //圆柱顶材质
        new THREE.MeshBasicMaterial({
          transparent: true,
          opacity: 0.8,
          depthWrite: false, //禁止写入深度缓冲区数据
          side: THREE.DoubleSide
        }),
        //圆柱底材质
        new THREE.MeshBasicMaterial({
          transparent: true,
          opacity: 0.8,
          depthWrite: false, //禁止写入深度缓冲区数据
          side: THREE.DoubleSide
        })
      ];
      let cylinderMesh = new THREE.Mesh(geometry, materials);
      cylinderMesh.rotation.set(-Math.PI / 2, 0, Math.PI);
      cylinderMesh.position.set(x - 1.8, y, 3.01);
      this.cylinderMesh3Group.add(cylinderMesh);
    },
    cylinderAnimate2() {
      let _this = this;
      _this.cylinderMeshArr.forEach(function (mesh) {
        mesh._s += 0.005;
        mesh.scale.set(
            mesh.size * mesh._s,
            mesh.size * mesh._s,
            mesh.size * mesh._s
        );
        if (mesh._s <= 1.5) {
          mesh.material.opacity = (mesh._s - 1) * 2; //2等于1/(1.5-1.0)，保证透明度在0~1之间变化
        } else if (mesh._s > 1.5 && mesh._s <= 2) {
          mesh.material.opacity = 1 - (mesh._s - 1.5) * 2; //2等于1/(2.0-1.5) mesh缩放2倍对应0 缩放1.5被对应1
        } else {
          mesh._s = 1.0;
        }
      });
    },
    tag(name) {
      let div = document.createElement('div');
      div.innerHTML = name;
      div.style.color = '#ffffff';
      div.style.fontSize = '14px';
      div.style.position = 'absolute';
      div.style.borderRadius = '5px';
      let label = new CSS3DObject(div);
      div.style.pointerEvents = 'none';
      return label;
    },
    animate() {
      this.cylinderAnimate2();//光圈动画
      if (this.camera.fov > 50) {
        /*        this.camera.fov -=0.32;*/
        this.camera.fov += this.rangeChange;
        this.camera.updateProjectionMatrix()
      } else {
        this.groupTag.lookAt(1, -3, 90)
        this.scene.add(this.groupTag);
        this.scene.add(this.pillarGroup);
        this.scene.add(this.pillarGroup2);
        this.scene.add(this.cylinderMeshGroup);
        this.scene.add(this.cylinderMesh3Group);
        this.scene.add(this.cylinderMeshGroup);
      }
      this.bloomComposer.render(this.scene, this.camera)
      requestAnimationFrame(this.animate.bind(this));
      this.raycaster.setFromCamera(this.mouse, this.camera);
      /*      this.renderer.render(this.scene, this.camera);*/
      this.labelRenderer.render(this.scene, this.camera);
    },
    //正常边界发光
    outlinePass(meshGroup){
      // RenderPass这个通道会渲染场景，但不会将渲染结果输出到屏幕上
      // 创建一个渲染器通道，场景和相机作为参数
      const renderPass = new RenderPass(this.scene, this.camera)

      // OutlinePass第一个参数v2的尺寸和canvas画布保持一致
      const v2 = new THREE.Vector2(this.innerWidth, this.innerHeight);
      const outlinePass = new OutlinePass(v2, this.scene, this.camera);
      outlinePass.visibleEdgeColor.set("#0173b0")
      // 一个模型对象
      outlinePass.selectedObjects=[meshGroup];
      outlinePass.edgeStrength = 5 //粗
      outlinePass.edgeGlow = 1 //发光
      outlinePass.edgeThickness = 1 //光晕粗
     /*       outlinePass.pulsePeriod = 1 //闪烁*/
      /*      outlinePass.usePatternTexture = false //是否使用贴图*/
      outlinePass.hiddenEdgeColor.set('white'); // 设置隐藏的颜色

      //创建效果组合器对象，可以在该对象上添加后期处理通道，通过配置该对象，使它可以渲染我们的场景，并应用额外的后期处理步骤，在render循环中，使用EffectComposer渲染场景、应用通道，并输出结果。
      this.bloomComposer.setSize(this.innerWidth, this.innerHeight);
      this.bloomComposer.addPass(renderPass);
      // 眩光通道bloomPass插入到composer
      this.bloomComposer.addPass(outlinePass)
    },
    //告警边界发光
    outlinePassAlarm(boxMesh2,name){
      if (name === "榆林市" || name === "神木市" || name === "定边县" || name === "靖边县" ) {
        let alarmArr = [];
        alarmArr=this.lineGroup.children.filter(item => item.name === name);
        if(alarmArr===[])return;
        const renderPass = new RenderPass(this.scene, this.camera)
        // OutlinePass第一个参数v2的尺寸和canvas画布保持一致
        const v2 = new THREE.Vector2(this.innerWidth, this.innerHeight);
        const outlinePass = new OutlinePass(v2, this.scene, this.camera);
        outlinePass.visibleEdgeColor.set("#da3d28")
        // 一个模型对象
        outlinePass.selectedObjects=[boxMesh2];
        outlinePass.edgeStrength = 5 //粗
        outlinePass.edgeGlow = 1 //发光
        outlinePass.edgeThickness = 1 //光晕粗
        outlinePass.pulsePeriod = 1 //闪烁
        /*      outlinePass.usePatternTexture = false //是否使用贴图*/
        outlinePass.hiddenEdgeColor.set('#da3d28'); // 设置隐藏的颜色

        //创建效果组合器对象，可以在该对象上添加后期处理通道，通过配置该对象，使它可以渲染我们的场景，并应用额外的后期处理步骤，在render循环中，使用EffectComposer渲染场景、应用通道，并输出结果。
        this.bloomComposer.setSize(this.innerWidth, this.innerHeight);
        this. bloomComposer.addPass(renderPass);
        // 眩光通道bloomPass插入到composer
        this.bloomComposer.addPass(outlinePass)
      }
    },
    loadFile(url) {
      /*      let t;
            new THREE.TextureLoader().load(url, async function(texture){
              t= texture;
            });*/
      return new THREE.TextureLoader().load(url);
    },
    regionsLightYFunc() {
      let _this = this;
      _this.lightY = -60;
      let time = new Date();
      _this.curDay = time.getDate();
      _this.regionsLightY = setInterval(() => {
        let curTime = time.getHours();
        let curDay2 = time.getDate();
        if (curDay2 != _this.curDay) {
          _this.lightY = -60;
          _this.curDay = curDay2;
          _this.lightY += 2.5
          return;
        }
        if (curTime >= 18 || curTime < 8) {
          _this.lightY = -20;
          _this.scene.children[3].position.set(23, _this.lightY, 50);
          return;
        } else {
          if (_this.lightY > 120) {
            _this.lightY = -60;
          } else {
            /*        _this.lightY+=2.5*/  //生产环境放开
            _this.lightY += 25
          }
          _this.scene.children[3].position.set(23, _this.lightY, 50);
        }
      }, 60000);
      /*     },450000);*/
    },
    //鼠标高亮事件
    handleMousemove(event) {
      event.preventDefault();
      let mouse = new THREE.Vector2(0, 0);
      let w = this.innerWidth-10;
      let h = this.innerHeight+35;
      mouse.x = (event.clientX / w * 0.87) * 2 - 1;
      mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
/*      mouse.x = (event.clientX /  this.innerWidth*0.87) * 2 - 1;
      mouse.y = - (event.clientY /  this.innerHeight*0.905) * 2 + 1;*/
      this.raycaster.setFromCamera( mouse,  this.camera);
      let intersects =  this.raycaster.intersectObjects( this.group.children);
      this.previousObj.material[0].color = new THREE.Color("#058dbd");
      if(intersects[0] && intersects[0].object) {
        intersects[0].object.material[0].color = new THREE.Color("#d6e73e");
        this.previousObj = intersects[0].object;
      }
    },
    handleClick(event) {
      if(this.curLocationType === '3')return;  //修正完数据后打开
      event.preventDefault();
      let mouse = new THREE.Vector2(0, 0);
      mouse.x = (event.clientX / this.innerWidth * 0.87) * 2 - 1;
      mouse.y = -(event.clientY / this.innerHeight * 0.905) * 2 + 1;
      this.raycaster.setFromCamera(mouse, this.camera);
      let intersects = this.raycaster.intersectObjects(this.group.children);
      mouse = cylinderCity(intersects,mouse,this.innerWidth,this.innerHeight,event);
      this.raycaster.setFromCamera(mouse, this.camera);
      let intersects2 = this.raycaster.intersectObjects(this.pillarGroup.children);
      console.log(intersects2.length);
      if(intersects2 != undefined && intersects2.length >0){
        this.$router.push({path:'/log',query:{type:'1'}})
        return;
      }   //修正完数据后打开
/*            if(this.curLocationType === '2')return;  //修正完数据后删掉*/
      if (intersects[0] && intersects[0].object) {
        //先全部置空
        this.removeAllGroup();
        if (Object.keys(this.cityMap).includes(intersects[0].object.name)) {
          this.mapJson = {};
          this.mapCenter = [];
          this.mapName = intersects[0].object.name;
          this.mapJson = this.cityMap[intersects[0].object.name];
          let curCityArr = this.sxJson.features.filter(item => item.properties.name === intersects[0].object.name);
          this.mapCenter = curCityArr[0].properties.center;
          this.scaleBlowUp = 1400; //地市图放大
          this.scaleBlowUp2 = 1400;
          //下钻数据修正
          let cityObj = changeCity(curCityArr,this.mapCenter);
          //再次加载地图
          this.createOnceMoreMap(cityObj);
          //定时器重置
          this.clearIntervalFunc();
          this.downCityShow = false;
          this.regionsCityIntervalFunc();
        }
      }
    },
    createOnceMoreMap(cityObj){
      this.scaleBlowUp = cityObj.scaleBlowUp;
      this.scaleBlowUp2 = cityObj.scaleBlowUp2;
      this.topMap = cityObj.topMap;
      this.leftMap = cityObj.leftMap;
      this.initCSS3D();
      this.mapCenter[0] = cityObj.mapCenter[0];
      this.mapCenter[1] = cityObj.mapCenter[1];
      this.initMap(this.mapCenter, this.mapJson);
      //重新渲染地图
      this.group.children = this.unique(this.group.children,'name');
      this.group.scale.y = 1.2;
      this.scene.add(this.group);
      this.lineGroup.scale.y = 1.2;
      this.scene.add(this.lineGroup);
      this.camera.fov = 179;
      this.rangeChangeShow = true;

    },
    //清除群组
    removeAllGroup() {
      this.clearGroup(this.group);
      this.scene.remove(this.group);
      this.clearGroup(this.groupTag);
      this.scene.remove(this.groupTag);
      this.clearGroup(this.pillarGroup);
      this.scene.remove(this.pillarGroup);
      this.clearGroup(this.pillarGroup2);
      this.scene.remove(this.pillarGroup2);
      this.clearGroup(this.cylinderMeshGroup);
      this.scene.remove(this.cylinderMeshGroup);
      this.clearGroup(this.cylinderMesh3Group);
      this.scene.remove(this.cylinderMesh3Group);
      this.clearGroup(this.lineGroup);
      this.scene.remove(this.lineGroup);
      this.cylinderMeshArr = [];
    },
    clearGroup(group) {
      // 释放 几何体 和 材质
      const clearCache = (item) => {
        if (item.geometry) {
          item.geometry.dispose();
          if (item.material.length > 0) {
            item.material.forEach((item2) => {
              item2.dispose();
            });
          } else {
            item.material.dispose();
          }
        } else {
          item.clear();
        }
      };
      // 递归释放物体下的 几何体 和 材质
      const removeObj = (obj) => {
        let arr = obj.children.filter((x) => x);
        arr.forEach((item) => {
          if (item.children.length && item.children.length > 0) {
            /*            removeObj(item);*/
          } else {
            clearCache(item);
            item.clear();
          }
        });
        obj.clear();
        arr = null;
      };
      // 移除 group
      removeObj(group);
    },
  },
  destroyed() {
    this.scene.remove(this.group); // 删除组
    this.scene.remove(this.groupTag);
    this.scene.remove(this.groupSprite);
    this.scene.clear();
    this.renderer.dispose();
    this.renderer.forceContextLoss();
    this.renderer.content = null;
    this.controls = null;
    //清除定时器
    clearInterval(this.regionsCity);
    this.regionsCity = null;
    clearInterval(this.regionsLightY);
    this.regionsLightY = null;
    cancelAnimationFrame(this.controls) ;// 去除animationFrame
    let gl = this.renderer.domElement.getContext("webgl");
    gl && gl.getExtension("WEBGL_lose_context").loseContext();
  },
}
</script>

<style lang="scss" scoped>
// @import url(); 引入公共css类
.sx-map {
  width: 100%;
  height: 100%;
}

.go-back {
  position: absolute;
  top: 120px;
  left: 25px;
  background: transparent;
  // border: 1px solid #3571BC;
  // border-radius: 2px;
  border: none;
  padding: 0;
  font-size: 18px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #41c4bf;
  height: 60px;
  width: 120px;
  cursor: pointer;
  z-index: 99999;
}

.export {
  width: 72px;
  height: 32px;
  background: #08214D;
  border: 1px solid #3571BC;
  border-radius: 2px;
  padding: 0;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #6FB3FF;
  box-shadow: 0 0 8px #3571BC;
  animation: 8s move infinite linear normal;
  animation-delay: -3s;
  animation-iteration-count: 1;
  @keyframes move {
    100% {
      top: 10%;
      opacity:1;
    }
    75% {
      top: 50%;
      opacity:0.25;
    }
    50% {
      top: 100%;
      opacity:0;
    }
  }
}
.el-button.is-plain:focus, .el-button.is-plain:hover {
  width: 72px;
  height: 32px;
  padding: 3px 2px;
  background: #08669d;
  border: 1px solid #08669d;
  border-radius: 2px;
  padding: 0;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #fff;
  box-shadow: 0 0 8px #08669d;
}
</style>

