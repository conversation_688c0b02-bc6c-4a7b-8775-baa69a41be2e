<template>
	<!-- 自定义列表 -->
	<div class="det-table hf">
		<el-table
			:data="tableList"
			:header-cell-style="{
				'color': '#9ED8FF',
				'background-color': 'rgba(15, 52, 124, 0.57)',
				'text-align': 'center',
				'font-size': '14px',
			}"
			v-loading="loading"
			element-loading-text="拼命加载中..."
			class="table-part fs18 wf"
			:rowKey="record => record.index"
			:show-overflow-tooltip="true"
		>
			<!-- :rowKey="record => record.resourceName || record.svcTitle" -->
			<!-- 列渲染 -->
			<el-table-column
				v-for="(i, idx) in colConfigs"
				:key="'col' + idx"
				:prop="i.prop"
				:label="i.label"
				:align="i.align"
				:width="i.width"
				:show-overflow-tooltip="true"
			>
				<template slot-scope="scope">
					<div v-if="i.prop === 'alarmCategory' && scope.row.alarmCategory !== null">
							<span :style="{color: scope.row[i.prop] == '一级告警' ? '#FF4349' : scope.row[i.prop] == '二级告警' ? '#FF8B00' : scope.row[i.prop] == '三级告警' ? '#87D067' : '#0086E5'}"> {{ scope.row[i.prop] ? scope.row[i.prop] : "" }} </span>
					</div>
          <span v-else> {{ scope.row[i.prop] ? scope.row[i.prop] : "" }} </span>
				</template>
			</el-table-column>
			<div slot="empty">
				<i class="el-icon-warning fs16"></i>
				<span class="ml10 fs16">暂无数据！</span>
			</div>
		</el-table>
	</div>
</template>

<script>
export default {
	name: "detailTable",
	props: {
		list: {
			type: Array,
			default: () => [],
		},
		colConfigs: {
			type: Array,
			default: () => [],
		},
    loading: {
      type: Boolean,
			default: () => false,
    },
	},
	data() {
		return {
			tableList: [],
			columns: [],
			resourceName: '',
		};
	},
	watch: {
		list: {
			handler(n, o) {
				n.map((i, idx) => {
					i.idx = idx+1;
				})
				this.tableList = n;
			},
			deep: true,
		},
		colConfigs: {
			handler(n, o) {
				this.columns = n;
			},
			deep: true,
		},
	},
	mounted() {
		let list = this.list;
		list.map((i, idx) => {
			i.idx = idx + 1;
		})
		this.$nextTick(() => {
			this.tableList = list ? list : [];
			this.columns = this.colConfigs ? this.colConfigs : [];
			this.custPercent = this.percent ? this.percent : null;
		})
	},
	methods: {
    
	}
};
</script>

<style scoped lang="less">
	.det-table {
    padding: 16px 20px;
		::v-deep {
			// .el-table {
      //   background: none;
      //   &::before {
      //     height: 0;
      //     background: none;
      //   }
      //   tr {
      //     background: none;
      //   }
			// 	thead {
			// 		font-size: 14px;
			// 		font-family: "PingFangSC-Regular, PingFang SC";
			// 		// color: #73777a;
      //     height: 49px;
      //     background-color: rgba(15, 52, 124, 0.57);
      //     &.has-gutter {
      //       height: 49px;
      //       line-height: 49px;
      //       .cell {
      //         height: 49px;
      //         line-height: 49px;
      //       }
      //     }
			// 	}
      //   .el-table__header-wrapper {
      //     height: 49px;
      //     line-height: 49px;
      //     // border: 1px solid rgba(24, 78, 138, 1);
      //   }
			// 	.el-table__body-wrapper {
			// 		overflow-y: scroll;
			// 		overflow-x: hidden;
			// 		position: relative;
      //     .el-table__row {
      //       background: rgba(0, 0, 0, 0);
      //     }
			// 		.el-table__body {
			// 			tr {
			// 				cursor: pointer;
			// 				&:hover {
			// 					.el-table__cell {
			// 						color: #606266;
      //             background: rgba(0, 0, 0, 0);
			// 					}
			// 				}
			// 				&.el-table__row {
			// 					td:hover {
			// 						color: #373d41;
      //             background: rgba(0, 0, 0, 0);
			// 					}
			// 					&.custRow {
			// 						td {
			// 							background: rgba(37, 190, 247, 0.4);
      //               &:hover {
      //                 color: #e1e9f9;
      //                 background: rgba(37, 190, 247, 0.4);
      //               }
      //               span {
      //                 color: #e1e9f9 !important;
      //               }
			// 						}
			// 					}
			// 				}
			// 			}
						
			// 		}
			// 	}
			// 	.cell {
			// 		font-weight: 500;
			// 		font-size: 14px;
			// 	}
			// 	.el-table__empty-block {
			// 		min-height: 200px;
			// 	}
			// }
			// .el-loading-spinner {
			// 	.el-loading-text,
			// 	.path {
      //     color: #02dbff;
      //     stroke: #02dbff;
			// 	}
			// }
			.red {
				color: red;
			}
		}
	}
</style>

<style lang="less" scoped>
	.current-row {
		.el-table__cell {
			background: rgba(21, 100, 154, 1) !important;
			span {
				color: #e1e9f9 !important;
			}
		}
		&:hover {
			color: #e1e9f9 !important;
			background: rgba(21, 100, 154, 1) !important;
			.el-table__cell {
				background: rgba(21, 100, 154, 1) !important;
				span {
					color: #e1e9f9 !important;
				}
			}
		}
	}
</style>