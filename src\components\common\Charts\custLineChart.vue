<template>
  <!-- 自定义折线图 -->
  <div :ref="className" :class="className" :style="{ height, width }" />
</template>

<script>

import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "../../../mixins/chart/resize.js";

export default {
  name: "custLineChart",
  mixins: [resize],
  props: {
    // 图表数据
    chartData: {
      type: Array,
      default: () => []
    },
    // 类名
    className: {
      type: String,
      default: "chart"
    },
    // 宽度
    width: {
      type: String,
      default: "100%"
    },
    // 高度
    height: {
      type: String,
      default: "300px"
    },
    // Y单位
    unitY: {
      type: String,
      default: ""
    },
    // 坐标轴数值配置
    axis: {
      type: Object,
      default: () => ({
        name: "",
        type: 'category',
        property: "name",
        axisLine: {
          show: true,
          lineStyle: {
            color: "#1A2F6C"
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false,
        },
        splitArea: { 
          show: false 
        },
        axisLabel: {
          color: "#CBD4E4",
          fontSize: 12,
          formatter: '{value}',
          rotate: 25,
        },
      })
    },
    max: {
      type: Number,
      default: () => null
    },
    // 纵轴配置
    yAxis: {
      type: Object,
      default: () => ({
        type: "value",
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false,
        },
        splitArea: { 
          show: false 
        },
      })
    },
    // 条形数值配置
    series: {
      type: Array,
      default: () => [
        {
          name: "使用次数",
          property: "useCount"
        }
      ]
    },
    seriesName: {
      type: String,
      default: () => ""
    },
    // 是否排序
    sort: {
      type: Boolean,
      default: false
    },
    // 图表背景
    background: {
      type: String,
      default: "transparent"
    },
    // 字体基数
    scale: {
      type: Number,
      default: 1
    },
    // 图表展示方式：条纹横向 horizontal， 条纹竖向 vertical
    direction: {
      type: String,
      default: "vertical"
    },
    // 动画 持续时间
    animationDuration: {
      type: Number,
      default: 1500
    },
    // 颜色配置
    colorList: {
      type: Array,
      default: () => [
        "#19d4ae",
        "#3dbdef",
        "#ff8fa2",
        "#ffc393",
        "#628be8",
        "#ae83f3",
        "#ff94d4",
        "#61c9da"
      ]
    },
    // 提示
    tooltip: {
      type: Object,
      default: () => ({
        trigger: "axis",
        borderWidth: 1,
        borderColor: "#1A2F6C",
        backgroundColor: "rgba(26, 47, 108, 0.5)", // 通过设置rgba调节背景颜色与透明度
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: "line" // 默认为直线，可选为：'line' | 'shadow'
        },
        textStyle: {
          color: "#fff",
        },
      })
    },
    tooltipFormatter: {
      type: Function,
      default: () => (params) => {
        let str = `<div>
          <p>${params[0].marker} ${params[0].name}: ${params[0].value}</p>
        </div>`;
        return str;
      },
    },
    formatter: {
      type: Function,
      default: () => (val, idx) =>{
        return val != 0 ? val.toFixed(1) : 0
      },
    },
    // 网格
    grid: {
      type: Object,
      default: () => ({
        top: "8%",
        left: "5%",
        right: "5%",
        bottom: "5%",
        containLabel: true
      })
    },
    // 数据放大配置
    dataZoom: {
      type: Array,
      default: () => [
        {
          type: "inside"
        }
      ]
    }
  },
  watch: {
    chartData(data) {
      console.log('chartData', data);
      // 图表重绘
      this.chart.setOption({}, true);
      this.initChart();
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.chart = echarts.init(this.$refs[this.className], "macarons");
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      // 处理横坐标轴数据
      let axisData = [];
      if(this.chartData && this.chartData.length>0) {
        this.chartData.forEach(e => {
          axisData.push(e[this.axis.property]);
        });
      }

      // 处理条形数据
      let seriesSet = [];
      this.series.forEach(e => {
        seriesSet.push({
          name: e.name ? e.name : this.seriesName ? this.seriesName : '',
          symbol: e.symbol ? e.symbol : "circle",
          type: e.type ? e.type : "line",
          showSymbol: e.showSymbol ? e.showSymbol : true,
          symbolSize: e.symbolSize ? e.symbolSize : 3,
          smooth: e.smooth ? e.smooth : false,
          itemStyle: e.itemStyle ? e.itemStyle : {
            color: '#00FFFF',
            backgroundColor: '#00FFFF'
          },
          data: this.chartData.map(i => i[e.property]),
          animationDuration: this.animationDuration
        });
      });
      // y轴格式化
      if(this.formatter) {
        this.yAxis.axisLabel.formatter = this.formatter;
      }
      // y轴最大值
      if(this.unitY=="%") {
        this.yAxis.max = 100;
        this.yAxis.min = 0;
      } else {
        let maxValue = Math.max(...seriesSet[0].data) > 0 && Math.max(...seriesSet[0].data) < 5 ? 5 : Math.max(...seriesSet[0].data) == 0 ? 5 : 100;
        this.yAxis.max = maxValue;
        console.log(`%c${maxValue}`,`color:lightblue;text-shadow: 0 1px 2px #0080ffc4, 0 1px 5px #36b3ff;font-size:24px;font-family:'Roboto'`);
      }
      // 提示框格式化
      if(this.tooltipFormatter) {
        this.tooltip.formatter = this.tooltipFormatter;
      }
      // 图表配置
      this.chart.setOption({
        tooltip: {
          ...this.tooltip,
        },
        legend: {
          show: false,
          orient: "horizontal",
          top: 0
        },
        grid: this.grid,
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: axisData,
          ...this.axis
        },
        yAxis: {
          ...this.yAxis,
        },
        series: seriesSet
      });
    }
  }
};
</script>

