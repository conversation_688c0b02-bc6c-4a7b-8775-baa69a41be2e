<template>
  <div style="height: 70%;">
    <div class="nc-query">
      <el-form :inline="true" :model="form" label-width="100px">
        <el-form-item
          class="formItem"
          label="角色名"
        >
        <el-input v-model="form.roleName" placeholder="请输入角色名" clearable></el-input>
        </el-form-item>
        <el-form-item style="float:right;width:5%">
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
        <el-form-item style="float:right;width:5%">
          <el-button type="primary"  @click="selectAllUserInfo">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="margin:0 0 15px 0">
      <el-button type="primary" size="mini" icon="el-icon-plus" @click="addUser">新增</el-button>
    </div>
    <el-table 
      border 
      :data="tableBody" 
      :header-cell-style="{
        color: '#9ED8FF',
        'background-color': 'rgba(15, 52, 124, 1)',
        'text-align': 'center',
        'font-size': '14px',
        'border-right': '1px rgba(15, 0, 137, .8) solid'
      }"
      height="95%"
      style="border-radius: 4px 4px 0 0;
      border:none;margin-top: 5px;"
    >
      <el-table-column 
        align='center'
        v-for="(i, idx) in tableColumns" 
        :key="idx" 
        :label="i.displayName" 
        :prop="i.columnName" 
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="100"
      >
        <template slot-scope="scope">
          <el-button title="编辑" type="text" size="mini" icon="el-icon-edit" style="margin-right: 5px" @click="addUser(scope.row)"></el-button>
          <el-popconfirm
            confirm-button-text='好的'
            cancel-button-text='不用了'
            icon="el-icon-info"
            icon-color="red"
            title="确定删除这个角色吗？"
            @confirm="deleteRole(scope.row)"
          >
            <el-button 
              title="删除" 
              slot="reference" 
              type="text" 
              size="mini" 
              icon="el-icon-delete"
            ></el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
      <el-pagination
        class="page-list"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageNumber"
        :page-sizes="[1,10, 20, 30, 40, 50]"
        :page-size="pageSize"
        background
        layout="total, prev, pager, next, sizes"
        :total.sync="total"
        style="float: right"
      >
      </el-pagination>
      <!-- 新增/编辑用户 -->
      <!-- `${dialogFormData.id ? '修改' : '新增'}用户` -->
      <el-dialog 
        :title="`${formData.roleId ? '修改' : '新增'}角色`" 
        width="600px" 
        :visible.sync="visible" 
        append-to-body
        :before-close="closeModel" 
        @close="resetForm" 
        :close-on-click-modal="false"
      >
      <el-form ref="formData" :model="formData" label-width="110px" size="small" :rules="rules">
        <el-form-item label="角色名称 :" prop="roleName">
          <el-input v-model.trim="formData.roleName" style="width: 100%" placeholder="名称" />
        </el-form-item>
        <div style="text-align: center; margin-top: 20px">
          <el-button size="small" @click="closeModel">
            取消
          </el-button>
          <el-button type="primary" size="small" @click="subForm('formData')">
            提交
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
data(){
    return {
        visible: false,
        detailModal: false,
        form: {
            roleName: '',
        },
        formData: {},
        total: 10,
        pageSize: 10,
        pageNumber: 1,
        allCityData:  [],
        allCountiesData: [],
        allCustUserRoles: [],
        roleList:[],
        custIdList: [],
        tableColumns: [{
            displayName: '角色编码',
            columnName: 'roleId'
        },{
            displayName: '角色名称',
            columnName: 'roleName'
        },],
        tableBody: [],
        userInformation: [],
        customerAdded: [],
        noCustomerAdded: [],
        customerName: '',
        deleteCustomer: [],
        rules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { max: 20, message: "账号长度最多20个字符", trigger: "change" }
        ],
      },
    }
},
created() {
    this.getAllCity()
    this.getAllCounties()
    this.getcustUserRoles()
    this.selectAllUserInfo()
},
methods: {
  getcustUserRoles(){
    this.$api.slaApi.getcustUserRoles()
      .then(res=>{
          this.allCustUserRoles = res.data
      })  
  },
  getAllCity(){
    this.$api.slaApi.getAllCity()
    .then(res=>{
      this.allCityData = res.data
    })  
  },
  getAllCounties(){
    this.$api.slaApi.getAllCounties()
    .then(res=>{
      this.allCountiesData = res.data
    }) 
  },
  selectAllUserInfo(){
    this.$api.slaApi.selectAllRoleInfo({
      "pageNum":this.pageNumber,
      "pageSize":this.pageSize,
      ...this.form
    })
    .then(res=>{
      this.tableBody = res.data;
      this.total = res.total;
    })
  },
  // 深拷贝
  extendCopy(p) {
    var c = {};
    for (var i in p) {
      c[i] = p[i];
    }
    c.uber = p;
    return c;
  },
  // 重置表单
  resetQuery(){
    this.form = {
          userName: '',
          city: '',
          counties: ''
      };
      this.pageSize = 10;
      this.pageNumber = 1;
      this.selectAllUserInfo()
  },
  addUser(row){
    // this.addCustomer()
    if (row.roleId) {
      this.formData = this.extendCopy(row);
    } else {
      this.formData = {};
    }
    this.visible = true;
  },
  subForm(formData) {
    if (this.formData.roleId) {
      this.$refs.formData.validate(valid => {
        if(valid) {
          this.subEditForm(formData);
        }
      });
    } else {
      this.$refs.formData.validate(valid => {
        if(valid) {
          this.subAddForm(formData);
        }
      });

    }
  },
  subAddForm(){
    this.$refs['formData'].validate((valid) => {
      if (valid) {
        this.$api.slaApi.addRole(this.formData)
        .then(res=>{
          if (res.resultCode == 200) {
            this.$message({
              message: res.data,
              type: 'success'
            });
            this.visible = false;
          } else {
            this.$message.error('新增失败');
          }
          this.selectAllUserInfo()
        })
      } else {
        console.log('error submit!!');
        return false;
      }
    });
  },
  subEditForm(){
    this.$refs['formData'].validate((valid) => {
      if (valid) {
        this.$api.slaApi.updateRole(this.formData)
        .then(res=>{
          if (res.resultCode == 200) {
            this.$message({
              message: res.data,
              type: 'success'
            });
            this.visible = false;
          } else {
            this.$message.error('修改失败');
          }
          this.selectAllUserInfo()
        })
      } else {
        console.log('error submit!!');
        return false;
      }
    });
  },
  // 删除角色
  deleteRole(row){
  // deleteRole
  this.$api.slaApi.deleteRole({roleId: row.roleId} )
    .then(res=>{
        if (res.resultCode == 200) {
        this.$message({
          message: res.data,
          type: 'success'
        });
        this.visible = false;
      } else {
        this.$message.error('删除失败');
      }
      this.selectAllUserInfo()
    })
  },
  resetForm() {
  this.formData.roleName = ''
  },
  closeModel() {
    this.visible = false;
  },
  /**
   * <AUTHOR>
   * 监听每页条数选择
   */
  handleSizeChange(val) {
    this.pageSize = val;
    this.selectAllUserInfo();
  },
  /**
   * <AUTHOR>
   * 监听当前页
   */
  handleCurrentChange(val) {
    this.pageNumber = val;
    this.selectAllUserInfo();
  },
  // addCustomer(){
  //   this.noCustomerAdded = [];
  //     let params = {
  //       options: JSON.stringify({"current":1,
  //       "limit":10,
  //       "isQueryCount":true,
  //       "where":{
  //           "custName":{
  //               "$like":this.customerName
  //         }}})}
  //     // this.$api.slaApi.query('pubCust',JSON.stringify(params))
  //     .then((result) => {
  //       result.data.map(item => {
  //         this.noCustomerAdded.push({
  //           custId: item.custId, 
  //           custName: item.custName 
  //         })
  //       })
  //     })
  // },
}

}
</script>

<style lang="less" scoped>
.nc-query {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
  // height: 10vh;
}
/deep/ .el-checkbox__inner {
    background-color: transparent;
    border: 1px solid #02dbff;
}
::v-deep {
  .el-table__fixed-right-patch {
      background-color: rgba(23, 70, 137, 1);
    }
    .el-table--border th.el-table__cell, .el-table__fixed-right-patch {
      border: none;
    }
    .el-table__fixed-right::before, .el-table__fixed::before {
      background-color: transparent;
    }
		.el-dialog {
			// height: 30vh;
            // width: 60%;
			background: url("../../assets/images/dialogbg.png") center center
              no-repeat;
            background-size: 100% 100%;
			margin-top: 10vh !important;
		}
		.el-dialog__header {
			justify-content: left;
			background: none;
		}
		.el-dialog__title {
			justify-content: left;
            line-height: 20px;
            font-weight: 800;
            height: 20px;
            display: flex;
            color: rgba(255, 255, 255, 1);
            font-size: 22px;
		}
		.el-dialog__title::before ,
		.el-dialog__title::after{
            content:none;
        }
		.el-dialog__headerbtn {
      width: 30px;
      height: 30px;
      float: right;
      top: 10px;
      right: 10px;
			background: url("../../assets/images/x.png") center center
        no-repeat;
      background-size: 100% 100%;
		}
		.el-dialog__body {
			height: 85%;
			.detail-info {
				max-height: 800px;
				height: 100%;
			}
			.el-form {
				min-height: 100px;
				width: 95%;
				// margin: 30px auto 20px;
        margin-top: 30px;
				text-align: center;
        .el-form-item__label {
          color: #fff;
        }
        .el-input__inner {
					color: #02dbff;
					background: none;
					border: 1px solid rgba(37, 190, 247, 0.5);
					// height: 24px !important;
					line-height: 24px;
				}
        .el-switch__label, .el-checkbox {
          color: #fff;
        }
        .el-button--primary {
					color: #FFF;
          background-color: #409EFF;
          border-color: #409EFF;
					&:focus {
						background-color: #409EFF;
          border-color: #409EFF;
					}
					&:hover {
						background-color: #409EFF;
          border-color: #409EFF;
					}
				}
        .el-select-dropdown__item:hover {
            color: #409EFF;
            background: #40a0ff27;
        }
        .el-button--default {
            color: #409EFF;
            background: transparent;
            border-color: #409EFF;
        }
			}
		}
	}
  .addCustomer {
    height: 5%;
    width: 6%;
    float: left;
    position: relative;
    top: 45%;
    left: 4%;
    background: url("../../assets/images/addCustomer.png") center center
      no-repeat;
    background-size: 100% 100%;
    transform: rotate(180deg);
  }
</style>