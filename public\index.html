<!DOCTYPE html>
<html lang="">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width,initial-scale=1.0"> -->
        <link rel="icon" href="<%= BASE_URL %>irms.png">
        <title>
            <%= htmlWebpackPlugin.options.title %>
        </title>
        <link rel="stylesheet" href="<%= BASE_URL %>font_icon/iconfont.css">
        <link rel="stylesheet" href="<%= BASE_URL %>swiper/swiper-bundle.min.css?timetap=<%= htmlWebpackPlugin.options.timetap %>">
        <script src="<%= BASE_URL %>swiper/jquery-3.2.1.min.js?timetap=<%= htmlWebpackPlugin.options.timetap %>"></script>
        <script src="<%= BASE_URL %>swiper/swiper-bundle.min.js?timetap=<%= htmlWebpackPlugin.options.timetap %>"></script>
        <script src="<%= BASE_URL %>rtc/trtc.js?timetap=<%= htmlWebpackPlugin.options.timetap %>"></script>
        <script src="<%= BASE_URL %>rtc/lib-generate-test-usersig.min.js?timetap=<%= htmlWebpackPlugin.options.timetap %>"></script>
        <script src="<%= BASE_URL %>rtc/debug/GenerateTestUserSig.js?timetap=<%= htmlWebpackPlugin.options.timetap %>"></script>
        <script src="https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/api/booter.js"></script>
        <script src="https://gis.10010.com:8219/dugis-baidu/bmapgl/?qt=getscript&libraries=visualization"></script>
        <link href="//mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/DrawingManager.min.css" rel="stylesheet">
        <script type="text/javascript" src="//mapopen.cdn.bcebos.com/github/BMapGLLib/DrawingManager/src/DrawingManager.min.js"></script>
    </head>
    <body>
        <noscript>
            <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript
                    enabled. Please enable it to continue.</strong>
        </noscript>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>
</html>
<style>
    body {
        caret-color: transparent;
        input {
            caret-color: #fff;
        }
    }
</style>