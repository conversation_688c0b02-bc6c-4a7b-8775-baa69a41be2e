<template>
	<!-- 纵向柱状图 -->
	<div
		ref="bar"
		class="bar"
		v-loading="uploading"
		element-loading-text="数据加载中"
		element-loading-background="rgba(0, 0, 0, 0)"
		:style="{ height, width }"
	/>
</template>
<script>
import * as echarts from 'echarts';
export default {
	name: "lBarChart",
	props: {
		barDataSX: {
			type: Object,
			default: {},
		},
    // 宽度
    width: {
      type: String,
      default: "100%"
    },
    // 高度
    height: {
      type: String,
      default: "28vh"
    },
	},
	data() {
		return {
			uploading: true,
			legend: {
				show: false,
			},
			custData: {},
		};
	},
	watch: {
		barDataSX: {
			handler(n, o) {
				if (n) {
					this.custData = n;
					this.setMyEchart();
				}
			},
			deep: true,
		},
	},
	// created(){
	// 	this.custData = this.barDataSX;
	// 	console.log(this.barDataSX);
	// },
	mounted() {
		this.$nextTick(() => {
			this.custData = this.barDataSX;
		});
		// this.setMyEchart();
	},
	methods: {
		setMyEchart(data) {
			let _this = this;
			const myChart = this.$refs.bar;
			if (myChart) {
				const barChart = echarts.init(myChart);
				const option = {
	                grid: {
	                	left: '4%',
	                	right: '4%',
	                	bottom: '6%',
	                	top:30,
	                	padding:'0 0 10 0',
	                	containLabel: true,
	                },
                    legend: {//图例组件，颜色和名字
                        right:10,
	                	top:0,
	                	itemGap: 16,
	                	itemWidth: 18,
	                	itemHeight: 10,
                        data:[{
                            name:'告警数量',
                            icon:'circle', //路径
                        },],
                        textStyle: {
	                		color: '#a8aab0',
	                		fontStyle: 'normal',
	                		fontFamily: '微软雅黑',
	                		fontSize: 12,            
                        }
                    },
	                xAxis: [
	                	{
	                		type: 'category',
	                		boundaryGap: true,//坐标轴两边留白
	                		data: _this.barDataSX.xData,
	                		axisLabel: { //坐标轴刻度标签的相关设置。
	                			interval: 0,//设置为 1，表示『隔一个标签显示一个标签』
	                			margin:15,
	                			textStyle: {
	                				color: '#a8aab0',
	                				fontStyle: 'normal',
	                				fontFamily: '微软雅黑',
	                				fontSize: 12,
	                			}
	                		},
	                		axisTick:{//坐标轴刻度相关设置。
	                			show: false,
	                		},
	                		axisLine:{//坐标轴轴线相关设置
	                			lineStyle:{
	                				color:'#1ad1de',
	                				opacity:0.2,
                                    fontStyle: 'normal',
	                			}
	                		},
	                		splitLine: { //坐标轴在 grid 区域中的分隔线。
	                			show: false,
	                		}
	                	}
	                ],
	                yAxis: [
	                	{
	                		type: 'value',
	                		splitNumber: 5,
	                		axisLabel: {
	                			textStyle: {
	                				color: '#a8aab0',
	                				fontStyle: 'normal',
	                				fontFamily: '微软雅黑',
	                				fontSize: 12,
	                			}
	                		},
	                		axisLine:{
	                			show: false
	                		},
	                		axisTick:{
	                			show: false
	                		},
	                		splitLine: {
			                	show: true,
			                	lineStyle: {
			                		color: ['#eee'],
			                		opacity:0.1,
									type:'dotted'
			                	}
			                }
                
	                	}
	                ],
                    series : [
                        // {
                        //     name:'告警数量',
                        //     type:'bar',
                        //     data:_this.barDataSX.yData,
                        //     barWidth: 20,
                        //     barGap:0,//柱间距离
                        //     label: {//图形上的文本标签
                        //         normal: {
                        //            show: true,
                        //            position: 'top',
                        //            textStyle: {
                        //                color: '#1ad1de',
                        //                fontStyle: 'normal',
                        //                fontFamily: '微软雅黑',
                        //                fontSize: 12,   
                        //            },
                        //         },
                        //     },
                        //     itemStyle: {//图形样式
                        //         normal: {
	                	// 			barBorderRadius: [5, 5, 0, 0],
	                	// 			color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                        //                 offset: 1, color: 'rgba(104, 253, 255, 0.7)'
                        //             }, {
                        //                 offset: 0, color: 'rgba(4, 157, 224, 0.7)'
                        //             }], false),
                        //         },
                        //     },
                        // },
						{
                          "data": _this.barDataSX.yData,
                          "type": "bar",
                          barMinHeight: 10,
                          "barWidth": 10,
                          "itemStyle": {
                            "color": {
                              "x": 0,
                              "y": 0,
                              "x2": 0,
                              "y2": 1,
                              "type": "linear",
                              "global": false,
                              "colorStops": [
                                {
                                  "offset": 0,
                                  "color": "RGBA(94, 144, 252, 1)"
                                },
                                {
                                  "offset": 1,
                                  "color": "RGBA(52, 170, 209, .3)"
                                }
                              ]
                            }
                          },
                          "label": {
                            "show": true,
                            "position": "top",
                            "distance": 10,
                          //   rotate:45,
                          //   offset:[10,0],
                            "color": "rgba(255, 255, 255, 0.5)"
                          }
                        },
                        {
                          "data": _this.barDataSX.yData,
                          "type": "pictorialBar",
                          "barMaxWidth": "10",
                          barMinHeight: 10,
                          "symbolPosition": "end",
                          "symbol": "diamond",
                          color: "RGBA(52, 170, 209, 1)",
                          "symbolOffset": [
                            0,
                            "-50%"
                          ],
                          "symbolSize": [
                            10,
                            5
                          ],
                          "zlevel": 2
                        },
                    ]
                };

				this.uploading = false;
				barChart.setOption(option);

				window.addEventListener("resize", function () {
					barChart.resize();
				});
				barChart.off('click');
				barChart.on("click",(parent) => {
					// console.log('柱状图点击事件',parent);
					this.$emit('cityName',parent.name)
				})
			}
		},
	},
};
</script>

<style lang='less' scoped>
.bar {
	width: 100%;
	height: 100%;
	min-width: 300px;
	// min-height: 20vh;
}
</style>
