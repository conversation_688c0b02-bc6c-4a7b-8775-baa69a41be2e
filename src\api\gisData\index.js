import axios from "@/utils/api.request";
const BASEURL = '/otnNpv'
const git_data = {
    // 性能监控告警
    getMapPointLineData() {
      // return axios.request({
      //   url: `${BASEURL}/monitor/getPerformanceAlertStatistics`,
      //   method: "post",
      // });
      return Promise.resolve([
        {
          name: '小寨',
          lng: 108.9434,
          lat: 34.2178,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '钟楼',
          lng: 108.9402,
          lat: 34.2583,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '大雁塔',
          lng: 108.9646,
          lat: 34.2192,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '西安万象城',
          lng: 108.9563,
          lat: 34.2314,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        },
        {
          name: '西安北站',
          lng: 108.9633,
          lat: 34.3708,
          description: `设备名称：10010-浐灞<br>所属机房：西安未央欧亚大道浐灞局四楼综合机房`
        }
      ]);
    },
}
export default git_data;
