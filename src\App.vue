<template>
<div class="container" id="app">
  <router-view class="routerview"></router-view>
</div>
</template>

<script>
export default {
  name: "App",
  data() {
    return {};
  },
  computed: {},
  watch: {},
  components: {},
  mounted() { },
  methods: {},
};
</script>

<style lang="less">
body,
html {
  width: 100%;
  height: 100vh;
  min-width: 1024px;
}

.container {
  box-sizing: border-box;
  position: relative;
  background: #181f32;
  background-size: 100% 100%;
  color: #fff;
  // display: flex;
  // flex-direction: column;
  position: relative;
}
</style>
