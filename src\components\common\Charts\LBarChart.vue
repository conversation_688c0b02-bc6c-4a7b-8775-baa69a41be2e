<template>
	<!-- 纵向柱状图 -->
	<div
		ref="bar"
		class="bar"
		v-loading="uploading"
		element-loading-text="数据加载中"
		element-loading-background="rgba(0, 0, 0, 0)"
		:style="{ height, width }"
	/>
</template>
<script>
import echarts from "echarts";
export default {
	name: "lBar<PERSON>hart",
	props: {
		barData: {
			type: Object,
			default: {},
		},
    // 宽度
    width: {
      type: String,
      default: "100%"
    },
    // 高度
    height: {
      type: String,
      default: "28vh"
    },
	},
	data() {
		return {
			uploading: true,
			legend: {
				show: false,
			},
			custData: {},
		};
	},
	watch: {
		barData: {
			handler(n, o) {
				if (n) {
					this.custData = n;
					this.setMyEchart();
				}
			},
			deep: true,
		},
	},
	mounted() {
		this.$nextTick(() => {
			this.custData = this.barData;
		});
	},
	methods: {
		setMyEchart() {
			let xData = this.custData.xData ? this.custData.xData : [];
			let colors = this.custData.colors
				? this.custData.colors
				: "#5B8FF9";
			let series = this.custData.series ? this.custData.series : [];
			let format = this.custData.format ? this.custData.format : null;
			let legend = this.custData.legend
				? this.custData.legend
				: this.legend;
			let rotate = this.custData.rotate ? this.custData.rotate : 0;
			let barWidth = this.custData.barWidth
				? this.custData.barWidth
				: null;

			let custSymbol = this.custData.series[1].symbol
				? this.custData.series[1].symbol
				: false;
			let custLine = this.custData.series[1].lineStyle
				? this.custData.series[1].lineStyle
				: null;
			let maxData = series[0].yData.concat(series[1].yData);
			let maxValue =
				maxData && Math.max(...maxData) > 0 ? Math.max(...maxData) : 5;

			const myChart = this.$refs.bar;
			if (myChart) {
				const barChart = echarts.init(myChart);
				const option = {
					color: colors,
					legend: legend,
					tooltip: {
						trigger: "axis",
						backgroundColor: "rgba(0, 10, 49, 0.9)",
						borderColor: "rgba(147, 235, 248, 0.1)",
						borderWidth: 0.1,
						shadowColor: "rgba(95, 148, 229, 0.75)",
						shadowBlur: 20,
						textStyle: {
							color: "#FFFFFF",
						},
						extraCssText: "box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
						axisPointer: {
							type: "shadow",
						},
						formatter: format,
					},
					grid: {
						left: "12%",
						top: "15%",
						right: "5%",
						bottom: "15%",
					},
					xAxis: {
						data: xData,
						boundaryGap: true,
						axisLine: {
							show: true, //隐藏X轴轴线
							lineStyle: {
								color: "rgba(0,0,0,0.15)",
								width: 2,
							},
						},
						axisTick: {
							show: false, //隐藏X轴刻度
							alignWithLabel: true,
						},
						axisLabel: {
							show: true,
							textStyle: {
								color: "#CBD4E4",
								fontSize: 10,
							},
							margin: 15,
							align: "center",
							interval: 0,
							rotate: rotate,
						},
					},
					yAxis: [
						{
							type: "value",
							min: 0,
							max: maxValue,
							name: series[0].unit ? series[0].unit : "",
							nameGap: 30,
							splitLine: {
								show: false,
								lineStyle: {
									color: "#ebebeb",
								},
							},
							axisTick: {
								show: false,
							},
							axisLine: {
								show: false, //隐藏X轴轴线
							},
							axisLabel: {
								show: true,
								textStyle: {
									color: "#CBD4E4",
									fontSize: 14,
								},
							},
						},
						{
							type: "value",
							name: series[1].unit ? series[1].unit : "",
							nameGap: 10,
							nameTextStyle: {
								color: "#ffffff",
								fontSize: 12,
								padding: [0, 30, 0, 0],
							},
							nameLocation: "end",
							splitLine: {
								show: false,
							},
							axisTick: {
								show: false,
							},
							axisLine: {
								show: false, //隐藏X轴轴线
							},
							axisLabel: {
								show: false,
							},
						},
					],
					series: [
						{
							name: series[0].name ? series[0].name : "",
							type: series[0].type,
							barWidth: barWidth,
							barGap: 0,
							label: {
								show: false,
							},
							showBackground: series[0].showBackground ? series[0].showBackground : false,
							backgroundStyle: series[0].backgroundStyle
									? series[0].backgroundStyle
									: {},
							itemStyle: {
								barBorderRadius: series[0].borderRadius
									? series[0].borderRadius
									: 0,
								...series[0].itemStyle
							},
							data: series[0].yData,
						},
						{
							name: series[1].name ? series[1].name : "",
							type: series[1].type ? series[1].type : "",
							barWidth: barWidth,
							itemStyle: {
								barBorderRadius: series[1].borderRadius
									? series[1].borderRadius
									: 0,
							},
							symbol: custSymbol,
							lineStyle: custLine,
							data: series[1].yData ? series[1].yData : [],
						},
					],
				};

				this.uploading = false;
				barChart.setOption(option);

				window.addEventListener("resize", function () {
					barChart.resize();
				});
			}
		},
	},
};
</script>

<style lang='less' scoped>
.bar {
	width: 100%;
	height: 100%;
	min-width: 300px;
	// min-height: 20vh;
}
</style>
