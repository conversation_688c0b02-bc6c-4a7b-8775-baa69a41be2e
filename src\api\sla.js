import axios from "@/utils/api.request";
const sla = {
  //用户登录
  getLogin(inParams) {
    return axios.request({
      url: "/otnUser/login",
      method: "post",
      data: inParams,
    });
  },
  // 发送短信验证码
  sendMessage(params) {
    return axios.request({
      url: "/otnUser/check/sendMessage",
      method: "post",
      data: params,
    });
  },
  // 验证验证码
  checkAuth(params) {
    return axios.request({
      url: "/otnUser/check/checkAuth",
      method: "post",
      data: params,
    });
  },
  // 修改密码
  changePassword(params) {
    return axios.request({
      url: "/otnUser/check/changePassword",
      method: "post",
      data: params,
    });
  },
  //校验登录状态
  checkTokenByTicketId(inParams) {
    return axios.request({
      url: "/otnUser/check/checkTicketId?ticketId=" + inParams,
      method: "get",
    });
  },

  // 下拉 - 获取地市列表
  getProvince(inParams) {
    return axios.request({
      url: "/otnNpv/resources/getArea?parentId=0",
      method: "post",
      data: inParams,
    });
  },
  // 下拉 - 获取地市列表
  getCity(inParams) {
    return axios.request({
      url: "/otnNpv/resources/getArea?parentId=10",
      method: "post",
      data: inParams,
    });
  },
  // 资源信息管理 - SLA单用户页面获取电路列表 && 内蒙古SLA页面 - 获取线路列表
  getCircuit(inParams) {
    return axios.request({
      url: "/otnNpv/sla/getCircuitList",
      method: "post",
      data: inParams,
    });
  },
  // 资源信息管理 - 列表查询
  getResourceList(inParams) {
    return axios.request({
      url: "/otnNpv/resources/list",
      method: "post",
      data: inParams,
    });
  },

  /*告警视图--end*/

  /*带宽调整--start*/

  //客户SLA-客户列表接口
  queryCusList(params) {
    return axios.request({
      url: "/gus/gbcCustomer/queryCusList",
      method: "post",
      data: params,
    });
  },

  // SLA电路列表
  getCircuitList(params) {
    return axios.request({
      url: "/sla/getCircuitList",
      method: "post",
      data: params,
    });
  },
  //根据俩核心汇聚点名称获取时延预估值
  bandwithExchange(params) {
    return axios.request({
      url: "/otnNpv/prd/bandwithExchange",
      method: "post",
      data: params,
    });
  },
  getSpeedVo(params) {
    return axios.request({
      url: `/otnNpv/prd/getSpeedVo?circuitName=${params.circuitName}`,
      method: "post",
    });
  },

  //宽带提速明细-柱状图
  getPrdbandwith(params) {
    return axios.request({
      url: "/sla/getPrdbandwith",
      method: "post",
      data: params,
    });
  },

  /*带宽调整--end*/

  /*客户SLA--start*/

  getSlaDetails(inParams) {
    return axios.request({
      url: "/otnNpv/sla/getSlaDetails",
      method: "post",
      data: inParams,
    });
  },

  //   获取当前的客户

  getCurrentCust() {
    return axios.request({
      url: "/otnNpv/user/getCurrentCust",
      method: "get",
    });
  },

  //   客户业务信息查询
  queryGbcCirInfo(params) {
    return axios.request({
      url: `/otnNpv/customerSla/queryGbcCirInfo`,
      method: "post",
      data: params,
    });
  },

  //客户SLA-性能指标
  getUnusual(params) {
    return axios.request({
      url: "/otnNpv/customerSla/getUnusual",
      method: "post",
      data: params,
    });
  },
  //异常流量电路数
  getMaxNetUse(params) {
    return axios.request({
      url: "/otnNpv/customerSla/getMaxNetUse",
      method: "post",
      data: params,
    });
  },
  //总览-客户
  historCurrAlarmNum(params) {
    return axios.request({
      url: "/gus/customerName/HistorCurrAlarmNum",
      method: "post",
      data: params,
    });
  },
  //客户SLA-网点列表接口A端网点---post
  queryCusNetA(params) {
    return axios.request({
      url: "/gus/gbcCustomer/queryCusNetA",
      method: "post",
      data: params,
    });
  },
  queryGbcRelevantInfo(params) {
    return axios.request({
      url: "/otnNpv/customerSla/queryGbcRelevantInfo",
      method: "post",
      data: params,
    });
  },

  //客户SLA-客户基本信息
  queryGbcCustomerDetail(params) {
    return axios.request({
      url: "/gus/gbcCustomer/queryGbcCustomerDetail",
      method: "post",
      data: params,
    });
  },
  //客户SLA-业务信息 业务数下专明细
  queryGbcCirInfoDetail(params) {
    return axios.request({
      url: "/gus/gbcCustomer/queryGbcCirInfoDetail",
      method: "post",
      data: params,
    });
  },
  //客户SLA-业务信息 网点数下专明细
  queryGbcCirInfoNet(params) {
    return axios.request({
      url: "/gus/gbcCustomer/queryGbcCirInfoNet",
      method: "post",
      data: params,
    });
  },

  //客户SLA-网点列表接口Z端   网点---post
  queryCusNetZ(params) {
    return axios.request({
      url: "/gus/gbcCustomer/queryCusNetZ",
      method: "post",
      data: params,
    });
  },
  //客户SLA-异常流量新增一个下专页面，按照天，周，月算一天66条的平均数，最大值，最小值
  getSlaAvgDetail(params) {
    return axios.request({
      url: "/otnNpv/customerSla/getSlaAvgDetail",
      method: "post",
      data: params,
    });
  },
  //客户SLA-性能统计（时延、抖动、丢包）正常和异常饼状图下钻各新增一个页面，统计一下一天当中有多少次的性能数据是超过阈值的，并增加正确率和异常率
  getUnusualCount(params) {
    return axios.request({
      url: "/otnNpv/customerSla/getUnusualCount",
      method: "post",
      data: params,
    });
  },
  //客户视图---流量预警中宽带利用率点击电路名称以后下钻出一个曲线图，呈现30天的最大宽带利用率曲线图
  getSlaAvgByMonth(params) {
    return axios.request({
      url: "/sla/getSlaAvgByMonth",
      method: "post",
      data: params,
    });
  },
  //客户SLA-丢包、时延抖动等指标下钻明细 下图为下钻之前的页面---post
  getUnusualDetail(params) {
    return axios.request({
      url: "/otnNpv/customerSla/getUnusualDetail",
      method: "post",
      data: params,
    });
  },
  //客户SLA-流量预警异常下钻明细----当月（最近30天）---post
  getMaxNetUseDetail(params) {
    return axios.request({
      url: "/sla/getMaxNetUseDetail",
      method: "post",
      data: params,
    });
  },
  //客户SLA-流量预警异常下钻明细----本年度---post
  getMaxNetUseDetailYear(params) {
    return axios.request({
      url: "/sla/getMaxNetUseDetailYear",
      method: "post",
      data: params,
    });
  },
  //客户SLA-流量预警异常柱状图下专明细--post
  getMaxNetCountDetail(params) {
    return axios.request({
      url: "/sla/getMaxNetCountDetail",
      method: "post",
      data: params,
    });
  },
  /** 查询该客户下所有电路及电路AZ坐标 */
  businessList(params) {
    return axios.request({
      url: `/otnNpv/customerSla/businessList?pageSize=${params.pageSize}&pageNum=${params.pageNum}`,
      method: "post",
    });
  },

  //客户SLA-业务信息 otn数量下专明细
  nodeList(params) {
    return axios.request({
      url: `/otnNpv/customerSla/nodeList?pageSize=${params.pageSize}&pageNum=${params.pageNum}`,
      method: "post",
    });
  },

  woOrderList(params) {
    return axios.request({
      url: `/otnNpv/customerSla/woOrderList?pageSize=${params.pageSize}&pageNum=${params.pageNum}&custId=${params.custId}`,
      method: "get",
    });
  },

  getTfCust(params) {
    return axios.request({
      url: `/otnNpv/customerSla/getTfCust?custId=${params.custId}`,
      method: "get",
    });
  },
  /*客户SLA--end*/

  getAlarmStat(code, name) {
    return axios.request({
      url:
        `/otnNpv/alarmPage/getAlarmStat?districtId=` +
        code +
        `&cityName=` +
        name,
      method: "post",
    });
  },
  getCircuitState(params) {
    return axios.request({
      url: `/otnNpv/alarmPage/getCircuitStat`,
      method: "post",
      data: params,
    });
  },
  getCusListTop(params) {
    return axios.request({
      url: `/otnNpv/alarmPage/getCusListTop`,
      method: "post",
      data: params,
    });
  },

  getCarouselVo() {
    return axios.request({
      url: `/otnNpv/alarmPage/getCarouselVo?custName=` + "",
      method: "post",
    });
  },
  alarmOrderPage(mainFormId) {
    return axios.request({
      url: `/otnNpv/alarmPage/alarmOrderPage?workOrderNumber=` + mainFormId,
      method: "post",
    });
  },

  manualDispatch(alarmId) {
    return axios.request({
      url: "/otnNpv/alarmPage/manualDispatch?alarmId=" + alarmId,
      method: "post",
    });
  },
  getTopologyNodes(params) {
    return axios.request({
      url: `/otnNpv/topology/getTopologyByCircuitId`,
      method: "post",
      data: params,
    });
  },
  // 资源查询
  query(classId, params) {
    return axios.request({
      url: `/otnNpv/resource/` + classId + "/query",
      method: "post",
      data: params,
    });
  },
  // 导出
  exports(classId, params) {
    return axios.request({
      url: `/otnNpv/resource/` + classId + "/export",
      method: "post",
      responseType: "blob", //格式
      data: params,
    });
  },
  queryField(url, params) {
    return axios.request({
      url: `/otnNpv` + url,
      method: "post",
      data: params,
    });
  },
  getExport(params) {
    return axios.request({
      url: `/otnNpv/alarmPage/export`,
      method: "post",
      responseType: "blob", //格式
      data: params,
    });
  },
  clearAlarm(params) {
    return axios.request({
      url: `/otnNpv/alarmPage/clearAlarmById?alarmId=` + params,
      method: "post",
    });
  },
  getAllCity() {
    return axios.request({
      url: `/otnNpv/resources/getAllCity?districtId=350002020000000000100006`,
      method: "get",
    });
  },
  getAllCounties(params) {
    return axios.request({
      url: `/otnNpv/resources/getAllCity?districtId=` + params,
      method: "get",
    });
  },

  selectAllUserInfo(params) {
    return axios.request({
      url: `/otnUser/check/selectAllUserInfo`,
      method: "post",
      data: params,
    });
  },
  updateCustAccount(params) {
    return axios.request({
      url: `/otnUser/check/updateCustAccount`,
      method: "post",
      data: params,
    });
  },
  getcustUserRoles(params) {
    return axios.request({
      url: `otnUser/check/getcustUserRoles`,
      method: "post",
      data: params,
    });
  },
  insertUserInfo(params) {
    return axios.request({
      url: `/otnUser/check/insertUserInfo`,
      method: "post",
      data: params,
    });
  },
  addRole(params) {
    return axios.request({
      url: `/otnUser/check/addRole`,
      method: "post",
      data: params,
    });
  },
  updateRole(params) {
    return axios.request({
      url: `/otnUser/check/updateRole`,
      method: "post",
      data: params,
    });
  },

  deleteRole(params) {
    return axios.request({
      url: `/otnUser/check/deleteRole`,
      method: "post",
      data: params,
    });
  },
  updateUserInfo(params) {
    return axios.request({
      url: `/otnUser/check/updateUserInfo`,
      method: "post",
      data: params,
    });
  },
  getMenu(params) {
    return axios.request({
      url: `/otnNpv/menu/getMenu`,
      method: "post",
      data: params,
    });
  },
  deleteUser(params) {
    return axios.request({
      url: `/otnUser/check/deleteUser`,
      method: "post",
      data: params,
    });
  },
  selectAllRoleInfo(params) {
    return axios.request({
      url: `/otnUser/check/selectAllRoleInfo`,
      method: "post",
      data: params,
    });
  },
  addMenu(params) {
    return axios.request({
      url: `/otnNpv/menu/add`,
      method: "post",
      data: params,
    });
  },
  // updateMenuFuncConfig(params) {
  //   return axios.request({
  //     url: `/otnNpv/menu/updateMenuFuncConfig`,
  //     method: "post",
  //     data: params
  //   });
  // },
  updateMenuFuncConfig(params) {
    return axios.request({
      url: `/otnNpv/menu/updateMenuFuncConfig`,
      method: "post",
      data: params,
    });
  },
  importMenuFuncConfig(params) {
    return axios.request({
      url: `/otnNpv/menu/importMenuFuncConfig`,
      method: "post",
      headers: { "Content-Type": "multipart/form-data" },
      timeout: 30000,
      data: params,
    });
  },
  downloadModel(params) {
    return axios.request({
      url: `otnNpv/menu/downloadModel`,
      method: "post",
      responseType: "blob", //格式
      data: params,
    });
  },
  exportMenuFuncConfig(params) {
    return axios.request({
      url: `otnNpv/menu/exportMenuFuncConfig`,
      method: "post",
      responseType: "blob", //格式
      data: params,
    });
  },
  deleteMenu(params) {
    return axios.request({
      url: `/otnNpv/menu/delete`,
      method: "post",
      data: params,
    });
  },
  modifyRelation(params) {
    return axios.request({
      url: `/otnNpv/menu/modifyRelation`,
      method: "post",
      data: params,
    });
  },
  getMenuByUser(params) {
    return axios.request({
      url: `otnNpv/menu/getMenuByUser`,
      method: "post",
      data: params,
    });
  },
  getMenuPointByUser(params) {
    return axios.request({
      url: `otnNpv/menu/getMenuPointByUser`,
      method: "post",
      data: params,
    });
  },
  updateUser(params) {
    return axios.request({
      url: `otnUser/check/updateUser`,
      method: "post",
      data: params,
    });
  },
  getTopologyConfig(params) {
    return axios.request({
      url: `otnNpv/topology/getTopologyConfig`,
      method: "post",
      data: params,
    });
  },
  updateTopologyConfig(params) {
    return axios.request({
      url: `otnNpv/topology/updateTopologyConfig`,
      method: "post",
      data: params,
    });
  },
  // 客户导出
  exportCust(params) {
    return axios.request({
      url: `/otnNpv/resources/exportCust`,
      method: "post",
      responseType: "blob", //格式
      data: params,
    });
  },
  // 电路查询
  getCircuitInfo(params) {
    return axios.request({
      url: `/otnNpv/circuit/getCircuit`,
      method: "post",
      data: params,
    });
  },
  getCircuitRate(params) {
    return axios.request({
      url: `/otnNpv/circuit/getCircuitRate`,
      method: "post",
      data: params,
    });
  },
  // 设备查询
  getEqpPort(params) {
    return axios.request({
      url: `/otnNpv/circuit/getEqpPort`,
      method: "post",
      data: params,
    });
  },
  // 线路列表 - 拓扑
  getRouteDetailNe(params) {
    return axios.request({
      url: `/otnNpv/resources/getRouteDetailNe?circuitId=` + params,
      method: "post",
    });
  },
};

export default sla;
