const path = require("path");
const resolve = (dir) => path.resolve(__dirname, dir);
// const webpack = require("webpack");

module.exports = {
  /* # # 基本路径 */
  // outputDir: process.env.OUTPUT_PATH,
  publicPath: `${process.env.PUBLIC_PATH}`,
  outputDir: "hxw-ui",
  lintOnSave: false,
  /* # 是否使用包含运行时编译器的Vue核心的构建 */
  runtimeCompiler: false,
  /* # 默认情况下 babel-loader 会忽略所有 node_modules 中的文件。
    如果你想要通过 Babel 显式转译一个依赖，可以在这个选项中列出来 */
  transpileDependencies: [],
  /* # 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。*/
  productionSourceMap: false,
  configureWebpack: (config) => {
    // 生产环境取消 console.log
    if (process.env.NODE_ENV === "production") {
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
    }
  },
  /* # webpack-dev-server 相关配置 */
  devServer: {
    port: 3001,
    proxy: {
      "/core-resource": {
        target: "http://10.93.19.26:6062/",
        pathRewrite: {
          "^/core-resource": "",
        },
      },
      "/core-network": {
        // target: 'http://192.168.1.109:9092/', // 内网测试
        // 10.217.11.69 9090
        // 10.93.19.26 6060
        target: "http://10.93.19.26:6060/", // 接口
        // target: "http://10.217.11.69:9091/", // 接口
        // target: "http://10.217.11.69:9090/", // 登录接口
        // target: "http://10.93.19.26:9596/",
        // target: "http://10.217.11.69:9092/", // 接口
        // 10.217.11.69:9092
        // 9090

        pathRewrite: {
          "^/core-network": "",
        },
      },
    },
    // true 则热更新，false 则手动刷新，默认值为 true
    inline: true,
    open: true,
    hot: true,
  },
  /* # 对内部的 webpack 配置进行更细粒度的修改。*/
  chainWebpack: (config) => {
    // 修复HMR
    config.resolve.symlinks(true);
    //修复 Lazy loading routes Error
    config.plugin("html").tap((args) => {
      args[0].chunksSortMode = "none";
      // args[0].title = "吉林联通网络运营调度系统";
      args[0].title = "核心网业务监控系统";
      args[0].timetap = new Date().getTime();
      return args;
    });

    // 添加别名
    config.resolve.alias
      .set("@", resolve("src"))
      .set("assets", resolve("src/assets"))
      .set("components", resolve("src/components"));
  },
  /* # PWA 插件相关配置 */
  pwa: {},
  /* # 第三方插件配置 */
  pluginOptions: {},
};
