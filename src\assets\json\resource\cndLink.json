{"queryConfig": {"columns": [{"displayName": "链路ID", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "id", "tableColumnName": "id"}, {"displayName": "链路类型", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "linktype", "tableColumnName": "linktype"}, {"displayName": "A端设备IP", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "adevice<PERSON>", "tableColumnName": "adevice<PERSON>"}, {"displayName": "A端设备ID", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "adevice<PERSON>", "tableColumnName": "adevice<PERSON>"}, {"displayName": "A端端口", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "aportname", "tableColumnName": "aportname"}, {"displayName": "Z端设备IP", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "zdeviceip", "tableColumnName": "zdeviceip"}, {"displayName": "Z端设备ID", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "zdeviceid", "tableColumnName": "zdeviceid"}, {"displayName": "Z端端口", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "zportname", "tableColumnName": "zportname"}], "classId": "cndLink", "className": "链路"}, "tableConfig": {"columns": [{"displayName": "链路ID", "type": "string", "columnName": "id"}, {"displayName": "链路类型", "type": "string", "columnName": "linktype"}, {"displayName": "A端设备IP", "type": "string", "columnName": "adevice<PERSON>"}, {"displayName": "A端设备ID", "type": "string", "columnName": "adevice<PERSON>"}, {"displayName": "A端端口", "type": "string", "columnName": "aportname"}, {"displayName": "Z端设备IP", "type": "string", "columnName": "zdeviceip"}, {"displayName": "Z端设备ID", "type": "string", "columnName": "zdeviceid"}, {"displayName": "Z端端口。", "type": "string", "columnName": "zportname"}], "selectColumns": ["id", "linktype", "adevice<PERSON>", "adevice<PERSON>", "aportname", "zdeviceip", "zdeviceid", "zportname"]}}