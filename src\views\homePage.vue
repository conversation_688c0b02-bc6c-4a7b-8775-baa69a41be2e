<template><!-- 内蒙古政企精品网首页 -->
<div class="nmgjpw">
  <el-row class="list-page">
    <el-col :span="24" class="hf">
      <el-menu class="el-menu-demo menuTitle" style="z-index: 999;width: 18%" :default-openeds="defaultOpenedsArray"
        v-if="colspan">
        <!-- mode="horizontal" -->
        <el-submenu index="1" class="menu">
          <template slot="title">{{ this.title }}</template>
          <el-menu-item v-for="(item, idx) in menuList" :key="idx" :title="item.title" :index="item.title"
            @click="shouq">
            <router-link :to="item.path" class="menu-title">
              <div>
                <span slot="title">
                  {{ item.title }}
                </span>
              </div>
            </router-link>
          </el-menu-item>
        </el-submenu>
      </el-menu>
      <div class="menuOcc" v-else></div>
      <div class="alarm_selector" v-if="false">
        <!-- <div class="alarm_selector" v-if="this.title == '告警监控'"> -->
        <el-menu 
          class="el-menu-demo menuTitle" 
          style="z-index: 999;width: 18%" 
          :default-openeds="defaultOpenedsArray1"
          v-if="alarmMenu"
        >
          <el-submenu index="1" class="alarm_menu">
            <template slot="title">{{ this.alarmTitle }}</template>
            <el-menu-item 
              v-for="(item, idx) in alarmList" 
              :key="idx" 
              :title="item.title" 
              :index="item.title"
              @click="showAlarm(item.idx, item.title)"
            >
              <div>
                <span slot="title">
                  {{ item.title }}
                </span>
              </div>
            </el-menu-item>
          </el-submenu>
        </el-menu>
      </div>
      <el-row>
        <el-col :span="24" class="form-header-box">
          <form-header ref="formHeader" :companyName="companyName" :custName="globalCustId"
            ></form-header>
          <div class="mini-list" v-if="showCustList === true && miniList && miniList.length > 0"
            @mouseleave="mouseLeave">
            <ul class="infinite-list" style="overflow: auto" v-if="showCustList === true">
              <li v-for="(i, idx) in miniList" :key="idx" class="list-item">
                <span v-if="i.token !== null" @click="getCustId(i)" style="color: #fff; cursor: pointer">{{ i.custId
                }}</span>
                <span v-else style="color: #000; cursor: no-drop">{{
                  i.custId
                }}</span>
              </li>
            </ul>
          </div>
          <div class="mini-list empty-list" v-if="showCustList === true && !miniList.length" @mouseleave="mouseLeave">
            <span style="color: #d0d0d0"> 暂无数据 </span>
          </div>
        </el-col>
        <el-col :span="24" v-loading="loginLoading" element-loading-text="拼命加载中..." class="views">
          <router-view></router-view>
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</div>
</template>

<script>
export default {
  name: "HomePage",
  data() {
    return {
      colspan: true,
      defaultOpenedsArray: [],
      title: this.$route.meta.title,
      menuCol: 3,
      rightCol: 21,
      navTitle: "用户报告",
      detailInfo: {},
      leftH: "",
      miniList: [],
      companyName: "",
      showMenu: true, // 控制左侧菜单展开收起
      showDetail: null, // 控制是否显示详情
      showCustList: false, // 控制是否显示右上角客户列表
      globalCustId: null, // 当前选中客户ID
      loginLoading: false,
      lazy: true, // 懒加载
      globalToken: "",
      curLine: "",
      isCust: true, // 控制时延地图渲染默认点
      menuList: [
        { title: "拓扑管理", idx: "topoPage", path: "/topoPage" },
        { title: "资源管理", idx: "resource", path: "/resource" },
        { title: "告警监控", idx: "AlarmMonitoring", path: "/AlarmMonitoring" },
        { title: "系统管理", idx: "systemManagement", path: "/systemManagement" },
      ],
      // 记得改登录页面的数据
      menuListM: ['首页', '资源管理', '告警监控', '工单管理', '拓扑管理', '运营管理', '系统管理'],
      alarmMenu: true,
      alarmList: [ 
        {title: "数通资源告警", idx: "cndResourceAlarm",},
        {title: "传输资源告警", idx: "trsResourceAlarm",},
        {title: "数通资源性能告警-板卡", idx: "cndPropertyAlarm",},
        {title: "数通资源性能告警-端口", idx: "trsPropertyAlarm",}
      ],
      alarmTitle: '数通资源告警',
      defaultOpenedsArray1: []
    };
  },
  watch: {
    $route(val) {
      this.title = val.meta.title
    }
  },
  created() {
    this.getMenu()
  },
  mounted() {


    if (this.$route.query.ticket) {
      this.getCustList(); // 登录校验，获取客户列表
    } else {
      // if (!localStorage.getItem("nmgjpwToken")) {
      //   this.goErrorPage();
      // } else {
      this.lazy = false;
      // this.$refs.formBody.refresh();
      // this.$refs.formHeader.getAlarmCycleTime();
      // }
    }
    // 监听左侧菜单展开收起
    this.$watch(
      () => {
        return this.$refs.formHeader.showMenu;
      },
      (val) => {
        this.showMenu = val;
        if (val === true) {
          this.menuCol = 3;
          this.rightCol = 21;
          this.menuIcon = "ico el-icon-s-fold cp";
        } else {
          this.menuCol = 1;
          this.rightCol = 23;
          this.menuIcon = "ico el-icon-s-unfold cp";
        }
      }
    );
    // 监听右上角客户菜单展开收起
    this.$watch(
      () => {
        return this.$refs.formHeader.showCustList;
      },
      (val) => {
        this.showCustList = val;
      }
    );
  },
  methods: {
    showAlarm(val,val2){
      this.$bus.$emit("typeYY", val)
      this.alarmTitle = val2
      this.defaultOpenedsArray1 = [];

    },
    // 大屏模式
    showFull(msg) {
      // console.log(msg);
      this.colspan = msg;
      // if (this.colspan) {
      //   this.itxstAll = "itxstM";
      // } else {
      //   this.itxstAll = "itxstAll";
      // }
    },
    getMenu() {
      this.$api.slaApi.getMenuByUser({
        type: '0',
        "pageNum": 1,
        "pageSize": 10,
      })
        .then(res => {
          let _this = this;
          res.data.sort((prev, next) => {
            return _this.menuListM.indexOf(prev.title) - _this.menuListM.indexOf(next.title)
          })
          this.menuList = res.data;
          
        })
    },
    shouq() {
      this.defaultOpenedsArray = [];
    },
    /**
     * <AUTHOR>
     * 切换刷新左侧菜单
     */
    refreshMenu(e) {
      this.showDetail = e;
    },
    /**
     * <AUTHOR>
     * 资源信息管理列表 跳转SLA查看性能详情
     */
    jumpSla(obj) {
      let rowData = obj.rowData;
      let title = obj.title;
      this.$refs.leftMenu.handleSelect({ idx: "2", title: title });
      this.curLine = obj.rowData.circuitCode;
      this.detailInfo = rowData;
      this.showDetail = false;
    },
    /**
     * <AUTHOR>
     * 点击告警消息列表，跳转告警监控
     */
    // jumpAlarmMonitor(text) {
    //   this.$refs.leftMenu.handleSelect({ idx: "5", title: text });
    // },
    /**
     * <AUTHOR>
     * SLA线路列表 点击查看详情跳转资源信息管理中对应线路详情
     */
    jumpDetail(obj) {
      // console.log('obj', obj);
      this.detailInfo = {};
      this.$refs.formDetail.detailLoading = true;
      let timer1 = setTimeout(() => {
        this.$refs.leftMenu.handleSelect({ idx: "1", title: obj.title });
        localStorage.setItem("resourceName", obj.rowData.bizType);
        this.$refs.formBody.resourceName = obj.rowData.bizType;
        this.$refs.formBody.searchData.circuitCode = obj.rowData.bizType;
        this.$refs.formBody.getListInter("", "search");
        clearTimeout(timer1);
      }, 1000);
      let timer2 = setTimeout(() => {
        if (
          this.$refs.formBody.tableData &&
          this.$refs.formBody.tableData.length > 0
        ) {
          let row = { ...this.$refs.formBody.tableData[0] };
          this.$refs.formBody.goDetail(row);
        }
        clearTimeout(timer2);
      }, 3500);
      let timer3 = setTimeout(() => {
        this.$refs.formDetail.detailLoading = false;
        clearTimeout(timer3);
      }, 3000);
      this.navTitle = obj.title;
      this.showDetail = true;
    },
    /**
     * <AUTHOR>
     * 切换左侧菜单
     */
    menuChange() {
      this.navTitle = this.$refs.leftMenu.title;
    },
    /**
     * <AUTHOR>
     * 详情
     */
    isDetail(obj) {
      this.showDetail = true;
      this.detailInfo = obj.row;
    },
    /**
     * <AUTHOR>
     * 获取sla页面高度，动态改变菜单高度
     */
    getH(val) {
      this.leftH = val + "px";
    },
    /**
     * <AUTHOR>
     * 显示 / 隐藏详情
     */
    changeShowDetail(val) {
      this.showDetail = val;
    },
    /**
     * <AUTHOR>
     * 校验登录状态，获取客户列表
     */
    async getCustList() {
      this.loginLoading = true;
      this.$api.slaApi
        .checkTokenByTicketId(this.$route.query.ticket)
        .then((res) => {
          let { resultCode, data } = res;
          if (resultCode == 200) {
            this.$store.dispatch("changeUserInfo", data);
            console.log("设置changeUserInfo", data);
            // 客户列表、客户名称赋值
            this.miniList = data.nmCustUserDtoList;
            this.loginLoading = false;
            this.companyName = data.companyName ? data.companyName : "";
            // 将客户列表第一条对应的客户 id 和 token 放入缓存
            if (data.nmCustUserDtoList && data.nmCustUserDtoList.length > 0) {
              let nmCustUserDtoList = data.nmCustUserDtoList[0];
              this.globalCustId = nmCustUserDtoList.custId;
              this.globalToken = nmCustUserDtoList.token;
              let oldToken = localStorage.getItem("nmgjpwToken");
              localStorage.setItem("nmgjpwCustId", this.globalCustId);
              if (oldToken !== this.globalToken) {
                localStorage.setItem("nmgjpwToken", this.globalToken);
                let timer = setTimeout(() => {
                  this.lazy = false;
                  // this.$refs.formBody.refresh();
                  // this.$refs.formHeader.getAlarmCycleTime();
                  clearTimeout(timer);
                }, 500);
              }
            }
          } else {
            this.$message.error({
              showClose: true,
              message: "系统检测到当前用户登录失效，请联系管理员!",
              duration: 3000,
            });
            this.$router.push({ name: "error" });
          }
        });
    },
    /**
     * <AUTHOR>
     * 选择客户，获取token
     */
    getCustId(item) {
      this.globalCustId = item.custId;
      this.globalToken = item.token;
      let oldToken = localStorage.getItem("nmgjpwToken");
      if (oldToken !== this.globalToken) {
        localStorage.setItem("nmgjpwCustId", item.custId);
        localStorage.setItem("nmgjpwToken", item.token);
        // this.refreshPages();
      }
      this.$refs.formHeader.showCustList = false;
    },
    /**
     * <AUTHOR>
     * 返回错误页
     */
    goErrorPage() {
      this.$message.error({
        showClose: true,
        message: "系统检测到当前用户登录失效，请联系管理员!",
        duration: 3000,
      });
      this.$router.push({ name: "error" });
    },
    /**
     * <AUTHOR>
     * 鼠标移出菜单
     */
    mouseLeave() {
      this.showCustList = false;
    },
    /**
     * <AUTHOR>
     * 刷新
     */
    refreshPages() {
      if (this.navTitle === "资源信息管理") {
        this.$refs.formBody.refresh();
      } else if (this.navTitle === "用户报告") {
        this.$refs.nmgsla.refresh();
      } else if (this.navTitle === "告警视图") {
        this.$refs.shanxisla.init();
      } else if (this.navTitle === "带宽调整") {
        this.$refs.bandwidth.refresh();
      } else if (this.navTitle === "客户SLA") {
        this.$refs.customersla.init();
      } else if (this.navTitle === "网络SLA") {
        this.$refs.network.init();
      } else if (this.navTitle === "时延地图") {
        this.$refs.syMap.refresh();
      } else if (this.navTitle === "拓扑") {
        console.log(this.navTitle);
        this.$refs.topoPage.refresh();
      } else if (this.navTitle === "数据总览页") {
        this.$refs.overviewPage.refresh();
      }
    },
  },
};
</script>

<style scoped lang="less">
.menuOcc {
  height: 60px;
  width: 100%;
}

.nmgjpw {
  // background: #ffffff;
  width: 100%;
  height: 100%;
  color: #000;
  // background: url("../assets/images/bg_bg.png") center center
  //           no-repeat;
  //         background-size: 100% 100%;
  background: #040b1d url(../assets/images/line_bg.png) no-repeat 40% 42%;
  background-size: 100% 100%;

  .list-page{
    .hf{
      position: relative;
      .alarm_selector{
        position: absolute;
        right: 15%;
        top: 0%;
        color: wheat;
        .alarm_menu {
          background: url("../assets/header/menuBg.png") center center no-repeat;
          background-size: 100% 100%;
          width: 270px;
          height: 45px;
          margin-left: 20px;
          margin-top: 50px;
          border: none;

          font-size: 22px;
          font-weight: 400;

          ::v-deep .el-submenu__title {
            height: 45px !important;
            line-height: 45px !important;
            color: #fff !important;
            border: none;
            font-size: 20px;

            &:hover {
              color: #fff;
              background: rgba(15, 52, 124, 0);

              .star {
                color: #333333;
              }
            }

            &.is-active {
              color: #fff;
              background: rgba(15, 52, 124, 0);

              & span {
                color: #ffffff;
              }

              & .star {
                color: #ffffff;
              }

              & :hover .star {
                color: #ffffff;
              }
            }
          }

          .menu-title {
            text-decoration: none;
            font-size: 20px;
            font-family: PangMenZhengDao;
            font-weight: 400;
            color: #FFFFFF;
          }

          ::v-deep .el-menu-item {
            // background-color: RGBA(3, 48, 91, 1);
            color: #fff;
            text-align: left;

            //   border-bottom: 1px rgb(7 13 26) solid;
            // border-radius: 5px;
            &:hover {
              color: #fff;
              background: #0f347c57;

              .star {
                color: #333333;
              }
            }

            &.is-active {
              color: #fff;
              background: #0f347c57;

              & span {
                color: #ffffff;
              }

              & .star {
                color: #ffffff;
              }

              & :hover .star {
                color: #ffffff;
              }
            }

            .menuHeader {
              display: block;
              width: 100%;
              height: 100%;
              line-height: 45px;
              color: #fff;
              font-size: 18px;
              padding-left: 15px;
            }
          }

          ::v-deep .el-menu {
            border: none;
            // border-left: 1px solid #ddd;
            // border-image: linear-gradient(rgba(0, 72, 154, 1),rgba(0, 204, 255, 1)) 30 30;
            // border-right: 1px solid #ddd;
            // border-image: linear-gradient(rgba(0, 72, 154, 1),rgba(0, 204, 255, 1)) 30 30;
            // border-bottom: 1px solid rgba(0, 204, 255, 1);
            background: url("../assets/images/xlbg.png") center center no-repeat;
            background-size: 100% 100%;
          }

          ::v-deep .el-icon-arrow-down:before {
            content: ''
          }

          ::v-deep .el-submenu__title i {
            width: 40px;
            height: 40px;
            background: url("../assets/images/jt.png") center center no-repeat;
            background-size: 100% 100%;
          }

          ::v-deep .el-submenu__icon-arrow {
            position: absolute;
            top: 27%;
            right: 0px;
            margin-top: -7px;
            transition: transform .3s;
            font-size: 12px;
          }

          ::v-deep .el-submenu.is-opened>.el-submenu__title .el-submenu__icon-arrow {
            transform: rotateZ(90deg) !important;
          }
        }
      }
    }
  }

  .form-header-box {
    position: relative;

    .mini-list {
      position: absolute;
      top: 50px;
      right: 5px;
      color: #ffffff;
      background: rgba(0, 0, 0, 0.75);
      z-index: 10;
      width: 300px;
      height: 200px;
      border: 1px solid rgba(0, 0, 0, 0.75);
      border-radius: 5px;
      padding: 10px 16px;
      overflow-y: scroll;

      .list-item {
        display: inline-flex;
        width: 100%;
        text-align: center;
        justify-content: space-around;
        line-height: 30px;
        font-size: 14px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.25);

        &:hover {
          border-radius: 15px;
          background: rgba(255, 255, 255, 0.1);
        }

        span {
          width: 100%;
          height: 100%;
        }
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.2);
      }

      &::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.95);
      }

      &.empty-list {
        overflow: unset;
        width: 160px;
        height: 90px;
        line-height: 90px;
        text-align: center;
        padding: 0;
        font-size: 16px;
      }
    }
  }
}

.views {
  // margin-top: 5%;
  // height: 90%;
}

.menuTitle {
  background-color: transparent;
  border: none;
}

.menu {
  background: url("../assets/header/menuBg.png") center center no-repeat;
  background-size: 100% 100%;
  width: 200px;
  height: 45px;
  margin-left: 20px;
  margin-top: 50px;
  border: none;

  font-size: 22px;
  font-weight: 400;

  ::v-deep .el-submenu__title {
    height: 45px !important;
    line-height: 45px !important;
    color: #fff !important;
    border: none;
    font-size: 20px;

    &:hover {
      color: #fff;
      background: rgba(15, 52, 124, 0);

      .star {
        color: #333333;
      }
    }

    &.is-active {
      color: #fff;
      background: rgba(15, 52, 124, 0);

      & span {
        color: #ffffff;
      }

      & .star {
        color: #ffffff;
      }

      & :hover .star {
        color: #ffffff;
      }
    }
  }

  .menu-title {
    text-decoration: none;
    font-size: 20px;
    font-family: PangMenZhengDao;
    font-weight: 400;
    color: #FFFFFF;
  }

  ::v-deep .el-menu-item {
    // background-color: RGBA(3, 48, 91, 1);
    color: #fff;
    text-align: left;

    //   border-bottom: 1px rgb(7 13 26) solid;
    // border-radius: 5px;
    &:hover {
      color: #fff;
      background: #0f347c57;

      .star {
        color: #333333;
      }
    }

    &.is-active {
      color: #fff;
      background: #0f347c57;

      & span {
        color: #ffffff;
      }

      & .star {
        color: #ffffff;
      }

      & :hover .star {
        color: #ffffff;
      }
    }

    .menuHeader {
      display: block;
      width: 100%;
      height: 100%;
      line-height: 45px;
      color: #fff;
      font-size: 18px;
      padding-left: 15px;
    }
  }

  ::v-deep .el-menu {
    border: none;
    // border-left: 1px solid #ddd;
    // border-image: linear-gradient(rgba(0, 72, 154, 1),rgba(0, 204, 255, 1)) 30 30;
    // border-right: 1px solid #ddd;
    // border-image: linear-gradient(rgba(0, 72, 154, 1),rgba(0, 204, 255, 1)) 30 30;
    // border-bottom: 1px solid rgba(0, 204, 255, 1);
    background: url("../assets/images/xlbg.png") center center no-repeat;
    background-size: 100% 100%;
  }

  ::v-deep .el-icon-arrow-down:before {
    content: ''
  }

  ::v-deep .el-submenu__title i {
    width: 40px;
    height: 40px;
    background: url("../assets/images/jt.png") center center no-repeat;
    background-size: 100% 100%;
  }

  ::v-deep .el-submenu__icon-arrow {
    position: absolute;
    top: 27%;
    right: 20px;
    margin-top: -7px;
    transition: transform .3s;
    font-size: 12px;
  }

  ::v-deep .el-submenu.is-opened>.el-submenu__title .el-submenu__icon-arrow {
    transform: rotateZ(90deg) !important;
  }
}
</style>
