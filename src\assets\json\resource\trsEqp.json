{"queryConfig": {"columns": [{"displayName": "网元名称", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "eqpName", "tableColumnName": "eqpName"}, {"displayName": "所属行政区域", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "districtName", "tableColumnName": "districtName"}], "classId": "trsEqp", "className": "传输网元"}, "tableConfig": {"columns": [{"displayName": "网元ID", "type": "string", "columnName": "id"}, {"displayName": "网元名称", "type": "string", "columnName": "eqpName"}, {"displayName": "网元编码", "type": "string", "columnName": "eqpNo"}, {"displayName": "网元类型", "type": "string", "columnName": "resType"}, {"displayName": "厂家", "type": "string", "columnName": "mfr"}, {"displayName": "网络级别", "type": "string", "columnName": "networkLevel"}, {"displayName": "网络层次", "type": "string", "columnName": "netLevel"}, {"displayName": "所属管理区域", "type": "string", "columnName": "regionName"}, {"displayName": "所属行政区域", "type": "string", "columnName": "districtName"}, {"displayName": "所属安置点", "type": "string", "columnName": "posit"}, {"displayName": "所在安置地点", "type": "string", "columnName": "inPosit"}, {"displayName": "产权性质", "type": "string", "columnName": "propChar"}, {"displayName": "产权归属", "type": "string", "columnName": "propertyBelong"}, {"displayName": "创建人", "type": "string", "columnName": "createOp"}, {"displayName": "创建时间", "type": "string", "columnName": "createDate"}, {"displayName": "修改人", "type": "string", "columnName": "modifyOp"}, {"displayName": "修改时间", "type": "string", "columnName": "modifyDate"}], "selectColumns": ["eqpName", "posit", "networkLevel", "netLevel", "regionName", "districtName", "resType", "mfr", "eqpNo", "inPosit", "propChar", "propertyBelong", "createOp", "createDate", "modifyOp", "modifyDate"]}}