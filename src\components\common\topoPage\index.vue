<template>
  <!-- 内蒙古SLA -->
  <div class="newIndexTopo">
    <div class="content">
      <el-table
        :data="tableList"
        :header-cell-style="{
          color: '#73777A',
          'background-color': '#F0F2F5',
          'text-align': 'center',
        }"
        v-loading="loading"
        element-loading-text="拼命加载中..."
        class="table-part fs18 wf"
        @row-click="rowClick"
        :rowKey="(record) => record.index"
        :row-class-name="setRowClassName"
        :show-overflow-tooltip="true"
      >
        <!-- 列渲染 -->
        <el-table-column
          v-for="(i, idx) in topColumns"
          :key="'col' + idx"
          :prop="i.prop"
          :label="i.label"
          :align="i.align"
          :width="i.width"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <div>
              <span
                v-if="i.prop === 'num' && scope.row.num !== null"
                :style="
                  Math.round(parseFloat(scope.row.num) * 1000) / 1000 >=
                  custPercent
                    ? 'color: red;'
                    : ''
                "
              >
                <i
                  class="el-icon-warning"
                  v-if="
                    scope.row.num &&
                    Math.round(scope.row.num * 1000) / 1000 >= custPercent
                  "
                ></i>
                <span class="ml10">{{
                  scope.row.num
                    ? Math.round(parseFloat(scope.row.num) * 1000) / 1000 + `%`
                    : null
                }}</span>
                <!-- <span> {{ scope.row[i.prop] ? scope.row[i.prop] : "" }} </span> -->
              </span>
              <span
                v-else
                :style="
                  scope.row.num &&
                  Math.round(parseFloat(scope.row.num) * 1000) / 1000 >=
                    custPercent
                    ? 'color: red;'
                    : ''
                "
                >{{ scope.row[i.prop] ? scope.row[i.prop] : "" }}</span
              >
            </div>
          </template>
        </el-table-column>

        <div slot="empty">
          <i class="el-icon-warning fs16"></i>
          <span class="ml10 fs16">暂无数据！</span>
        </div>
      </el-table>
      <div class="paginationbox" style="text-align: right">
        <el-pagination
          style="padding: 15px 0"
          background
          @current-change="handleDetailCurrentChange"
          :current-page.sync="pageNum"
          :page-size="pageSize"
          layout="prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
      <div class="topo-cls">
        <topo :sendData="nodeData" :selRow="selRow"></topo>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "topoPage",
    components: {},
    props: {
      curLine: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        loading: false,
        tableList: [],
        columns: [],
        clickId: "",
        selRow: {},
        pageSize: 20,
        pageNum: 1,
        total: 0,
        nodeData: [],
        showTopo: false,
        topColumns: [
          // 列名字段配置
          {
            prop: "id",
            label: "序号",
            align: "center",
          },
          {
            prop: "circuitName",
            label: "业务名称",
            align: "center",
          },
          {
            prop: "serialNumber",
            label: "业务号码",
            align: "center",
          },
          {
            prop: "custName",
            label: "客户名",
            align: "center",
          },
          {
            prop: "tunnelName",
            label: "通道ID",
            align: "center",
          },
        ],
        custPercent: 100,
      };
    },
    computed: {},
    watch: {
      selRow: {
        handler(n, o) {
          this.getTopologyNode(n.tunnelName);
        },
      },
    },
    created() {},
    mounted() {
      this.getTableData();
    },
    methods: {
      async getTopologyNode(tunnelName) {
        let req = await this.$api.slaApi.getTopologyNode({
          tunnelName,
        });
        if (req.resultCode === 200) {
          let dataList = req.data || [];
          this.nodeData = dataList;
        } else {
          this.$message.error(req?.message);
        }
      },
      rowClick(row) {
        this.clickId = row.id;
        this.selRow = row;
      },
      setRowClassName({ row }) {
        if (this.clickId === row.id) {
          return "custRow";
        }
      },
      handleDetailCurrentChange(val) {
        this.pageNum = val;
        this.getTableData();
      },
      // 获取拓扑表格数据
      async getTableData() {
        let req = await this.$api.slaApi.getTopologyList({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        if (req.resultCode === 200) {
          let curList = req?.data || [];
          this.tableList = curList.map((item, index) => {
            return {
              id: index + 1,
              ...item,
            };
          });
          this.total = req?.total || 0;
        } else {
          this.$message.error(req?.message);
        }
      },
    },
  };
</script>

<style lang="less" scoped>
  .newIndexTopo {
    width: 100%;
    height: 100vh;
    background: #f2f2f2;
    overflow: hidden;
    position: relative;
    .content {
      margin-left: 16px;
      padding-top: 80px;
      .paginationbox {
        background-color: #fff;
      }

      ::v-deep {
        .el-table {
          thead {
            font-size: 14px;
            font-family: "PingFangSC-Regular, PingFang SC";
            color: #73777a;
            th {
              background-color: #fff !important;
            }
          }
          .el-table__body-wrapper {
            overflow-y: scroll;
            overflow-x: hidden;
            position: relative;
            max-height: 220px;
            .el-table__row {
              background: #ffffff;
            }
            .el-table__body {
              tr {
                cursor: pointer;
                &:hover {
                  .el-table__cell {
                    color: #606266;
                    background: #fff2ea;
                  }
                }
                &.el-table__row {
                  td:hover {
                    color: #373d41;
                    background: #fff2ea;
                  }
                  &.custRow {
                    td {
                      background: #06529d;
                      &:hover {
                        color: #ffffff;
                        background: #06529d;
                      }
                      span {
                        color: #ffffff !important;
                      }
                    }
                  }
                }
              }
            }
          }
          .cell {
            font-weight: 500;
            font-size: 14px;
          }
          .el-table__empty-block {
            min-height: 200px;
          }
        }
        .el-loading-spinner {
          .el-loading-text,
          .path {
            color: #06529d;
            stroke: #06529d;
          }
        }
        .red {
          color: red;
        }
      }
    }

    .topo-cls {
      margin-top: 12px;
      background-color: #fff;
    }
  }
</style>
<style lang="less">
  .cust-date {
    .el-picker-panel__icon-btn:hover {
      color: #06529d;
    }

    td {
      &.current:not(.disabled) span {
        color: #ffffff;
        background: #06529d !important;
      }

      &.today.current:not(.disabled) span {
        color: #ffffff;
      }
    }

    .available {
      &.today span,
      &.current span,
      &:hover span {
        color: #06529d;
      }

      &.in-range {
        & div {
          background: #f9f6f3;

          & span {
            color: #06529d;
          }
        }

        &.today span {
          color: #06529d;
        }

        &.start-date span,
        &.end-date span {
          color: #ffffff;
          background: #06529d;
        }
      }
    }
  }
</style>
