kind: ConfigMap
apiVersion: v1
metadata:
  name: nmjpw-web-nginx-config
  namespace: nmjpw
  selfLink: /api/v1/namespaces/nmjpw/configmaps/nmjpw-web-nginx-config
  labels:
    app: nmjpw-web-nginx-config
    chart: nginx-1.20.1
    heritage: Helm
    release: nmjpw-web
  managedFields:
    - manager: Go-http-client
      operation: Update
      apiVersion: v1
      time: '2021-10-27T03:23:54Z'
      fieldsType: FieldsV1
    - manager: dashboard
      operation: Update
      apiVersion: v1
      time: '2022-04-28T02:14:41Z'
      fieldsType: FieldsV1
      fieldsV1:
        'f:data':
          'f:nginx.conf': { }
data:
  nginx.conf: |-
    user  nginx;
    worker_processes  32;
    error_log  /var/log/nginx/error.log warn;
    pid        /var/run/nginx.pid;
    events {
        worker_connections  1024;
    }
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
        vhost_traffic_status_filter_by_host off;
        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for"';
        access_log  /var/log/nginx/access.log  main;
        sendfile        on;
        #tcp_nopush     on;
        keepalive_timeout 75;
        #gzip  on;;
        proxy_buffer_size       16k;
        proxy_buffers           4 64k;
        proxy_busy_buffers_size 128k;
        proxy_temp_file_write_size 128k;
        client_max_body_size 50m;
      server {
        listen 8080;
        #charset koi8-r;
        #access_log  /var/log/nginx/host.access.log  main;

        location / {
          root    /usr/local/nginx/www/;
          index  index.html index.htm;
        }

        location /api/ {
          proxy_pass http://*************:9090/;
          proxy_set_header x-forwarded-for  $remote_addr;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
          root   /usr/share/nginx/html;
        }
      }
    }
