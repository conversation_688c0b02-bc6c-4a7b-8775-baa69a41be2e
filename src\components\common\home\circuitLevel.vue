<template>
  <div>
    <div class="main-border main-all">
      <nav-bar :title="title"></nav-bar>
      <div
        id="barcharts"
        v-loading="uploading"
        element-loading-text="数据加载中"
        element-loading-background="rgba(0, 0, 0, 0)"
      />
    </div>
  </div>
</template>

<script>
import echarts from "echarts";
import navBar from "@/components/common/home/<USER>";
export default {
  name: "circuitLevel",
  components: { navBar },
  props: {
    height: {
      type: String,
      default: () => "",
    },
    cityUuid: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      pHeight: "36.8vh",
      title: "电路级别统计",
      xData: [8,5,4,7,3,2,1,6],
      yData: [],
      barTypeList: [
        {label: '4级', value: '', key: 'FOURTH'},
        {label: '4级纵向', value: '', key: 'FOURTH_DIRECTION'},
        {label: '4级横向', value: '', key: 'FOURTH_TRANSVERSE'},
        {label: '3级纵向', value: '', key: 'THIRD_DIRECTION'},
        {label: '3级横向', value: '', key: 'THIRD_TRANSVERSE'},
        {label: '3级骨干网', value: '', key: 'THIRD_BACKBONE'},
        {label: '2级骨干网', value: '', key: 'SECOND_BACKBONE'},
        {label: '自治区本级横向', value: '', key: 'THIS_TRANSVERSE'}
      ]
    };
  },
  watch: {
    height(n) {
      if(n) {
        this.pHeight = n;
      }
    },
    cityUuid(n) {
      if(n) {
        this.getBarData();
      }
    }
  },
  mounted() {
    this.pHeight = this.height;
    this.getBarData();
    let initYData = [];
    this.barTypeList.map(i => {
      initYData.push(i.label);
    })
    this.yData = initYData;
    setTimeout(() => {
      const resizeOb = new ResizeObserver((entries) => {
        for (const entry of entries) {
          echarts.getInstanceByDom(entry.target).resize();
        }
      });
      resizeOb.observe(document.getElementById("barcharts"));
    }, 10);
  },
  methods: {
    /**
     * <AUTHOR>
     * 获取不同电路级别的电路数量 /kams/homepage/getCircuitLevelNum
     */
    getBarData() {
      let xData = [];
      let yData = [];
      nc.rapi
        .request({
          url: "/rc-rm-kams-biz/kams/homepage/getCircuitLevelNum",
          method: "post",
          data: {
            districtUuid: this.cityUuid ? this.cityUuid : '',
          }, // 参数
          headers: {
            "Content-Type": "application/json;charset=UTF-8",
          },
        })
        .then((result) => {
          if(Object.keys(result.data).length>0) {
            let res = result.data;
            this.barTypeList.map(i => {
              Object.keys(res).map(j => {
                if(i.key == j) {
                  i.value = res[i.key];
                }
              })
              xData.push(i.value);
              yData.push(i.label);
              this.xData = xData;
              this.yData = yData;
            })
          }
          this.barcharts();
        })
        .catch((err) => {
          console.error(err);
          scope.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: "数据加载失败，系统异常!",
          });
        });
    },
    barcharts() {
      var myChart = echarts.init(document.getElementById("barcharts"));
      let option = {
        title: {
          text: "",
          subtext: "",
        },
        grid: {
          left: "3%",
          right: "5%",
          bottom: "0%",
          top: "2%",
          containLabel: true,
        },
        xAxis: [
          {
            show: false,
            type: "value",
            position: "top",
            splitLine: { show: false },
          },
        ],
        yAxis: [
          {
            triggerEvent: true,
            type: "category",
            data: this.yData,
            axisLine: { show: false }, //坐标轴
            axisTick: [
              { //坐标轴小标记
                show: false,
              },
            ],
            axisLabel: {
              show: true,
              inside: true,
              interval: 0,
              fontSize: 12,
              height: 10,
              lineHeight: 0,
              verticalAlign: "bottom",
              textStyle: {
                color: "#fff",
                align: "left",
                padding: [30, 0, 13, 0],
              },
            },
          },
        ],
        series: [
          {
            name: "",
            type: "bar",
            tooltip: { show: false },
            barWidth: 10, //柱宽度
            barGap: '100%', //柱图间距
            data: this.xData,
            showBackground: {
              show: true,
            }, //开启背景
            backgroundStyle: {
              //设置背景样式
              color: " rgba(91, 91, 91, 0.36)",
              barBorderRadius: 10,
            },
            itemStyle: {
              normal: {
                barBorderRadius: 7,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#0A9ae0",
                  },
                  {
                    offset: 1,
                    color: "#26d5dd",
                  },
                ]),
                label: {
                  show: true, //显示文本
                  position: [250, 0],
                  valueAnimation: true,
                  textStyle: {
                    color: "#fff",
                    fontSize: 12,
                  },
                },
              },
            },
          },
        ],
      };

      option && myChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.main-border {
  // height: 36.8vh;
  padding-bottom: 1vh;
  border: 1px solid rgba(4, 56, 226, 0.58);
  margin-top: 1vh;
  width: 100%;
  height: 100%;
  #barcharts {
    min-height: 32.8vh;
  }
}
</style>