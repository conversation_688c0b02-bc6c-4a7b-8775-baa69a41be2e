import axios from "axios";
import { Message } from "element-ui";
import core from "./core";

class HttpRequest {
  /**
   * axios 构造器
   * @param baseUrl
   */
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.fileName = "";
    this.ignoreApis = ["/otnUser/login", "/otnUser/check/checkTicketId"];
  }

  getInsideConfig() {
    return {
      baseURL: this.baseUrl,
      headers: {
        "Content-Type": "application/json;charset=utf-8",
      },
    };
  }

  interceptors(instance) {
    // 请求拦截器
    const cores = new core();
    instance.interceptors.request.use(
      (req) => {
        const { url, method } = req;
        if (req.fileName) {
          this.fileName = req.fileName;
        }
        const pathName = cores.getPathName(url);
        if (!cores.checkApiIsIgnore(this.ignoreApis, pathName, method)) {
          req.headers["token"] = localStorage.getItem("nmgjpwToken");
        }
        return req;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    // 返回拦截器
    instance.interceptors.response.use(
      (res) => {
        // if (res.data.size) {
        //   const file = new Blob([res.data], {
        //     type: "application/vnd.ms-excel",
        //   });
        //   const url = URL.createObjectURL(file);
        //   const a = document.createElement("a");
        //   a.href = url;
        //   a.download = this.fileName;
        //   a.click();
        //   return Promise.resolve("导出成功！");
        // }
        const { resultCode } = res.data;
        const message = res?.data?.message || "";
        if (resultCode == 502) {
          Message(message);
          window.location.href = "/#/error";
          if (message.includes("令牌过期")) {
            window.location.href = "/";
          } else {
            window.location.href = "/#/error";
          }
        }
        return Promise.resolve(res.data);
      },
      (error) => {
        const { response } = error;
        if (response && response.data) {
          const { status, message } = response.data;
          switch (status) {
            case 403:
              Message("您当前的访问未授权");
              break;
            case 400:
              Message("当前请求无效");
              break;
            case 404:
              Message("404 当前服务不可用");
              break;
            default:
              Message("当前服务不可用");
              break;
          }
        }
        return Promise.reject(error);
      }
    );
  }

  request(options) {
    const instance = axios.create({
      // timeout: 20000,
    });
    options = Object.assign(this.getInsideConfig(), options);
    this.interceptors(instance, options.url, options.hideLoading);
    return instance(options);
  }
}

export default HttpRequest;
