{"name": "nmjpw_web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build --mode=production", "build-gae": "vue-cli-service build --mode=gaeproduction", "build-test": "vue-cli-service build --mode=test", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/g6": "^4.8.23", "@jyeontu/jvuewheel": "^0.7.7", "axios": "^0.21.1", "core-js": "^3.6.5", "d3": "^7.8.5", "echarts": "^4.9.0", "echarts-gl": "^1.1.1", "element-ui": "^2.15.6", "jsencrypt": "^3.3.1", "leader-line": "^1.0.7", "moment": "^2.29.4", "sortablejs": "^1.15.6", "store": "^2.0.12", "swiper": "^4.5.1", "three": "^0.160.1", "vue": "^2.6.11", "vue-ls": "^4.0.0", "vue-router": "3.0", "vue-seamless-scroll": "^1.1.23", "vue-splitpane": "^1.0.6", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@david-j/vue-j-scroll": "^1.2.7", "@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-service": "^4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "sass-loader": "^10.5.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}