import axios from "@/utils/api.request";
const BASEURL = '/otnNpv'
const core_network_data = {
    // 性能监控告警
    getPerformanceAlertStatistics() {
      return axios.request({
        url: `${BASEURL}/monitor/getPerformanceAlertStatistics`,
        method: "post",
      });
    },
    // 各地市告警数量排行
    getPerformanceAlertStatisticsByCity() {
      return axios.request({
        url: `${BASEURL}/monitor/getPerformanceAlertStatisticsByCity`,
        method: "post",
      });
    },
    // 告警详情-端口
    getPortPerformanceAlertDetail(inParams) {
      return axios.request({
        url: `${BASEURL}/monitor/getPortPerformanceAlertDetail`,
        method: "post",
        data: inParams,
      });
    },
    // 告警详情-板卡
    getCardPerformanceAlertDetail(inParams) {
      return axios.request({
        url: `${BASEURL}/monitor/getCardPerformanceAlertDetail`,
        method: "post",
        data: inParams,
      });
    },
}
export default core_network_data;