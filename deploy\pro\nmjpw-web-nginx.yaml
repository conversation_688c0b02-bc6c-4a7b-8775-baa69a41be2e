kind: Deployment
apiVersion: apps/v1
metadata:
  name: nmjpw-web-nginx
  namespace: fivegjpw
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nmjpw-web-nginx
      release: nmjpw-web
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: nmjpw-web-nginx
        chart: nginx-1.20.1
        heritage: Helm
        release: nmjpw-web
    spec:
      volumes:
        - name: nginx-server-block
          configMap:
            name: nmjpw-web-nginx-config
            items:
              - key: nginx.conf
                path: nginxConfPath
            defaultMode: 420
      containers:
        - name: nginx
          image: 'harbor.dcos.ncmp.unicom.local/ndcp-jl/jingpin-web-dev:#{Build.BuildNumber}#'
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 100m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 256Mi
          volumeMounts:
            - name: nginx-server-block
              mountPath: /etc/nginx/nginx.conf
              subPath: nginxConfPath
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            timeoutSeconds: 1
            periodSeconds: 20
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app: nmjpw-web-nginx
                    release: nmjpw-web
                topologyKey: kubernetes.io/hostname
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600

