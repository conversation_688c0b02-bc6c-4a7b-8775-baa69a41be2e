<template>
<div style="height: 75%;">
  <div class="nc-query">
    <el-form :inline="true" size="small" :model="form" label-width="100px">
      <el-form-item class="formItem" label="所属专业">
        <!-- <el-input v-model="form.belongSpec" placeholder="所属专业" clearable></el-input> -->
        <el-select v-model="form.belongSpec" placeholder="所属专业">
          <el-option
            v-for="item in options"
            :key="item"
            :label="item"
            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="formItem" label="网元类型">
        <el-input v-model="form.routeTypeName" placeholder="网元类型" clearable></el-input>
      </el-form-item>
      <el-form-item class="formItem" label="设备名称">
        <el-input v-model="form.eqpName" placeholder="设备名称" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item class="formItem" label="端口">
        <el-input v-model="form.portName" placeholder="端口" clearable></el-input>
      </el-form-item> -->
      <el-form-item style="float:right;">
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button type="primary" @click="selectAllUserInfo">查询</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div style="margin:0 0 15px 0">
    <el-button size="mini" :disabled='multipleSelection.length !== 1 && belongSpec !=="核心网专业"' @click="custInfor">确认</el-button>
  </div>
  <el-table v-loading="loading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)" border :data="tableBody" :header-cell-style="{
      color: '#9ED8FF',
      'background-color': 'rgba(15, 52, 124, 1)',
      'text-align': 'center',
      'font-size': '14px',
      'border-right': '1px rgba(15, 0, 137, .8) solid'
    }" height="380" style="border-radius: 4px 4px 0 0;
      border:none;margin-top: 5px;" @selection-change="handleSelectionChange">
    <el-table-column class-name="tabSel" type="selection" width="55" align="center">
    </el-table-column>
    <el-table-column align='center' v-for="(i, idx) in tableColumns" :key="idx" :label="i.displayName"
      :prop="i.columnName" show-overflow-tooltip>
    </el-table-column>
  </el-table>
  <el-pagination class="page-list" @size-change="handleSizeChange" @current-change="handleCurrentChange"
    :current-page.sync="pageNumber" :page-sizes="[1, 10, 20, 30, 40, 50]" :page-size="pageSize" background
    layout="total, prev, pager, next, sizes" :total.sync="total" style="float: right;">
  </el-pagination>
</div>
</template>

<script>
export default {
  props: {
    equipDialog: {
      type: Boolean, //要求传递的类型,大写开头
      required: true, //强制传递,否则报错
    },
    belongSpec:{
      type: String,
      required: true
    }
  },
  watch: {
    equipDialog(val) {
      if (!val) {
        this.resetQuery()
      }
    },
    belongSpec(val) {
      this.multipleSelection=[];
      if (val) {
        this.options = val.split(",");
        this.selectAllUserInfo()
      }
    }
  },
  data() {
    return {
      loading: false,
      form: {},
      total: 10,
      pageSize: 10,
      pageNumber: 1,
      tableColumns: [
        {
        displayName: '所属专业',
        columnName: 'specialty'
      },{
        displayName: '所属区域',
        columnName: 'regionName'
      },{
        displayName: '所属机房',
        columnName: 'roomName'
      },{
        displayName: '网元类型',
        columnName: 'routeTypeName'
      }, {
        displayName: '设备名称',
        columnName: 'eqpName'
      }, 
      // {
      //   displayName: '端口',
      //   columnName: 'portName'
      // }
      ],
      tableBody: [],
      userInformation: [],
      customerAdded: [],
      noCustomerAdded: [],
      customerName: '',
      deleteCustomer: [],
      multipleSelection: [],
      options: [{
          value: '传输专业',
          label: '传输专业'
        }, {
          value: '核心网专业',
          label: '核心网专业'
        }, {
          value: '核心网数通专业',
          label: '核心网数通专业'
        }, {
          value: '数据网专业-智能城域',
          label: '数据网专业-智能城域'
        }, {
          value: '数据网专业-路由器设备',
          label: '数据网专业-路由器设备'
        }],
    }
  },
  created() {
    this.tableBody = [];
    this.options = this.belongSpec.split(",")
    this.selectAllUserInfo()
    
  },
  methods: {
    // 表格复选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    custInfor() {
      this.$emit('equipFrom', this.multipleSelection)
    },
    selectAllUserInfo() {
      this.loading = true;
      let scope = this;
      let params = {
        "belongSpec":this.form.belongSpec ? this.form.belongSpec : this.belongSpec,
        "eqpName": this.form.eqpName,
        "portName": this.form.portName,
        "routeTypeName": this.form.routeTypeName,
        "pageNum": this.pageNumber,
        "pageSize": this.pageSize
      }
      this.$api.transApi.getTransCircuitSelectDataV2(params)
      .then(res => {
        this.tableBody = res.data.records;
        this.total = res.data.total;
        this.loading = false;
      })
    },

    transformData(data) {
      let transformedData = { where: {} };
      data.forEach(item => {
        for (let key in item) {
          let value = item[key]; // 假设统一使用$like作为条件 
          transformedData.where[key] = value;
        }
      });
      return transformedData;
    },
    // 重置表单
    resetQuery() {
      this.form = {};
      this.pageSize = 10;
      this.pageNumber = 1;
      this.tableBody = [];
      this.total = 0;
      this.selectAllUserInfo();
    },
    /**
       * <AUTHOR>
       * 监听每页条数选择
       */
    handleSizeChange(val) {
      this.pageSize = val;
      this.selectAllUserInfo();
    },
    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.selectAllUserInfo();
    },
  }
}
</script>

<style lang="less" scoped>
.nc-query {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
  //   height: 10vh;
  // height: 85px;
}

/deep/ .el-checkbox__inner {
  background-color: transparent;
  border: 1px solid #02dbff;
}

::v-deep {
  .el-table__fixed-right-patch {
    background-color: rgba(23, 70, 137, 1);
  }

  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch {
    border: none;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }

  .el-dialog {
    // height: 30vh;
    // width: 60%;
    background: url("../../assets/images/dialogbg.png") center center no-repeat;
    background-size: 100% 100%;
    margin-top: 10vh !important;
  }

  .el-dialog__header {
    justify-content: left;
    background: none;
  }

  .el-dialog__title {
    justify-content: left;
    line-height: 20px;
    font-weight: 800;
    height: 20px;
    display: flex;
    color: rgba(255, 255, 255, 1);
    font-size: 22px;
  }

  .el-dialog__title::before,
  .el-dialog__title::after {
    content: none;
  }

  .el-dialog__headerbtn {
    width: 30px;
    height: 30px;
    float: right;
    top: 10px;
    right: 10px;
    background: url("../../assets/images/x.png") center center no-repeat;
    background-size: 100% 100%;
  }

  .el-dialog__body {
    height: 85%;

    .detail-info {
      max-height: 800px;
      height: 100%;
    }

    .el-form {
      min-height: 100px;
      width: 95%;
      // margin: 30px auto 20px;
      margin-top: 30px;
      text-align: center;

      .el-form-item__label {
        color: #fff;
      }

      .el-input__inner {
        color: #02dbff;
        background: none;
        border: 1px solid rgba(37, 190, 247, 0.5);
        // height: 24px !important;
        line-height: 24px;
      }

      .el-switch__label,
      .el-checkbox {
        color: #fff;
      }

      .el-button--primary {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;

        &:focus {
          background-color: #409EFF;
          border-color: #409EFF;
        }

        &:hover {
          background-color: #409EFF;
          border-color: #409EFF;
        }
      }

      .el-select-dropdown__item:hover {
        color: #409EFF;
        background: #40a0ff27;
      }

      .el-button--default {
        color: #409EFF;
        background: transparent;
        border-color: #409EFF;
      }
    }
  }
}
</style>