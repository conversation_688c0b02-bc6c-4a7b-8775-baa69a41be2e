/*
 * 描述: 封装组件库
 */

import formHeader from "./formHeader"; // 列表公共头部


const components = {
  
  formHeader,
  
  
};

const install = (Vue = {}) => {
  if (install.installed) return;
  Object.keys(components).forEach((component) => {
    Vue.component(components[component].name, components[component]);
  });
  install.installed = true;
};

install.installed = false;

if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue);
  install.installed = true;
}

const Vcomp = {
  ...components,
  install,
};

export default Vcomp;
