{"queryConfig": {"columns": [{"displayName": "局站名称", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "chinaName", "tableColumnName": "chinaName"}, {"displayName": "局站等级", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "grade", "tableColumnName": "grade"}, {"displayName": "所属行政区域", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "districName", "tableColumnName": "districName"}], "classId": "spcStation", "className": "局站"}, "tableConfig": {"columns": [{"displayName": "局站名称", "type": "string", "columnName": "chinaName"}, {"displayName": "所属行政区域", "type": "string", "columnName": "districName"}, {"displayName": "局站类型", "type": "string", "columnName": "type"}, {"displayName": "产权性质", "type": "string", "columnName": "propChar"}, {"displayName": "产权归属", "type": "string", "columnName": "property"}, {"displayName": "局站描述地址", "type": "string", "columnName": "location"}, {"displayName": "局站等级", "type": "string", "columnName": "grade"}, {"displayName": "是否共享", "type": "string", "columnName": "isShare"}, {"displayName": "共享单位", "type": "string", "columnName": "shareUnit"}], "selectColumns": ["deviceName", "chinaName", "districName", "type", "propChar", "property", "location", "grade", "isShare", "shareUnit"]}}