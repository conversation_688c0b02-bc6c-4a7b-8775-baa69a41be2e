<template>
  <div class="topoContainer">
    <div class="imgTuli2">
      <div class="imgCls">
        <span>正常</span>
        <img :src="imgMap.green" />
      </div>
      <div class="imgCls">
        <span>告警</span>
        <img :src="imgMap.red" />
      </div>
      <div class="imgCls">
        <span>未知</span>
        <img :src="imgMap.yellow" />
      </div>
    </div>
    <div id="container"></div>
  </div>
</template>
<script>
  import G6 from "@antv/g6";
  import green from "@/assets/images/topo/img2.png";
  import yellow from "@/assets/images/topo/img1.png";
  import red from "@/assets/images/topo/img3.png";
  let dashStyle = {
    lineDash: [10, 5],
    endArrow: true,
  };
  export default {
    name: "topoPage",
    props: {
      sendData: {
        type: Object,
      },
      selRow: {
        type: Object,
      },
    },
    data() {
      return {
        jsonData: {
          nodes: [],
        },
        imgMap: {
          green,
          yellow,
          red,
        },
      };
    },
    watch: {
      sendData: {
        handler(n, ) {
          let primary = n?.primary[0] || [];
          let imgMap = {
            0: green,
            1: red,
            2: yellow,
          };
          let primaryList = primary.map((item, ) => {
            return {
              id: item.aportId + '',
              label: `${item?.aportName?.trim()}`,
              img: imgMap[parseInt(item?.communicationState)],
              data: item,
            };
          });
          let allNodes = primaryList;

          let nodes = this.deduplication(allNodes);
          let pEdgeList = this.getEdges(primaryList, "working"); //公祖路径
          let edges = this.dealEdges([...pEdgeList]);
          let jsonData = {
            nodes,
            edges,
          };

          this.jsonData = jsonData;
          this.updateTopo();
        },
      },
    },
    mounted() {
      this.initTopo();
    },
    methods: {
      //数组去重
      deduplication(arr) {
        let newArr = [];
        let idArr = [];
        for (let item of arr) {
          if (!idArr.includes(item.label)) {
            newArr.push(item);
            idArr.push(item.label);
          }
        }
        return newArr;
      },
      // 线段数组
      getEdges(nodeList, flag) {
        let links = [];
        let typeCls = {};

        if (flag === "protection") {
          typeCls = {
            style: dashStyle,
          };
        } else {
          typeCls = {
            type: "line-arrow",
          };
        }
        for (let i = 0; i < nodeList.length; i++) {
          if (nodeList[i + 1]) {
            let item1 = nodeList[i];
            let item2 = nodeList[i + 1];
            let source = item1.id;
            let target = item2.id;
            links.push({
              source,
              target,
              ...typeCls,
            });
          }
        }
        return links;
      },
      dealEdges(linkList) {
        let links = [];
        let tmpArr = [];
        let repeatArr = [];
        for (let i = 0; i < linkList.length; i++) {
          let item = linkList[i];
          let idStr = item.source + item.target;
          if (!tmpArr.includes(idStr)) {
            tmpArr.push(idStr);
          } else {
            repeatArr.push(idStr);
          }
        }
        for (let i = 0; i < linkList.length; i++) {
          let item = linkList[i];
          let idStr = item.source + item.target;
          if (repeatArr.includes(idStr)) {
            if (item.type === "line-arrow") {
              //工作路径
              links.push({
                ...item,
                sourceAnchor: 4,
                targetAnchor: 2,
              });
            } else {
              //保护路径
              links.push({
                ...item,
                sourceAnchor: 5,
                targetAnchor: 3,
              });
            }
          } else {
            links.push(item);
          }
        }

        return links;
      },
      initTopo() {
        const container = document.getElementById("container");
        const width = container.scrollWidth;
        const height = container.scrollHeight || 500;

        const tooltip = this.drawTootip();
        
          let _this = this;

        G6.registerEdge("line-arrow", {
          itemType: "edge",
          draw: function draw(cfg, group) {
            var startPoint = cfg.startPoint,
              endPoint = cfg.endPoint;

            var keyShape = group.addShape("path", {
              attrs: {
                path: [
                  ["M", startPoint.x, startPoint.y],
                  ["L", endPoint.x, endPoint.y],
                ],
                stroke: "#0CC",
                lineWidth: 1,
                endArrow: {
                  path: G6.Arrow.vee(5, 5, 5),
                  d: 5,
                },
              },
            });
            return keyShape;
          },
        });
        const graph = new G6.Graph({
          container: "container",
          width,
          height,
          modes: {
            default: [
              "drag-canvas",
              "zoom-canvas",
              "drag-node",
              "activate-relations",
            ],
          },
          defaultNode: {
            size: [40, 40],
            type: "image",
            img: green,
            color: "#ffffff",
            style: {
              stroke: "#000",
              fill: "#C6E5FF",
            },
            anchorPoints: [
              [0, 0.5],
              [1, 0.5],
              [0, 0.3],
              [0, 0.7],
              [1, 0.3],
              [1, 0.7],
            ],
          },
          maxZoom: 1.5,

          defaultEdge: {
            shape: "line",
            style: {
              endArrow: true,
              lineWidth: 2,
              stroke: "#ccc",
            },
          },
          plugins: [tooltip],
          layout: {
            type: "dagre",
            rankdir: "LR", // 可选，默认为图的中心
            align: "DL", // 可选
            nodesep: 30, //节点间距（px）。在 rankdir 为 'TB' 或 'BT' 时是节点的水平间距；在 rankdir 为 'LR' 或 'RL' 时代表节点的竖直方向间距
            ranksep: 30, // 层间距（px）。在 rankdir 为 'TB' 或 'BT' 时是竖直方向相邻层间距；在 rankdir 为 'LR' 或 'RL' 时代表水平方向相邻层间距
            controlPoints: true, // 是否保留布局连线的控制点
          },
        });

        graph.node(function (node) {
          let position = "bottom";
          if (!node.children) {
            position = "bottom";
          }
          return {
            label: _this.superLongTextHandle(node.label, 60, 12),
            labelCfg: {
              position,
              style: {
                textAlign: "center",
                fill: '#fff',
              },
            },
          };
        });

        if (typeof window !== "undefined")
          window.onresize = () => {
            if (!graph || graph.get("destroyed")) return;
            if (!container || !container.scrollWidth || !container.scrollHeight)
              return;
            graph.changeSize(container.scrollWidth, container.scrollHeight);
          };
        graph.data(this.jsonData);
        graph.render();
        graph.zoomTo(2);
        this.graph = graph;
      },
      updateTopo() {
        this.graph.data(this.jsonData);
        this.graph.render();
        setTimeout(() => {
          this.graph.zoomTo(2);
        });
      },

      // 画画线
      drawLink() {
        console.log(this.jsonData.nodes);
        let len = this.jsonData.nodes.length || 0;
        let nodeList = this.jsonData.nodes;

        for (let i = 0; i < len; i++) {
          if (nodeList[i + 1]) {
            let item1 = nodeList[i];
            let item2 = nodeList[i + 1];
            let source = item1.data.zportId;
            let target = item2.data.aportId;

            this.graph.addItem("edge", {
              source,
              target,
              type: "line-arrow",
            });
          }
        }
      },
      // 提示框
      drawTootip() {
        let tooltip = new G6.Tooltip({
          offsetX: 10,
          offsetY: 20,
          getContent(e) {
            const outDiv = document.createElement("div");
            outDiv.style.width = "300px";
            let curData = e.item.getModel();

            if (curData?.data) {
              let obj = curData?.data;
              outDiv.innerHTML = `
              <ul>
                
                <li class="node-cls">
                  <span>网元名称 : </span><span>${obj.atrsNeName}</span>
                </li>
                <li class="node-cls">
                  <span>网元A端口 : </span><span>${obj.aportName}</span>
                </li>
                <li class="node-cls">
                  <span>网元Z端口 : </span><span>${obj.zportName}</span>
                </li>
                
              </ul>
            `;
            } else if (curData.label) {
              outDiv.innerHTML = `
              <ul>
                <li>业务名称：${curData.label || ""}</li>
              </ul>
            `;
            } else {
              return "";
            }

            return outDiv;
          },
          itemTypes: ["node"],
        });


        return tooltip;
      },
      // G6换行符处理超长文本
    superLongTextHandle(str, maxWidth, fontSize) {
      let currentWidth = 0;
      let res = str;
      // 区分汉字和字母
      const pattern = new RegExp("[\u4E00-\u9FA5]+");
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth) return;
        if (pattern.test(letter)) {
          // 中文字符
          currentWidth += fontSize;
        } else {
          // 根据字体大小获取单个字母的宽度
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth) {
          res = `${str.substr(0, i)}\n${this.superLongTextHandle(
            str.substr(i),
            maxWidth,
            fontSize
          )}`;
        }
      });
      return res;
    },
    },
  };
</script>

<style lang="less" scoped></style>

<style lang="less">
  .g6-component-tooltip {
    .node-cls {
      display: flex;

      span {
        display: flex;
        align-items: center;
        height: 30px;
      }
      :first-child {
        width: 90px;
      }
    }
  }
  .topoContainer {
    position: relative;
    .tuli {
      position: absolute;
      bottom: 20px;
      left: 30px;
      background-color: #fff;
      padding: 16px;
      .lujing {
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .workcls {
          width: 80px;
          height: 2px;
          background-color: #2ecccc;
        }
        .protectCls {
          width: 80px;
          height: 2px;
          background: repeating-linear-gradient(
            to right,
            #fff 0px,
            #fff 0px,
            #ccc 4px,
            #ccc 8px
          );
        }
      }
    }
    .imgTuli2 {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background-color: transparent;
      padding: 16px;
      display: flex;
      .imgCls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 24px;
        img {
          width: 30px;
          margin-left: 8px;
        }
      }
    }
  }
</style>
