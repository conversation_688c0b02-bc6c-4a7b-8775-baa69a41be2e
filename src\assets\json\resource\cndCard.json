{"queryConfig": {"columns": [{"displayName": "板卡名称", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "boardname", "tableColumnName": "boardname"}, {"displayName": "板卡ID", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "cardId", "tableColumnName": "cardId"}, {"displayName": "所属设备IP", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "deviceip", "tableColumnName": "deviceip"}], "classId": "cndCard", "className": "板卡"}, "tableConfig": {"columns": [{"displayName": "板卡ID", "type": "string", "columnName": "coreId"}, {"displayName": "设备ID", "type": "string", "columnName": "deviceid"}, {"displayName": "所属设备IP", "type": "string", "columnName": "deviceip"}, {"displayName": "槽位号", "type": "string", "columnName": "slotNo"}, {"displayName": "子卡号", "type": "string", "columnName": "subslotNo"}, {"displayName": "板卡名称", "type": "string", "columnName": "boardname"}, {"displayName": "板卡类型", "type": "string", "columnName": "boardtype"}], "selectColumns": ["deviceid", "deviceip", "slotNo", "subslotNo", "boardname", "boardtype"]}}