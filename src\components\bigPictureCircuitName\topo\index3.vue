<template>
  <div class="topoContainer" v-loading="loading">
    <!-- <div class="imgTuli">
      <div class="imgCls">
        <img :src="imgMap.green" />
        <span>网元</span>
      </div>
      <div class="imgCls">
        <img :src="imgMap.red" />
        <span>告警网元</span>
      </div>
      <div class="lujing">
        <div class="workcls"></div>
        <span>线路</span>
      </div>
      <div class="lujing">
        <div class="protectCls"></div>
        <span>告警线路</span>
      </div>
    </div> -->
    <div id="container" ref="container"></div>
  </div>
</template>

<script>
import G6, { Legend } from "@antv/g6";
import green from "@/assets/img/topo/green.png";
import yellow from "@/assets/img/topo/yellow.png";
import red from "@/assets/img/topo/red.png";
// import resultJson from "./data.json";
import { getHiddenNodes, isCashed } from './util.js';
// import insertCss from 'insert-css';

import green1 from "@/assets/img/topo/defaultData/defaultData1.png";
import yellow1 from "@/assets/img/topo/selectData/selectData1.png";
import red1 from "@/assets/img/topo/alarmData/red1.png";


let leavelMap = {
  'combo1': '骨干层',
  'combo2': '核心层',
  'combo3': '汇聚层',
  'combo4': '接入层',
  'combo5': '未知'
}

// insertCss(`
//   .g6-component-toolbar{
//     top: -50px;
//     left: 81% !important;
//     background: #041736 !important;
//     border: 1px solid #03396A;
//     svg {
//       fill: #fff;
//     }
//   }

//   .g6-component-tooltip {
//       background-color: #062856 !important;
//       color: #fff;
//       border: 0;
//       padding: 16px;
//       .node-cls {
//         display: flex;
//         span {
//           display: flex;
//           align-items: center;
//           height: 30px;
//         }

//         :first-child {
//           width: 90px;
//         }
//       }
//     }

// `);

export default {
  name: "Topo",
  props: {
    sendData: {
      type: Object,
      default: () => {
        return {
          nodes: [],
          edges: []
        }
      }
    },
    custId: {
      type: String
    },
  },
  data() {
    return {
      isCache: false,
      loading: false,
      combosStyle: {},
      canvansStyle: {},
      // sendData: {},
      jsonData: {
        // id: "01",
        // label: "业务类型",
        // img: img1,
        // children: [],
        nodes: [],
        edges: [],
        combos: [],
      },
      imgMap: {
        green,
        yellow,
        red,
      },
      defaultNetElement: green1,
      selectNetElement: yellow1,
      alarmNetElement: red1,
      defaultLineColor: '#0AEFC5',
      selectLineColor: 'yellow',
      alarmLineColor: 'red',
      defaultHierarchyColor: '#0AEFC5',
      alarmHierarchyColor: 'red',
    };
  },
  watch: {
    sendData: {
      async handler(newData) {
        // let topoData = resultJson?.data;
        if (this.graph) {
          this.graph.destroy();
          this.jsonData = {
            nodes: [],
            edges: [],
            combos: [],
          }
        }

        let combos = this.jsonData.combos || [];
        if (!combos.length && newData) {
          this.dealTopoData(newData);
          console.log('获取原始数据')
        }
        // console.log(this.jsonData)
        this.initTopo();
        this.highlightTopo();
      },
    },

  },
  mounted() {
  },
  methods: {
    dealTopoData(data) {
      console.log(data);
        this.defaultNetElement = data.defaultNetElement.imgsrc
        this.selectNetElement = data.selectNetElement.imgsrc
        this.alarmNetElement = data.alarmNetElement.imgsrc
        this.defaultLineColor = data.defaultLineColor
        this.selectLineColor = data.selectLineColor
        this.alarmLineColor = data.alarmLineColor
        this.defaultHierarchyColor = data.defaultHierarchyColor
        this.alarmHierarchyColor = data.alarmHierarchyColor
      let nodeList = data?.nodes || [];
      let edgeList = data?.edges || [];
      this.isCache = isCashed(nodeList)
      console.log(this.isCache, 'isCache')

      let { nodes: allNodes } = this.getNodesList(nodeList);
      let edges = [];
      let combos = this.getCombos(allNodes);
      let hiddenNodes = getHiddenNodes(combos, this.combosStyle);
      let nodes = [];

      if (this.isCache) {
        nodes = [...allNodes,];
      } else {
        nodes = [...allNodes, ...hiddenNodes];
      }
      edges = this.getEdges(edgeList);

      let jsonData = {
        nodes,
        edges,
        combos,
      };
      // console.log(jsonData)
      this.jsonData = jsonData;
      // this.drawLink();
    },

    highlightTopo() {
      let { nodes, edges, combos } = { ...this.jsonData };
      // let statusType = {
      //   0: this.imgMap.green,
      //   1: this.imgMap.yellow,  //高亮
      //   '-1': this.imgMap.red   //告警
      // }
      nodes.forEach(item => {
        const node = this.graph.findById(item.id);
        if (item.colourType === '1') { //高亮
          node.update({
            img: this.selectNetElement
          })
        } else if (item.colourType === '-1') { //告警
          node.update({
            img: this.alarmNetElement
          })
        }
        node.refresh();
      })
      edges.forEach(item => {
        const edge = this.graph.findById(item.id);
        if (item.colourType === '1') { //高亮
          this.graph.setItemState(edge, 'active', true)
        } else if (item.colourType === '-1') { //告警
          this.graph.setItemState(edge, 'warn', true)
        }
      })

      combos.forEach(item => {
        let children = item.children || [];
        let curCombs = this.graph.findById(item.id);
        if (this.isWaning(children)) {
          curCombs.update({
            style: {
              stroke: this.alarmHierarchyColor     // 层级告警变色
            }
          })
        }
      })
      // this.graph.render();
    },
    //判断是否为告警
    isWaning(list) {
      return list.some(item => {
        const node = this.graph.findById(item.id);
        return node.getModel().colourType == '-1'
      })
    },
    //数组去重
    deduplication(arr) {
      let newArr = [];
      let idArr = [];
      for (let item of arr) {
        if (!idArr.includes(item.label)) {
          newArr.push(item);
          idArr.push(item.label);
        }
      }
      return newArr;
    },
    // 线段数组
    getEdges(edgesList) {
      let links = [];
      for (let item of edgesList) {
        let { source, target } = item;
        links.push({
          ...item,
          source,
          target,
          style: { endArrow: true,},
          colourType: item.colourType || '0'
        });
      }
      G6.Util.processParallelEdges(links);
      return links;
    },
    dealEdges(linkList) {
      let links = [];
      let tmpArr = [];
      let repeatArr = [];
      //   sourceAnchor: 4,
      // targetAnchor: 2,

      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (!tmpArr.includes(idStr)) {
          tmpArr.push(idStr);
        } else {
          repeatArr.push(idStr);
        }
      }
      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (repeatArr.includes(idStr)) {
          if (item.type === "line-arrow") {
            //工作路径
            links.push({
              ...item,
              sourceAnchor: 4,
              targetAnchor: 2,
            });
          } else {
            //保护路径
            links.push({
              ...item,
              sourceAnchor: 5,
              targetAnchor: 3,
            });
          }
        } else {
          links.push(item);
        }
      }

      return links;
    },
    getCombos() {
      if (this.isCache) {
        return [
          { id: "combo1", label: leavelMap.combo1, type: 'backbone', },
          { id: "combo2", label: leavelMap.combo2, type: 'region', },
          { id: "combo3", label: leavelMap.combo3, type: "local", },
          // { id: "combo4", label: leavelMap.combo4, type: "access", },
        //   { id: "combo5", label: leavelMap.combo5, type: "unknown", },
        ]
      } else {
        return [
          { id: "combo1", label: leavelMap.combo1, type: 'backbone', x: this.combosStyle.width, y: this.combosStyle.height * 0.5, },
          { id: "combo2", label: leavelMap.combo2, type: 'region', x: this.combosStyle.width, y: this.combosStyle.height * 1.8, },
          { id: "combo3", label: leavelMap.combo3, type: "local", x: this.combosStyle.width, y: this.combosStyle.height * 3.1, },
          // { id: "combo4", label: leavelMap.combo4, type: "access", x: this.combosStyle.width, y: this.combosStyle.height * 4.4, },
        //   { id: "combo5", label: leavelMap.combo5, type: "unknown", x: this.combosStyle.width, y: this.combosStyle.height * 5.7, },
        ]
      }

    },
    getNodesList(allNodes) {
      let comboMap = {
        [leavelMap.combo1]: 'combo1',
        [leavelMap.combo2]: 'combo2',
        [leavelMap.combo3]: 'combo3',
        [leavelMap.combo4]: 'combo4',
        [leavelMap.combo5]: 'combo5'
      }
      const nodesMap = {
        [leavelMap.combo1]: 0,
        [leavelMap.combo2]: 0,
        [leavelMap.combo3]: 0,
        [leavelMap.combo4]: 0,
        [leavelMap.combo5]: 0
      }
      const nodesMap2 = {
        [leavelMap.combo1]: 0,
        [leavelMap.combo2]: 0,
        [leavelMap.combo3]: 0,
        [leavelMap.combo4]: 0,
        [leavelMap.combo5]: 0
      }

      let nodes = [];

      for (let item of allNodes) {
        let nodeType = item.netLevelName;
        if (nodeType) {
          nodesMap[nodeType]++
        }
      }
      //获取最多的节点
      let lenArr = Object.values(nodesMap).map(item => {
        return item
      })
      let maxNodeLen = Math.max(...lenArr);
      let canvasWidth = (maxNodeLen > 80 ? 80 : maxNodeLen) * 150;
      let containerW = this.$refs.container.offsetWidth;
      let containerH = this.$refs.container.offsetHeight -200;
      this.canvansStyle.width = canvasWidth >= containerW ? containerW : canvasWidth;
      this.canvansStyle.height = containerH;
      this.combosStyle.width = this.canvansStyle.width * 0.5;
      this.combosStyle.height = this.canvansStyle.height * 0.15;

      for (let item of allNodes) {
        let nodeType = item.netLevelName;
        nodesMap2[nodeType]++
        let nodeMode = {
          id: item.id,
          label: item?.trsNetName ? `${item?.trsNetName?.trim()}` : '',
        //   img: "http://browser9.qhimg.com/bdm/480_296_0/t01bbd94b90e850d1d3.jpg",
          img: this.defaultNetElement,
          comboId: comboMap[nodeType] || 'combo5',
          data: item,
          x: (this.isCache ? item.x : nodesMap2[nodeType] * 150 - this.canvansStyle.width / 2),
          y: (this.isCache ? item.y : 0),
          colourType: item.colourType || '0',
          size: !item.id.includes('combo') ? [100, 100,] : [1, 1]
        }

        nodes.push(nodeMode);
      }

      return {
        nodes,
      };
    },
    initTopo() {

      const tooltip = this.drawTootip();

      this.registerCombo("backbone", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("region", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("local", { width: this.combosStyle.width, height: this.combosStyle.height });
      // this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height, refY: 90 });
      this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("unknown", { width: this.combosStyle.width, height: this.combosStyle.height });

      this.registerEdge("line-arrow");
      const toolbar = new G6.ToolBar({
        position: { x: 10, y: -50 },
      });
      // this.registerCustomNode("myNode");
      // console.log(this.jsonData)
      const graph = new G6.Graph({
        container: "container",
        width: this.canvansStyle.width,
        height: this.canvansStyle.height,
        groupByTypes: false,
        // linkCenter: true,
        maxZoom: 1,
        modes: {
          default: [

            { type: 'zoom-canvas', sensitivity: 1, enableOptimize: false, optimizeZoom: 0.5 },
            { type: 'drag-canvas', enableOptimize: false },
            {
              type: 'drag-node',
              enableOptimize: false,
              onlyChangeComboSize: true,
              // enableDelegate: true,
              multiple: true,
            },

            {
              type: 'drag-combo',
              enableOptimize: true,
              onlyChangeComboSize: true,
              enableDelegate: false,
            },
            { type: 'brush-select', enableOptimize: true },
          ],
        },
        defaultNode: {
          size: [40, 40],
          type: "image",
          img: green,
          style: {
            // lineWidth: 20,
            // stroke: "#000",
            // fontSize: 24,
            // stroke: "#C6E5FF",
            // fill: "#C6E5FF",
            // radius: 5
          },
        },
        defaultEdge: {
          type: "quadratic",
          style: {
            lineWidth: 2,
            stroke: this.defaultLineColor,         //  默认线路颜色
          },
          labelCfg: {
            autoRotate: true,
          },
        },
        nodeStateStyles: {
          active: {
            stroke: "#01E2B5",
            // shadowColor: "#01E2B5",
            shadowColor: "yellow",
            lineWidth: 100,
            strokeOpacity: 0.5,
            shadowBlur: 10,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            "text-shape": {
              fill: "#01E2B5",
            },
            opacity: 1,
          },
          inactive: {
            opacity: 0.6,
          },
        },
        comboStateStyles: {
          active: {
            fill: "rgba(255,255,255,0)",
            opacity: 1,
            stroke: "#0AEFC5",
            "text-shape": {
              fill: "#01E2B5",
            },
            lineWidth: 5,
          },
          inactive: {
            lineDash: [5, 5],
            opacity: 1,
            fill: "rgba(255,255,255,0)",
            lineWidth: 2,
          },
        },

        edgeStateStyles: {
          active: {
            lineWidth: 5,
            stroke: this.selectLineColor,   // 选中颜色
          },
          inactive: {
            lineWidth: 2,
            stroke: "#fff",
          },
          hover: {
            lineWidth: 5,
            stroke: '#0AEFC5',   //  鼠标悬浮颜色
          },
          warn: {
            lineWidth: 5,
            stroke: this.alarmLineColor,       //  告警颜色
          }
        },
        // plugins: [tooltip],
        // plugins: [tooltip, toolbar,],
      });

      graph.node(function (node) {
        let position = "bottom";
        let rotate = 0;
        if (!node.children) {
          position = "bottom";
          rotate = -Math.PI / 8;
        }
        return {
          label: node.label,
          labelCfg: {
            position,
            // offset: 50,
            style: {
              textAlign: "center",
              rotate,
              fill: "#fff",
            },
          },
        };
      });

      graph.on('node:dblclick', e => {
        if (e.item.getModel().colourType == '-1') {
          console.log(e.item.getModel());
        }
        
      })
      graph.on('node:mouseenter', e => {
        // console.log(e);
        graph.setItemState(e.item, 'active', true)
        let edgeItem = e.item
        edgeItem.getEdges().forEach(edge => {
          graph.setItemState(edge.getTarget(), 'active', true)
          graph.setItemState(edge.getSource(), 'active', true)
          graph.setItemState(edge, 'hover', true)
        })
      })

      graph.on('node:mouseleave', e => {
        graph.setItemState(e.item, 'active', false)
        graph.getNodes().forEach(node => {
          if (node.getModel().colourType == '0') {
            graph.clearItemStates(node)
          }
        })
        graph.getEdges().forEach(edge => {
          let colourType = edge.getModel().colourType
          graph.clearItemStates(edge)
          if (colourType === '1') {
            this.graph.setItemState(edge, 'active', true)
          } else if (colourType === '-1') { //告警
            this.graph.setItemState(edge, 'warn', true)
          }
        })
        // graph.paint()
        // graph.setAutoPaint(true)
      })

      graph.on('dragend', async () => {
        // const { item } = evt;
        // const model = item.getModel();
        // console.log(model)

        // const { x, y, width, height } = item.getBBox();
        // const edges = this.graph.save().edges;
        const { nodes, } = this.graph.save();
        let sendNodes = [];
        nodes.forEach(item => {
          let curMode = {
            id: item.id,
            x: item.x,
            y: item.y,
            custId: this.custId,
            trsNetId: item.data?.trsNetId || item.id,
            netLevelName: item.data?.netLevelName || item.netLevelName
          }
          // if (!item.id.includes('combo')) {
          sendNodes.push(curMode)
          // }
        })

        // console.log(sendNodes)
        await this.$api.topo.updateTrsNode(sendNodes);
      });


      if (typeof window !== "undefined")
        window.onresize = () => {
          if (!graph || graph.get("destroyed")) return;
          const container = document.getElementById('container');
          const width = container.scrollWidth;
          const height = container.scrollHeight || window.innerHeight;
          if (!container || !container.scrollWidth || !container.scrollHeight)
            return;
          graph.changeSize(width, height);
        };
      graph.data(this.jsonData);

      graph.render();
      graph.fitView();
      this.graph = graph;
    },

    registerEdge(linkName) {
      G6.registerEdge(linkName, {
        itemType: "edge",
        draw: function draw(cfg, group) {
          var startPoint = cfg.startPoint,
            endPoint = cfg.endPoint;

          var keyShape = group.addShape("path", {
            attrs: {
              path: [
                ["M", startPoint.x, startPoint.y],
                ["L", endPoint.x, endPoint.y],
              ],
              stroke: "#0CC",
              lineWidth: 1,
              // startArrow: {
              //   path: "M 10,0 L -10,-10 L -10,10 Z",
              //   path: G6.Arrow.vee(15, 20, 15),
              //   d: 15,
              // },
              endArrow: {
                path: G6.Arrow.vee(5, 5, 5),
                d: 5,
              },
            },
          });
          return keyShape;
        },
      });
    },

    registerCombo(comboName, cusConf) {
      let _this = this;
      G6.registerCombo(
        comboName,
        {
          drawShape: function drawShape(cfg, group) {
            const self = this;
            // 获取配置中的 Combo 内边距
            cfg.padding = cfg?.padding || [0, 0, 0, 0];
            cfg.labelCfg = {
              refY: cusConf?.refY || 40,
              position: "top",
              style: {
                fontSize: 18,
                fill: "#fff",
              },
            }
            cfg.size = [cusConf?.width || cfg.width, cusConf?.height || cfg.height];
            // 获取样式配置，style.width 与 style.height 对应 rect Combo 位置说明图中的 width 与 height
            const style = self.getShapeStyle(cfg);
            // 绘制一个矩形作为 keyShape，与 'rect' Combo 的 keyShape 一致
            const rect = group.addShape("rect", {
              attrs: {
                allowZoom: true,
                allowDrag: true,
                ...style,
                x: -style.height / 2 - cfg.padding[0],
                y: -style.width / 2 - cfg.padding[3],
                lineWidth: 2,
                fill: "rgba(255,255,255,0)",
                opacity: 1,
                stroke: _this.defaultHierarchyColor,      // 默认图层颜色
                // stroke: cusConf?.stroke || '#fff',
                lineDash: [5, 5],
              },
              draggable: false,
              name: "combo-keyShape" + comboName, // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
            });

            return rect;
          },
        },
        "rect"
      );
    },

    // 画画线
    drawLink() {
      let len = this.jsonData.nodes.length || 0;
      let nodeList = this.jsonData.nodes;

      for (let i = 0; i < len; i++) {
        if (nodeList[i + 1]) {
          let item1 = nodeList[i];
          let item2 = nodeList[i + 1];
          let source = item1.id;
          let target = item2.id;

          this.graph.addItem("edge", {
            source,
            target,
          });
        }
      }
    },
    // 提示框
    drawTootip() {
      let tooltip = new G6.Tooltip({
        offsetX: 20,
        offsetY: 0,
        fixToNode: [1, 1],
        // trigger: 'click',
        itemTypes: ['node'],
        getContent(e) {
          const outDiv = document.createElement("div");
          outDiv.style.width = "300px";
          // console.log(e.item.getModel());
          let curData = e.item.getModel();

          if (curData?.data) {
            let obj = curData?.data;
            outDiv.innerHTML = `
              <ul>
                <li class="node-cls">
                  <span>网元ID : </span> <span>${obj.id}</span>
                </li>
                <li class="node-cls">
                  <span>网元名称 : </span> <span>${obj.label}</span>
                </li>
                <li class="node-cls">
                  <span>所属层级 : </span><span>${obj.netLevelName}</span>
                </li>
              </ul>
            `;
          } else if (curData.label) {
            outDiv.innerHTML = `
              <ul>
                <li>业务名称：${curData.label || ""}</li>
              </ul>
            `;
          } else {
            return "";
          }

          return outDiv;
        },
      });

      return tooltip;
    },
  },
};
</script>


<style lang="less" scoped>
.topoContainer {
  position: relative;

  #container {
    display: flex;
    flex: 1;
    height: 750px;
    overflow: auto;


  }

  #container::-webkit-scrollbar-thumb {
    background-color: #158C8D;
  }

  #container::-webkit-scrollbar-track {
    background-color: #041440;
  }


  .imgTuli {
    position: absolute;
    top: -50px;
    left: 0px;
    padding: 16px;
    display: flex;
    color: #fff;

    .lujing {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;

      .workcls {
        width: 50px;
        height: 2px;
        background-color: #fff;
        margin-right: 12px;
      }

      .protectCls {
        width: 50px;
        height: 2px;
        background: red;
        margin-right: 12px;

      }
    }

    .imgCls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;

      img {
        display: block;
        width: 30px;
        margin-right: 12px;
      }
    }
  }

  .el-loading-mask {
    background: rgba(5, 99, 153, 0.3);
  }
}
</style>
