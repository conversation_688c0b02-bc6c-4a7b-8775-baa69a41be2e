<template>
  <div id="container"></div>
</template>
<script>
  import G6 from "@antv/g6";
  import img1 from "@/assets/img/topo/image1.png";
  import green from "@/assets/img/topo/green.png";
  import yellow from "@/assets/img/topo/yellow.png";
  import red from "@/assets/img/topo/red.png";

  // let jsonData = {
  //   id: "01",
  //   label: "业务类型",
  //   img: img1,
  //   children: [
  //     { id: "1.1", label: "GLT **********", img: img2 },
  //     { id: "1.2", label: "GLT **********1", img: img2 },
  //     { id: "1.3", label: "GLT **********2", img: img2 },
  //     { id: "1.4", label: "GLT **********3", img: img2 },
  //     { id: "1.5", label: "GLT **********4", img: img2 },
  //     { id: "1.6", label: "GLT ***********", img: img2 },
  //   ],
  // };

  export default {
    name: "topo",
    props: {
      sendData: {
        type: Array,
      },
      selRow: {
        type: Object,
      },
    },
    data() {
      return {
        jsonData: {
          id: "01",
          // label: "业务类型",
          img: img1,
          children: [],
        },
      };
    },
    watch: {
      sendData: {
        handler(n, o) {
          console.log(n);
          let curList = n || [];
          let nodeList = curList.map((item, index) => {
            let imgMap = {
              0: green,
              1: red,
              2: yellow,
            };

            return {
              id: item.nodeUuid + "index=" + index,
              label: `${item?.name?.trim()}(${item.linkTpId}) `,
              img: imgMap[item?.communicationState],
              data: item,
            };
          });

          let jsonData = {
            id: this.selRow.serialNumber,
            label: this.selRow.baseTitle,
            img: img1,
            children: nodeList,
          };
          this.jsonData = jsonData;
          this.updateTopo();
          this.drawLink();
        },
      },
    },
    mounted() {
      this.initTopo();
    },
    methods: {
      initTopo() {
        const container = document.getElementById("container");
        const width = container.scrollWidth;
        const height = container.scrollHeight || 500;

        const tooltip = new G6.Tooltip({
          offsetX: 10,
          offsetY: 20,
          getContent(e) {
            const outDiv = document.createElement("div");
            outDiv.style.width = "300px";
            // console.log(e.item.getModel());
            let curData = e.item.getModel();

            if (curData?.data) {
              let obj = curData?.data;
              let status = {
                0: "正常",
                1: "终端",
                2: "未知",
              };
              // active：对象已激活，表示对象处于管理状态；inactive：对象未激活，表示对象处于未管理状态；test：对象处于测试状态；
              // maintenance：对象处于维护状态；unknown：表示对象的管理状态未知；--：表示该对象里这个属性没意义
              let manageStatus = {
                active: "已激活状态",
                inactive: "未管理状态",
                test: "测试状态",
                maintenance: "维护状态",
                unknown: "未知状态",
              };
              // "enableAson": "", //仅ASON设备支持此参数。网元使能智能。0=网元智能关闭；1=网元智能开启（光层和电层智能关闭）；2=光层智能开启；3=电层智能开启；4=光层和电层智能都开启
              let enableAsonStatus = {
                0: "网元智能关闭",
                1: "网元智能开启（光层和电层智能关闭）",
                2: "光层智能开启",
                3: "电层智能开启",
                4: "光层和电层智能都开启",
              };
              outDiv.innerHTML = `
              <ul>
                <li class="node-cls">
                  <span>网元ID : </span> <span>${obj.nodeId}</span>
                </li>
                <li class="node-cls">
                  <span>网元名称 : </span> <span>${obj.name}</span>
                </li>
                <li class="node-cls">
                  <span>网元端口Id : </span><span>${obj.linkTpId}</span>
                </li>
                <li class="node-cls">
                  <span>设备IP : </span><span>${obj.ipAddress}</span>
                </li>
                <li class="node-cls">
                  <span>设备型号 : </span><span>${obj.productName}</span>
                </li>
                <li class="node-cls">
                  <span>网元通信状态 : </span>
                  <span> ${status[obj.communicationState] || "--"}</span>
                </li> 
                <li class="node-cls">
                  <span>管理状态 : </span>
                  <span> ${manageStatus[obj.adminStatus] || "--"}</span>
                </li> 
                <li class="node-cls">
                  <span>网元使能智能 : </span>
                  <span> ${enableAsonStatus[obj.enableAson] || "--"}</span>
                </li> 
                <li class="node-cls">
                  <span>端口名 : </span><span>${obj.tpName}</span>
                </li>
                <li class="node-cls">
                  <span>端口ID : </span><span>${obj.portId}</span>
                </li>
                <li class="node-cls">
                  <span>端口类型 : </span><span>${obj.tpType}</span>
                </li>
              </ul>
            `;
            } else if (curData.label) {
              outDiv.innerHTML = `
              <ul>
                <li>业务名称：${curData.label || ""}</li>
              </ul>
            `;
            } else {
              return "";
            }

            return outDiv;
          },
          itemTypes: ["node"],
        });

        G6.registerEdge("line-arrow", {
          itemType: "edge",
          draw: function draw(cfg, group) {
            var startPoint = cfg.startPoint,
              endPoint = cfg.endPoint;

            var keyShape = group.addShape("path", {
              attrs: {
                path: [
                  ["M", startPoint.x, startPoint.y],
                  ["L", endPoint.x, endPoint.y],
                ],
                stroke: "#0CC",
                lineWidth: 1,
                // startArrow: {
                //   path: "M 10,0 L -10,-10 L -10,10 Z",
                //   path: G6.Arrow.vee(15, 20, 15),
                //   d: 15,
                // },
                endArrow: {
                  // path: "M 10,0 L -10,-10 L -10,10 Z",
                  path: G6.Arrow.vee(15, 20, 15),

                  d: 5,
                },
              },
            });
            return keyShape;
          },
        });

        const graph = new G6.TreeGraph({
          container: "container",
          width,
          height,
          linkCenter: true,
          modes: {
            default: [
              // {
              //   type: "collapse-expand",
              //   onChange: function onChange(item, collapsed) {
              //     const data = item.getModel();
              //     data.collapsed = collapsed;
              //     return true;
              //   },
              // },
              "drag-canvas",
              "zoom-canvas",
              "drag-node",
              "activate-relations",
              // "click-select",
            ],
          },
          defaultNode: {
            type: "image",
            size: 50,
          },
          maxZoom: 1.5,
          defaultEdge: {
            type: "cubic-vertical",
          },
          plugins: [tooltip],
          layout: {
            type: "compactBox",
            direction: "TB",
            getId: function getId(d) {
              return d.id;
            },
            getHeight: function getHeight() {
              return 16;
            },
            getWidth: function getWidth() {
              return 80;
            },
            getVGap: function getVGap() {
              return 80;
            },
            getHGap: function getHGap() {
              return 20;
            },
          },
        });

        graph.node(function (node) {
          let position = "bottom";
          let rotate = 0;
          if (!node.children) {
            position = "bottom";
            rotate = -Math.PI / 8;
          }
          return {
            label: node.label,
            labelCfg: {
              position,
              // offset: 20,
              style: {
                textAlign: "center",
                rotate,
              },
            },
          };
        });

        if (typeof window !== "undefined")
          window.onresize = () => {
            if (!graph || graph.get("destroyed")) return;
            if (!container || !container.scrollWidth || !container.scrollHeight)
              return;
            graph.changeSize(container.scrollWidth, container.scrollHeight);
          };
        graph.data(this.jsonData);
        graph.render();
        graph.fitView();
        this.graph = graph;
      },
      updateTopo() {
        this.graph.data(this.jsonData);
        this.graph.render();
        this.graph.fitView();
      },

      // 画画线
      drawLink() {
        console.log(this.jsonData.children);
        let len = this.jsonData.children.length || 0;
        let nodeList = this.jsonData.children;

        for (let i = 0; i < len; i++) {
          if (nodeList[i + 1]) {
            let item1 = nodeList[i];
            let item2 = nodeList[i + 1];
            let source = item1.id;
            let target = item2.id;
            console.log({
              source,
              target,
            });
            this.graph.addItem("edge", {
              source,
              target,
              // style: {
              //   endArrow: true,
              // },
              type: "line-arrow",
            });
          }
        }
      },
    },
  };
</script>

<style lang="less" scoped></style>

<style lang="less">
  .g6-component-tooltip {
    .node-cls {
      display: flex;

      span {
        display: flex;
        align-items: center;
        height: 30px;
      }
      :first-child {
        width: 90px;
      }
    }
  }
</style>
