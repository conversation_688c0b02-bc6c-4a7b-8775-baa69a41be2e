import axios from 'axios'
import {
  // MessageBox,
  Notification } from 'element-ui'


// 请求地址前缀
export const requestUrl = process.env.VUE_APP_API_BASE_URL; // 内网生产环境前缀



// import router from '@/router'
import store from '@/store'
import session from '@/utils/session'
const BASE_API = process.env.NODE_ENV === 'production' ? '/core-network' : `${requestUrl}/core-network/`
// const BASE_API = process.env.NODE_ENV === 'production' ? '/portal' : '/core-network'
// const BASE_API = process.env.NODE_ENV === 'production' ? '/portal' : '/solution'


const pending = {}
const CancelToken = axios.CancelToken
const removePending = (key, isRequest = false) => {
  if (pending[key] && isRequest) {
    pending[key]('取消重复请求')
  }
  delete pending[key]
}
const getRequestIdentify = (config, isReuest = false) => {
  let url = config.url
  if (isReuest) {
    url = config.baseURL + config.url.substring(1, config.url.length)
  }
  return ['delete', 'get'].includes(config.method) ? encodeURIComponent(url + JSON.stringify(config.params)) : encodeURIComponent(config.url + JSON.stringify(config.data))
}

const $http = axios.create({
  baseURL: BASE_API,
  // timeout: 20000,
  withCredentials: true
})

// 请求拦截器
$http.interceptors.request.use(config => {
  // console.log('config', config);
  // 拦截重复请求(即当前正在进行的相同请求)
  const requestData = getRequestIdentify(config, true)
  removePending(requestData, true)

  config.cancelToken = new CancelToken((c) => {
    pending[requestData] = c
  })

  return config
}, error => {
  return Promise.reject(error)
})

$http.interceptors.response.use(
  response => {
    // 把已经完成的请求从 pending 中移除
    const requestData = getRequestIdentify(response.config)
    removePending(requestData)
    return response
  },
  error => {
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    } else {
      return Promise.reject(error)
    }
  }
)

// 检查状态码
function checkStatus(res, showErr) {
  if (!res) {
    return {
      code: -1,
      msg: 'Error: 服务器请求出错!',
      data: {}
    }
  }
  if (res.status === 413) {
    return {
      code: -1,
      msg: 'Error: 文件大小超出上传限制!',
      data: {}
    }
  }
  if (res.status === 200 || res.status === 400) {
    return res.data
  }
  return {
    code: -1,
    msg: `Error:${res.status} 服务器出错!`,
    data: res.statusText
  }
}

// 401 时跳转
function handleWithoutPermission() {
  const origin = localStorage.getItem('token')
  const whiteList = ['asiainfo', 'sand', 'yunChange']

  // const routeWhiteList = ['home', 'newHome', 'homePage']
  // router

  // if()

  if (whiteList.indexOf(origin) !== -1) {
    // 在免登录白名单，直接进入
    // alert(origin)
    return
  } else {
    session.removeUser()
    window.location.href = window.location.origin
  }
}
const getFun = (url, params, showError, type, withToken, withAllRes, useMsg, useFullRes) => {
  if (!url) return
  let token = localStorage.getItem('token')
  if (token) {
    params.t = new Date().getTime()
    return $http({
      headers: {'token': token},
      method: type,
      url,
      params
    }).then(checkStatus).then(function (res, isShow) {
          if (useFullRes) {
            return res
          }
          isShow = showError
          if (Number(res.resultCode) === 401) {
            handleWithoutPermission()
          } else if (Number(res.resultCode) === 200 || Number(res.resultCode) === 0) {
            if (withAllRes) {
              return res
            }
            return res.data
          } else {
            if (isShow) {
              let errMsg = '请求出错，请联系系统管理员！'
              if (useMsg) {
                errMsg = res.msg
              }
              // MessageBox.error(errMsg, '错误', {
              //   confirmButtonText: '关闭',
              //   type: 'error',
              //   closeOnClickModal: false,
              //   callback: action => {
              //   }
              // })
              Notification.error({
                title: '错误',
                message: errMsg,
                duration: 3000
              })
            }
            throw new Error(res.msg)
          }
        }
    ).catch(err => {
      throw new Error(err.response ? err.response.data.status : err)
    })
  }
}

const get = (url, params = {}, showError = false, withToken = true, withAllRes = false, useMsg = false, useFullRes = false) => {
  return getFun(url, params, showError, 'get', withToken, withAllRes, useMsg, useFullRes)
}

const deleteAxios = (url, params = {}, showError = true, withToken = true, withAllRes = false) => {
  return getFun(url, params, showError, 'delete', withToken, withAllRes)
}

const post = (url, data = {}, showError = true, withAllRes = false) => {
  return postFun(url, data, showError, 'post', {}, withAllRes)
}


const postFun = (url, data, showError, type, params, withAllRes = false) => {
  if (!url) return
  let token = localStorage.getItem('token')
  if (token) {
  return $http({
    headers: { 'token': token, 'Content-Type': 'application/json' },
    method: type,
    url,
    data,
    params
  }).then(checkStatus).then(function(res, isShow) {
    if (withAllRes) {
      return res
    }
    if (Number(res.resultCode) === 401) {
      handleWithoutPermission()
    } else if (Number(res.resultCode) !== 200) {
      if (showError) {
        // Notification.error(res.msg, '错误', {
        //   confirmButtonText: '关闭',
        //   type: 'error',
        //   closeOnClickModal: false,
        //   callback: action => {
        //   }
        // })
        Notification.error({
          title: '错误',
          message: res.message,
          duration: 3000
        })
      }
      throw new Error(res.msg)
    }
    return res.data
  }).catch(err => {
    if (showError) {
      throw new Error(err.response ? err.response.data.status : err)
    } else {
      throw new Error('')
    }
  })
  }
}

const putAxios = (url, data = {}, showError = true) => {
  return postFun(url, data, showError, 'put')
}

const postParams = (url, params, data = {}, showError = false) => {
  return postFun(url, data, showError, 'post', params)
}
export function apiDownLoad(url, data) {
  return new Promise((resolve) => {
    axios({
      method: 'post',
      url: url,
      params: data,
      contentType: ' multipart/form-data'
    }).then(res => {
      resolve(res.data)
      // eslint-disable-next-line handle-callback-err
    }).catch(err => {
      resolve('error')
    })
  })
}

export default {
  get,
  post,
  postParams,
  putAxios,
  deleteAxios,
  apiDownLoad,
  requestUrl
}
