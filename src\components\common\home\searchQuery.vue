<template>
  <div class="search-class">
    <el-form :model="formInline" class="demo-form-inline">
      <el-row>
        <el-col :span="7">
          <!-- 地市 -->
          <el-form-item prop="city">
            <el-select
              popper-class="selectPopper"
              :popper-append-to-body="false"
              style="width: 100%"
              v-model="formInline.city"
              placeholder="请选择地区"
              @change="changeCity"
              clearable
            >
              <el-option
                v-for="(i, idx) in cityList"
                :key="idx"
                :label="i.label"
                :value="i.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <!-- 厅级 -->
          <el-form-item prop="unit">
            <el-select
              popper-class="selectPopper"
              v-model="formInline.unit"
              style="width: 100%"
              placeholder="请选择厅级"
              :popper-append-to-body="false"
              clearable
              @change="changeUnit"
            >
              <el-option v-for="(i, idx) in unitList" :key="idx" :label="i.label" :value="i.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <!-- 资源 -->
          <el-form-item prop="resource">
            <el-select
              popper-class="selectPopper"
              v-model="formInline.resource"
              style="width: 100%"
              placeholder="请选择资源"
              :popper-append-to-body="false"
              clearable
              @change="changeResource"
            >
              <el-option v-for="(i, idx) in resourceList" :key="idx" :label="i.label" :value="i"></el-option>
              <!-- <el-option label="区域一" value="shanghai"></el-option>
              <el-option label="区域二" value="beijing"></el-option> -->
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item>
            <el-button type="primary" @click="onSearch" style="width: 100%"
              >查询</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "searchQuery",
  components: {},
  props: {
    cityName: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      cityList: [
        {
          label: "内蒙古",
          value: "10",
        },
        {
          label: "呼伦贝尔市",
          value: "0470",
        },
        {
          label: "呼和浩特市",
          value: "0471",
        },
        {
          label: "包头市",
          value: "0472",
        },
        {
          label: "乌海市",
          value: "0473",
        },
        {
          label: "乌兰察布市",
          value: "0474",
        },
        {
          label: "通辽市",
          value: "0475",
        },
        {
          label: "赤峰市",
          value: "0476",
        },
        {
          label: "鄂尔多斯市",
          value: "0477",
        },
        {
          label: "巴彦淖尔市",
          value: "0478",
        },
        {
          label: "锡林郭勒盟",
          value: "0479",
        },
        {
          label: "兴安盟",
          value: "0482",
        },
        {
          label: "阿拉善盟",
          value: "0483",
        },
      ],
      uuid: 'DISTRICT-00001-00010', // 选择地市对应的uuid,默认内蒙古
      unitList: [], // 厅级下拉
      cust_uuid: '', // 选择厅级对应的uuid
      resourceList: [], // 资源下拉
      formInline: {
        city: "",
        unit: "",
        resource: "",
      },
    };
  },
  watch: {
    cityName: {
      handler(n, o) {
        if (n) {
          this.formInline.city = n;
          this.getUnitList();
        }
      },
      deep: true,
    },
    uuid(n) {
      this.unitList = [];
      this.resourceList = [];
      this.formInline.unit = '';
      this.formInline.resource = '';
      if(n != '') {
        this.getUnitList(); // 获取厅级下拉
      }
    },
    cust_uuid(n) {
      this.formInline.resource = '';
      this.resourceList = [];
      if(n != '') {
        this.getResourceList(); // 获取资源下拉
      }
    }
  },
  mounted() {
    this.getCityList();
    this.formInline.city = "10";
    this.uuid = 'DISTRICT-00001-00010';
  },
  methods: {
    /**
     * <AUTHOR>
     * 获取地市下拉 /kams/homepage/getCityList
     * 返回值：
     * {
        "name": "内蒙古",
        "uuid": "DISTRICT-00001-00010"
      }
     */
    getCityList() {
      let self = this;
      nc.rapi
        .request({
          url: "/rc-rm-kams-biz/kams/homepage/getCityList",
          method: "post",
          data: null, // 参数
          headers: { "Content-Type": "application/json;charset=UTF-8"},
        })
        .then((res) => {
          let newArr = res.data;
          if(newArr && newArr.length > 0) {
            newArr.map((i) => {
              this.cityList.forEach((j, idx) => {
                if(i.name == j.label) {
                  j.uuid = i.uuid;
                }
              })
            })
          }
          
        })
        .catch((err) => {
          console.error(err);
          self.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: "数据加载失败，系统异常!",
          });
        });
    },
    // 切换地市
    changeCity(val) {
      if(val) {
        this.uuid = this.cityList.filter(i => i.value == val)[0].uuid;
        this.$emit("transfer", val);
      } else {
        this.uuid = ''
        this.unitList = [];
        this.$emit("transfer", '10');
      }
      this.$emit("clearLines", true)
    },
    /**
     * <AUTHOR>
     * 获取厅级下拉  /kams/homepage/getCustomerByDistrict
     */
    getUnitList() {
      this.unitList = [];
      let self = this;
      nc.rapi
        .request({
          url: `/rc-rm-kams-biz/kams/homepage/getCustomerByDistrict?districtUuid=${this.uuid}`,
          method: "post",
          headers: { "Content-Type": "application/json;charset=UTF-8"},
        })
        .then((res) => {
          let newArr = res.data;
          if(newArr && newArr.length > 0) {
            this.unitList = newArr.map(i => ({
              label: i.name,
              value: i.uuid
            }))
          }
        })
        .catch((err) => {
          console.error(err);
          self.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: "数据加载失败，系统异常!",
          });
        });
    },
    /**
     * <AUTHOR>
     * 切换厅级
     */
    changeUnit(val) {
      this.cust_uuid = val;
      if(!val) {
        this.unitList = [];
      }
      this.$emit("clearLines", true)
    },
    /**
     * <AUTHOR>
     * 获取资源下拉  /kams/homepage/getCircuitByCustomer
     */
    getResourceList() {
      // this.cust_uuid = '91219993a5584e0eafb86efd01f202c3';
      let self = this;
      nc.rapi
        .request({
          url: `/rc-rm-kams-biz/kams/homepage/getCircuitByCustomer?customerUuid=${this.cust_uuid}`,
          method: "post",
          headers: { "Content-Type": "application/json;charset=UTF-8"},
        })
        .then((res) => {
          let newArr = res.data;
          if(newArr && newArr.length > 0) {
            this.resourceList = newArr.map(i => ({
              label: i.no,
              value: i.uuid,
              ...i
            }))
          }
        })
        .catch((err) => {
          console.error(err);
          self.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: "数据加载失败，系统异常!",
          });
        });
    },
    /**
     * <AUTHOR>
     * 切换电路资源
     */
    changeResource(item) {
      if(item) {
        // this.formInline.resource = item.uuid;
        this.$emit('showMapLine', item)
      }
    },
    // 查询
    onSearch() {
      // console.log("查询");
    },
    // 重置
    resetForm() {},
  },
};
</script>

<style lang="scss" scoped>
.mian-border {
  padding-bottom: 2vh;
  border: 1px solid rgba(4, 56, 226, 0.58);
}
::v-deep .selectPopper {
  left: 0px !important;
}
.search-class {
  width: 100%;
  height: 6vh;
  padding-left: 12px;
  padding-top: 0.8vh;
  background-color: rgba(23, 70, 137, 0.25);
}
</style>