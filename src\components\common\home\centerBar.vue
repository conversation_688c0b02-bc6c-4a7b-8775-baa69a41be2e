<template>
  <div class="jsc-content">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(i, idx) in tabList" :key="'tab'+idx" :label="i.label" :name="i.key">
      </el-tab-pane>
      
      <home-Barchart ref="barChart" :act.sync="activeName" :mapCityName="mapCityName" :xData.sync="xData" :yData.sync="yData" :xUuids.sync="xUuids"></home-Barchart>
    </el-tabs>
  </div>
</template>

<script>
import homeBarchart from "@/components/common/Charts/homeBarchart";
export default {
  name: "centerBar",
  components: { homeBarchart },
  props: {
    mapCityName: {
      type: String,
      default: () => "内蒙古",
    },
    cityUuid: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      activeName: "BOUTIQUE_NETWORK",
      tabList: [
        { label: '精品网', value: '1', key: 'BOUTIQUE_NETWORK'},
        { label: '互联网', value: '2', key: 'INTERNET'},
        { label: '以太网电路', value: '3', key: 'ETHERNET'},
        { label: '裸光纤', value: '4', key: 'BARE_FIBRE'},
        { label: '其他', value: '5', key: 'OTHER'}
      ],
      xData: [],
      yData: [],
      xUuids: [], // x轴地市对应的uuid
    };
  },
  watch: {
    mapCityName(n) {},
    activeName(n) {
      if(n) {
        this.getCircuitKindNumGroupByDistrict(n)
      }
    },
    cityUuid(n) {
      if(n) {
        this.getCircuitKindNumGroupByDistrict(this.tabList[0].key);
      }
    }
  },
  mounted() {
    this.getCircuitKindNumGroupByDistrict(this.tabList[0].key)
  },
  methods: {
    /**
     * <AUTHOR>
     * 获取各地市不同维度的电路数量柱状图 /kams/homepage/getCircuitKindNumGroupByDistrict
     * 入参：districtUuid(传空值默认查地市，传具体区域uuid查该区域的子区域的电路数量)
     */
    getCircuitKindNumGroupByDistrict(tabKey) {
      let res = [];
      let xData = [];
      let xUuids = [];
      let yData = [];
      nc.rapi
        .request({
          url: "/rc-rm-kams-biz/kams/homepage/getCircuitKindNumGroupByDistrict",
          method: "post",
          data: {
            districtUuid: this.cityUuid ? this.cityUuid : '',
          }, // 参数
          headers: {
            "Content-Type": "application/json;charset=UTF-8",
          },
        })
        .then((result) => {
          if(Object.keys(result.data).length>0) {
            res = result.data[tabKey];
            result.data[tabKey].map((i, idx) => {
              xData.push(i.district_name);
              xUuids.push(i.district_uuid);
              yData.push(i.count_num);
            })
            this.xData = xData;
            this.yData = yData;
            this.xUuids = xUuids;
          }
        })
        .catch((err) => {
          console.error(err);
          scope.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: "数据加载失败，系统异常!",
          });
        });
    },
    /**
     * <AUTHOR>
     * 切换tab
     */
    handleClick(tab, event) {
      this.activeName = tab.name;
    },
  },
};
</script>

<style lang="scss" scoped>
.mian-border {
  padding-bottom: 1vh;
  border: 1px solid rgba(4, 56, 226, 0.58);
}
.main-all {
  margin-top: 1vh;
}
.jsc-content {
  // background: url("../../../assets/images/home-bottom-gb.png");
  background: url("../../../assets/images/index-table.png");
  width: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 1vh 0px;
}
.jsc-content ::v-deep .el-tabs__nav-scroll {
  width: 45%;
  margin: 0 auto;
}
::v-deep .el-tabs__header {
  margin: 0 0 1.5vh;
}
</style>