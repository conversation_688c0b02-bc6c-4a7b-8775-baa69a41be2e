<template>
    <div class="modal-bg" v-show="show">
        <div class="modal-container" :style="{height:`${766*contrastRadio}px`}">
            <div class="modal-header" :style="{height:`${80*contrastRadio}px`,fontSize:`${20*contrastRadio}px`}">
                <span class="modal-header-font">{{title}}</span>
            </div>
            <div class="modal-main" :style="{height:`${620*contrastRadio}px`}">
                <slot></slot>
            </div>
            <div class="modal-footer">
                <button @click="close">关 闭</button>
                <button @click="submit" style="margin-left:16px;">确 定</button>
            </div>
        </div>
    </div>
</template>
<script>
export default{
    name: 'modal',
    data() {
        return {}    
    },
    props: {
        show: {    // 控制弹窗展示
            type: Boolean,
            default: true,    
            required: true,   // 必传递
        },
        contrastRadio:{
            type: Number,
            default: 1
        },
        title: {
            type: String,
            default: 'title',
        }
    },
    methods: {
        // 通过事件绑定及$emit来执行父组件的方法，改变弹窗展示状态
        close() {
            this.$emit("hideModal");    
        },
        submit() {
            this.$emit("submit");    
        }
    }
}
</script>
<style>
.modal-bg{
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, .4);
    z-index:999;
}
.modal-container{
    background: url("../../../assets/text/tanchuang-a.png") center
    center no-repeat;
    background-size: 100% 100%;
    border-radius: 8px;
    width:52.7%;
}

.modal-header{
    width:100%;
    color: rgba(255, 255, 255, 1);
    display: flex;
    justify-content: center;
    align-items: center;
}
.modal-header-font{
    box-shadow: 0px 0px 16px 0px rgba(0, 147, 255, 0.1);
    font-family: PingFangSC-Semibold;
}
.modal-main {
    padding: 16px 30px;
    color:#fff;
}
.modal-footer{
    display: flex;
    justify-content: center;
    align-items: center;
}
.modal-footer button{
    width:100px;
    border: 1px solid rgba(2, 219, 255, 1);
    color:#fff;
    border-radius: 5px;
    background-color: rgba(4, 15, 45, 0.2);
    box-shadow: undefined, inset 0px 0px 15px 0px rgba(2, 219, 255, 0.75);
}
</style>