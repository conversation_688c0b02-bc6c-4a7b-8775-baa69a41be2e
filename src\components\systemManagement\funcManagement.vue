<template>
<div style="height: 70%;">
  <div class="nc-query">
    <el-form :inline="true" :model="form" label-width="100px">
      <el-form-item class="formItem" label="功能点名称">
        <el-input v-model="form.title" placeholder="请输入功能点名称" clearable></el-input>
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button type="primary" @click="getMenu();">查询</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div style="margin:0 0 15px 0">
    <el-button type="primary" size="mini" icon="el-icon-plus" @click="addUser">新增</el-button>
    <el-button size="mini" @click="downloadModel">下载模板</el-button>
    <el-button size="mini" type="warning" plain @click="importMenuFuncConfig">导入</el-button>
    <el-button size="mini" type="danger" plain @click="exportMenuFuncConfig">导出</el-button>
  </div>
  <el-table border :data="tableBody" :header-cell-style="{
    color: '#9ED8FF',
    'background-color': 'rgba(15, 52, 124, 1)',
    'text-align': 'center',
    'font-size': '14px',
    'border-right': '1px rgba(15, 0, 137, .8) solid'
  }" height="95%" style="border-radius: 4px 4px 0 0;
      border:none;margin-top: 5px;">
    <el-table-column type="selection" width="55">
    </el-table-column>
    <el-table-column align='center' v-for="(i, idx) in tableColumns" :key="idx" :label="i.displayName"
      :prop="i.columnName" show-overflow-tooltip>
      <template slot-scope="scope">
        <el-dropdown size="mini" trigger="click" :ref="'messageDrop' + scope.row.id" v-if="i.columnName == 'roleId'"
          @visible-change="val => visibleChange(val, scope.row.roleId, 'tree' + scope.row.id)">
          <el-button size="mini">管理员<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-tree :data="roleTreeData" show-checkbox :default-checked-keys="scope.row[i.columnName]"
              default-expand-all node-key="id" :ref="'tree' + scope.row.id" highlight-current style="width: 140px;">
            </el-tree>
            <div style="float: right;margin-right: 10px">
              <el-button type="text" icon="el-icon-close"
                @click="closeModels('tree' + scope.row.id, scope.row.roleId, 'messageDrop' + scope.row.id)"></el-button>
              <el-button type="text" icon="el-icon-check"
                @click="getCheckedKeys('tree' + scope.row.id, scope.row.id, 'messageDrop' + scope.row.id)"></el-button>
            </div>
          </el-dropdown-menu>
        </el-dropdown>
        <span v-else>{{ scope.row[i.columnName] ? scope.row[i.columnName] : "" }}</span>
      </template>

    </el-table-column>
    <el-table-column fixed="right" label="操作" width="100">
      <template slot-scope="scope">
        <el-button title="编辑" type="text" size="mini" icon="el-icon-edit" style="margin-right: 5px"
          @click="addUser(scope.row)"></el-button>
        <el-popconfirm confirm-button-text='好的' cancel-button-text='不用了' icon="el-icon-info" icon-color="red"
          title="确定删除这个功能点吗？" @confirm="deleteRole(scope.row)">
          <el-button title="删除" slot="reference" type="text" size="small" icon="el-icon-delete"></el-button>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination class="page-list" @size-change="handleSizeChange" @current-change="handleCurrentChange"
    :current-page.sync="pageNumber" :page-sizes="[1, 10, 20, 30, 40, 50]" :page-size="pageSize" background
    layout="total, prev, pager, next, sizes" :total.sync="total" style="float: right">
  </el-pagination>
  <el-dialog :title="`${formData.id ? '修改' : '新增'}功能点`" width="600px" :visible.sync="visible" append-to-body
    :before-close="closeModel" @close="resetForm" :close-on-click-modal="false">
    <el-form ref="formData" :model="formData" label-width="110px" size="small" :rules="rules">
      <el-form-item label="功能点名称 :" prop="title">
        <el-input v-model.trim="formData.title" style="width: 100%" placeholder="功能点名称" />
      </el-form-item>
      <el-form-item label="接口地址 :" prop="path">
        <el-input v-model.trim="formData.path" style="width: 100%" placeholder="接口地址" />
      </el-form-item>
      <div style="text-align: center; margin-top: 20px">
        <el-button size="small" @click="closeModel()">
          取消
        </el-button>
        <el-button type="primary" size="small" @click="subForm('formData')">
          提交
        </el-button>
      </div>
    </el-form>
  </el-dialog>
  <!-- 数据导入 -->
  <el-dialog title="功能点数据导入" :visible.sync="dialogDataImport2" @close="cancel">

    <div class="flex-div uploaditem">
      <el-tooltip class="item" effect="dark" :content="tag.name" placement="top-start" v-for="(tag, index) in fileList"
        :key="index">
        <el-tag style="margin-right:10px;display:flex;" :disable-transitions="false" @close="handleClose(index)"
          closable @click="downloadFile(tag)"><i class="el-icon-paperclip"></i><span
            class="tagtext">{{ tag.name }}</span></el-tag>
      </el-tooltip>
      <el-upload class="upload-demo" drag action :http-request="uploadFile" ref="upload" :limit="fileLimit"
        :on-remove="handleRemove" :file-list="fileList" :on-exceed="handleExceed" :before-upload="beforeUpload"
        :headers="headers" :show-file-list="false" style="width:100%">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <span style="color:red; font-weight: 100;font-size: 14px;">文件类型为xls并且上传文件大小不能超过 50MB!</span>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel()">取 消</el-button>
      <el-button type="primary" @click="handleIng()">确 定</el-button>
    </div>




  </el-dialog>
</div>
</template>

<script>
import permission from '../common/permission.js';
export default {
  directives: {
    permission
  },
  data() {
    return {
      dialogDataImport2: false,
      visible: false,
      //上传后的文件列表
      fileList: [],
      // 允许的文件类型
      fileType: ["xlsx",],
      // fileType: [ "pdf", "doc", "docx", "xls", "xlsx","txt"],
      // 运行上传文件大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      form: {
        title: '',
      },
      formData: {
        roleList: [],
        custIdList: []
      },
      total: 10,
      pageSize: 10,
      pageNumber: 1,
      allCityData: [],
      allCountiesData: [],
      allCustUserRoles: [],
      roleList: [],
      custIdList: [],
      tableColumns: [{
        displayName: '功能点名称',
        columnName: 'title'
      }, {
        displayName: '接口地址',
        columnName: 'path'
      }, {
        displayName: '角色',
        columnName: 'roleId'
      }],
      tableBody: [],
      userInformation: [],
      customerAdded: [],
      noCustomerAdded: [],
      roleTreeData: [],
      customerName: '',
      deleteCustomer: [],
      customerNull: [],
      rules: {
        title: [
          { required: true, message: '请输入功能点名称', trigger: 'blur' },
          { max: 20, message: "账号长度最多20个字符", trigger: "change" }
        ],
        path: [
          { required: true, message: '请输入地址', trigger: 'blur' },
          { max: 50, message: "菜单地址最多50个字符", trigger: "change" },
          {
            validator: function (rule, value, callback) {
              var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
              if (reg.test(value)
              ) {
                callback(new Error("地址中不能含有中文"));
              } else {
                callback();
              }
            }, trigger: 'blur'
          }
        ],
        // name: [
        //   { required: true, message: '请输入姓名', trigger: 'blur' },
        //   { max: 10, message: "角色名称长度最多10个字符", trigger: "change" }
        // ],
        // email: [
        //   // { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        //   // { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
        //   {
        //     required: true,
        //     message: "请输入邮箱",
        //     trigger: "blur"
        //   },
        //   {
        //     validator: function(rule, value, callback) {
        //       if (
        //         /^\w{1,64}@[a-z0-9\-]{1,256}(\.[a-z]{2,6}){1,2}$/i.test(
        //           value
        //         ) == false
        //       ) {
        //         callback(new Error("请输入正确的邮箱地址"));
        //       } else {
        //         callback();
        //       }
        //     },
        //     trigger: "blur"
        //   }
        // ],
        // phoneNumber: [
        //   { required: true, message: '请输入手机号', trigger: 'blur' },
        //   { pattern: /^1[3|5|7|8|9]\d{9}$/, message: '请输入正确的号码格式', trigger: 'change' }
        // ],

        // userType:[
        //   { required: true, message: '请选择用户类型', trigger: 'blur' },
        // ],
        // userGrade: [
        //   { required: true, message: '请选择用户级别', trigger: 'blur' },
        // ],
        // roleNames: [
        //   { type: 'array', required: true, message: '请至少选择一个角色', trigger: 'change' }
        // ],
      },
    }
  },
  created() {
    // this.getAllCity()
    // this.getAllCounties()
    this.getcustUserRoles()
    // this.selectAllUserInfo()
    this.getMenu();
  },
  methods: {
    getcustUserRoles() {
      this.$api.slaApi.getcustUserRoles()
        .then(res => {
          console.log(res);
          this.allCustUserRoles = res.data
          res.data.map(item => {
            this.roleTreeData.push({
              id: item.roleId,
              label: item.roleName
            })
          })
        })
    },
    getMenu() {
      this.$api.slaApi.getMenu({
        type: '1',
        "pageNum": this.pageNumber,
        "pageSize": this.pageSize,
        ...this.form
      })
        .then(res => {
          res.data.records.map(item => {
            if (item.roleId) {
              item.roleId = item.roleId.split(",");
            }
          })
          this.tableBody = res.data.records;
          this.total = res.data.total;
        })
    },
    importMenuFuncConfig() {
      this.dialogDataImport2 = true
    },
    handleIng() {
      // 上传文件的需要formdata类型;所以要转
      if (this.fileList.length != 0) {
        let FormDatas = new FormData();
        FormDatas.append('file', this.fileList[0]);
        this.$api.slaApi.importMenuFuncConfig(FormDatas).then(res => {
          if (res.code == 200) {
            // if (res.data.data.status == 0) {
            //   this.$message({
            //   	type:'error',
            //   	message: res.data.data.importResult
            //   });
            // } else {
            //   if (res.data.data.kpiName != null) {
            this.$message({
              type: 'success',
              message: '上传成功'
            });
            this.cancel();
            this.getMenu()
          } else {
            this.$message({
              type: 'error',
              message: "导入失败"
            });
          }
        });
      } else {
        this.$message({
          type: 'warning',
          message: '请选择导入文件'
        });
      }

    },
    downloadModel() {
      this.$api.slaApi.downloadModel({})
        .then(res => {
          const blob = new Blob([res], {
            // type: 'application/vnd.ms-excel' // 定义格式
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
          });
          let link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.setAttribute('download', '功能点上传模板.xlsx');
          link.click();
          link = null;

          this.$message({
            message: '下载成功',
            type: 'success',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
        })
        .catch(() => {
          this.$message({
            message: '下载失败',
            type: 'error',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
        });
    },
    cancel() {
      this.dialogDataImport2 = false;
      this.fileList = [];
      this.dataValue2 = '';
    },
    //上传文件之前
    beforeUpload(file) {
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 50; //这里做文件大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message({
            message: '上传文件大小不能超过 50MB!',
            type: 'warning'
          });
          return false;
        }
        //如果文件类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传文件格式不正确!");
          return false;
        }
      }
    },
    //上传文件的事件
    uploadFile(item) {
      // console.log(item);
      this.$message({
        type: 'warning',
        message: '文件上传中......'
      });
      this.fileList.push(item.file);//成功过后手动将文件添加到展示列表里
    },
    //上传了的文件给移除的事件，由于我没有用到默认的展示，所以没有用到
    handleRemove() {
    },
    //这是我自定义的移除事件
    handleClose(i) {
      this.fileList.splice(i, 1);//删除上传的文件
      // if(this.fileList.length == 0){//如果删完了
      // 	this.fileflag = true;//显示url必填的标识
      // 	this.$set(this.rules.url,0,{ required: true, validator: this.validatorUrl, trigger: 'blur' })//然后动态的添加本地方法的校验规则
      // }
    },
    //超出文件个数的回调
    handleExceed() {
      this.$message({
        type: 'warning',
        message: '超出最大上传文件数量的限制！'
      });
      return;
    },
    exportMenuFuncConfig() {
      this.$api.slaApi.exportMenuFuncConfig({})
        .then(res => {
          const blob = new Blob([res], {
            // type: 'application/vnd.ms-excel' // 定义格式
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
          });
          let link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.setAttribute('download', '功能点数据.xlsx');
          link.click();
          link = null;

          this.$message({
            message: '下载成功',
            type: 'success',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
        })
        .catch(() => {
          this.$message({
            message: '下载失败',
            type: 'error',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
        });
    },
    // 深拷贝
    extendCopy(p) {
      var c = {};
      for (var i in p) {
        c[i] = p[i];
      }
      c.uber = p;
      return c;
    },
    // 重置表单
    resetQuery() {
      this.form = {
        userName: '',
      };
      this.pageSize = 10;
      this.pageNumber = 1;
      this.getMenu();
    },
    addUser(row) {
      if (row.id) {
        this.formData = this.extendCopy(row);
      } else {
        this.formData = {};
      }
      //   this.addCustomer()
      this.visible = true;
    },
    subForm(formData) {
      if (this.formData.id) {
        this.$refs.formData.validate(valid => {
          if (valid) {
            this.subEditForm(formData);
          }
        });
      } else {
        this.$refs.formData.validate(valid => {
          if (valid) {
            this.subAddForm(formData);
          }
        });

      }
    },
    subAddForm() {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          // console.log(this.formData);
          this.$api.slaApi.addMenu({ ...this.formData, type: '1' })
            .then(res => {
              //  console.log(res);
              if (res.code == 200) {
                this.$message({
                  message: '新增成功',
                  type: 'success'
                });
                this.visible = false;
              } else {
                this.$message.error('新增失败');
              }
              this.getMenu();
            })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    subEditForm() {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          // console.log(this.formData);
          this.$api.slaApi.updateMenuFuncConfig({ id: this.formData.id, title: this.formData.title, path: this.formData.path, type: '1' })
            .then(res => {
              //  console.log(res);
              if (res.code == 200) {
                this.$message({
                  message: '修改成功',
                  type: 'success'
                });
                this.visible = false;
              } else {
                this.$message.error('修改失败');
              }
              this.getMenu();
            })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    deleteRole(row) {
      // deleteRole
      this.$api.slaApi.deleteMenu({ id: row.id })
        .then(res => {
          if (res.code == 200) {
            this.$message({
              message: '删除成功',
              type: 'success'
            });
            this.visible = false;
          } else {
            this.$message.error('删除失败');
          }
          this.getMenu();
        })
    },
    // handleCheckChange(data, checked, indeterminate) {
    //     console.log(data, checked, indeterminate);
    //   },
    getCheckedKeys(trf, id, drop) {
      // modifyRelation
      this.$api.slaApi.modifyRelation({ id: id, roleId: this.$refs[trf][0].getCheckedKeys() + '', "type": "1", })
        .then(res => {
          if (res.code == 200) {
            this.$message({
              message: '更新成功',
              type: 'success'
            });
            this.visible = false;
            this.$refs[drop][0].hide();
          } else {
            this.$message.error('更新失败');
          }
          this.getMenu();
        })
    },
    closeModels(tree, list, drop) {
      this.$refs[drop][0].hide();
      this.setCheckedKeys(list, tree)
    },
    setCheckedKeys(list, drop) {
      this.$refs[drop][0].setCheckedKeys(list);
    },
    visibleChange(val, list, tree) {
      if (!val) {
        this.setCheckedKeys(list, tree)
      }
    },
    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMenu();
    },

    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getMenu();
    },
    addCustomer(id) {
      this.noCustomerAdded = [];
      let params = {
        options: JSON.stringify({
          "current": 1,
          "limit": 10,
          "isQueryCount": true,
          "where": {
            "custName": {
              "$like": this.customerName
            }
          }
        })
      }
      this.$api.slaApi.query('pubCust', JSON.stringify(params))
        .then((result) => {
          // console.log(result.data);
          result.data.map(item => {
            console.log(id);
            this.noCustomerAdded.push({
              userId: id,
              custId: item.custId,
              custName: item.custName,
            })
          })
          // this.noCustomerAdded = result.data;
        })
    },
    updateCustAccount() {
      this.deleteCustomer = []
      let newArr = [...this.customerAdded, ...this.userInformation]
      var obj = {};
      newArr = newArr.reduce((current, next) => {
        obj[next.custId] ? "" : obj[next.custId] = true && current.push(next);
        return current;
      }, []);
      this.customerAdded = newArr;
    },
    handleSelectionChange(val) {
      this.deleteCustomer = [val]
    },
    handleSelectionChange2(val) {
      // console.log(val);
      // let userInformation = []
      // val.map(item => {
      //   userInformation.push({
      //     "userId": this.userInformation.id, 
      //     "custId": item.custId, 
      //     "custName": item.custName
      //   })
      // })
      this.userInformation = val
    },
    // 删除客户
    deleteCustomers() {
      console.log(this.customerAdded, this.deleteCustomer);
      let userId = this.customerAdded[0].userId
      this.customerAdded = this.customerAdded.filter(item => {
        return this.deleteCustomer[0].every(item2 => {
          return item.custId != item2.custId;
        })
      });
      console.log(this.customerAdded);
      if (this.customerAdded.length == 0) {
        this.customerNull = [{
          "userId": userId,
          "custId": '',
          "custName": ''
        }]
      }
    },
    // 保存客户信息
    saveCustInformation() {
      this.$api.slaApi.updateCustAccount({ accountCustList: this.customerAdded.length == 0 ? this.customerNull : this.customerAdded })
        .then(res => {
          // console.log(res);
          if (res.resultCode == 200) {
            this.$message({
              message: res.data,
              type: 'success'
            });
          } else {
            this.$message.error('绑定失败');
          }
          this.selectAllUserInfo()
        })
    },
    resetForm() {
      this.$refs.formData.resetFields();
    },
    closeModel() {
      //   this.dialogFormData = {};
      this.visible = false;
    },
    closeModelss() {
    },

  }

}
</script>

<style lang="less" scoped>
.nc-query {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
  // height: 10vh;
}

/deep/ .el-checkbox__inner {
  background-color: transparent;
  border: 1px solid #02dbff;
}

/deep/.el-button--warning.is-plain,
/deep/.el-button--danger.is-plain {
  background-color: transparent;
}

/deep/.el-button--warning.is-plain:hover {
  // background: #e6a23c9c;
  border-color: #e6a23c99;
  color: #e6a23ca3;
}

/deep/.el-button--danger.is-plain:hover {
  // background: #F56C6C;
  border-color: #f56c6c86;
  color: #f56c6c8a;
}

/deep/.el-upload {
  width: 100%;

  .el-upload-dragger {
    width: 100%;
  }
}

::v-deep {
  .el-dropdown-menu {
    width: 120px !important;
  }

  .el-button--text {
    font-size: 18px !important;
  }

  .el-table__fixed-right-patch {
    background-color: rgba(23, 70, 137, 1);
  }

  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch {
    border: none;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }

  .el-dialog {
    // height: 40vh;
    // width: 60%;
    background: url("../../assets/images/dialogbg.png") center center no-repeat;
    background-size: 100% 100%;
    margin-top: 10vh !important;
  }

  .el-dialog__header {
    justify-content: left;
    background: none;
  }

  .el-dialog__title {
    justify-content: left;
    line-height: 20px;
    font-weight: 800;
    height: 20px;
    display: flex;
    color: rgba(255, 255, 255, 1);
    font-size: 22px;
  }

  .el-dialog__title::before,
  .el-dialog__title::after {
    content: none;
  }

  .el-dialog__headerbtn {
    width: 30px;
    height: 30px;
    float: right;
    top: 10px;
    right: 10px;
    background: url("../../assets/images/x.png") center center no-repeat;
    background-size: 100% 100%;
  }

  .el-dialog__body {
    height: 65%;

    .detail-info {
      max-height: 800px;
      height: 100%;
    }

    .el-form {
      min-height: 100px;
      width: 95%;
      // margin: 30px auto 20px;
      margin-top: 30px;
      text-align: center;

      .el-form-item__label {
        color: #fff;
      }

      .el-input__inner {
        color: #02dbff;
        background: none;
        border: 1px solid rgba(37, 190, 247, 0.5);
        // height: 24px !important;
        line-height: 24px;
      }

      .el-switch__label,
      .el-checkbox {
        color: #fff;
      }

      .el-button--primary {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;

        &:focus {
          background-color: #409EFF;
          border-color: #409EFF;
        }

        &:hover {
          background-color: #409EFF;
          border-color: #409EFF;
        }
      }

      .el-select-dropdown__item:hover {
        color: #409EFF;
        background: #40a0ff27;
      }

      .el-button--default {
        color: #409EFF;
        background: transparent;
        border-color: #409EFF;
      }
    }
  }
}
.el-dropdown-menu {
    background: #193758 !important;
    border: 1px solid transparent !important;
    .el-tree {
      background-color: transparent;
      color: #fff;
    }
    ::v-deep .el-tree-node__content:hover, 
    ::v-deep .el-upload-list__item:hover {
        background-color: #204a79d1!important;
    }
  }

  .el-button--text {
    font-size: 18px !important;
  }

  .el-table__fixed-right-patch {
    background-color: rgba(23, 70, 137, 1);
  }

  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch {
    border: none;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }

.addCustomer {
  height: 5%;
  width: 6%;
  float: left;
  position: relative;
  top: 45%;
  left: 4%;
  background: url("../../assets/images/addCustomer.png") center center no-repeat;
  background-size: 100% 100%;
  transform: rotate(180deg);
}
</style>