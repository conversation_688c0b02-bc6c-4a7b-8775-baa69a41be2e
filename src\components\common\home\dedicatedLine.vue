<template>
  <div class="main-border">
    <nav-bar :title="title"></nav-bar>
    <div
      id="verticalcharts"
      v-loading="uploading"
      element-loading-text="数据加载中"
      element-loading-background="rgba(0, 0, 0, 0)"
      style="height: 30vh"
    ></div>
      <!-- style="height: 334px" -->
  </div>
</template>

<script>
import navBar from "@/components/common/home/<USER>";
import echarts from "echarts";
export default {
  name: "dedicatedLine",
  components: { navBar },
  data() {
    return {
      activeName: "first",
      title: "专线占比",
      nameList: [ // 电路性质列表
        {name: '其他', value: 'OTHER'},
        {name: '裸光纤', value: 'BARE_FIBRE'},
        {name: '互联网', value: 'INTERNET'},
        {name: '以太网电路', value: 'ETHERNET'},
        {name: '精品网', value: 'BOUTIQUE_NETWORK'}
      ],
      cirData: []
    };
  },
  props: {
    cityUuid(n) {
      if(n) {
        this.getCircuitKindNum()
      }
    }
  },
  watch: {
    cirData: {
      handler(n,o) {
        if(n && n.length) {
          this.verticalcharts();
        }
      },
      deep: true
    },
    cityUuid(n) {
      if(n) {
        this.getCircuitKindNum();
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.getCircuitKindNum()
      const resizeOb = new ResizeObserver((entries) => {
        for (const entry of entries) {
          echarts.getInstanceByDom(entry.target).resize();
        }
      });
      resizeOb.observe(document.getElementById("verticalcharts"));
    }, 10);
  },
  methods: {
    /**
     * <AUTHOR>
     * 获取专线占比接口 /kams/homepage/getCircuitKindNum
     * districtUuid(传空值默认查全省，传地市区域uuid查该地市的对应数量)
     */
    getCircuitKindNum() {
      nc.rapi
        .request({
          url: `/rc-rm-kams-biz/kams/homepage/getCircuitKindNum?districtUuid=${this.cityUuid}`,
          method: "post",
          headers: {"Content-Type": "application/json;charset=UTF-8"}
        })
        .then((res) => {
          let tempArr = [];
          if(Object.keys(res.data).length>0) {
            Object.keys(res.data).map((i, idx) => {
              tempArr.push({name: Object.keys(res.data)[idx], value: Object.values(res.data)[idx].toString()});
            })
            tempArr.forEach(i => {
              i.name = this.nameList.filter(j => j.value == i.name)[0].name;
            })
          }
          // console.log('tempArr', tempArr);
          this.circuitKindNum = [...tempArr];
          this.verticalcharts()
        })
        .catch((err) => {
          console.error(err);
          this.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: "数据加载失败，系统异常!",
          });
        });
    },
    verticalcharts() {
      var myvertical = echarts.init(document.getElementById("verticalcharts"));
      let option = {
        title: {
          left: "center",
        },
        tooltip: {
          trigger: "item",
          textStyle: {
            color: "rgba(255, 255, 255, 0.8)",
          },
          extraCssText: "box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
          backgroundColor: "rgba(0, 10, 49, 0.7)",
          axisPointer: {
            type: "none", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        color: [
          "#fbee4b",
          "#fba050",
          "#a14afe",
          "#28edf6",
          "#2197f7",
          "#42c69c",
        ],
        grid: {
          left: "5%",
          right: "5%",
          bottom: "7%",
          top: "0%",
        },
        legend: {
          x: "center",
          y: "bottom",
          orient: "horizontal",
          formatter: "{a|{name}}",
          right: "9%",
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            padding: [0, 10, 0, 5],
            rich: {
              a: {
                width: 60,
                fontSize: 12,
                color: "#fff",
                borderWidth: 1
              },
            },
          }, //图例设置
          itemGap: 8,
        },
        series: [
          {
            // name: this.title,
            type: "pie",
            radius: ["50%", "28%"],
            center: ["50%", "38%"],
            label: {
              normal: {
                show: true,
                formatter: "{c}",
                rich: {
                  total: {
                    fontSize: 15,
                    color: "inherit",
                  },
                  active: {
                    fontSize: 15,
                    lineHeight: 30,
                  },
                },
              },
            },
            labelLine: {
              normal: {
                show: true, //控制线条显示
                length: 35,
              },
            },
            data: this.circuitKindNum,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      option && myvertical.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
.main-border {
  // height: 34vh;
  padding-bottom: 1vh;
  border: 1px solid rgba(4, 56, 226, 0.58);
}
</style>