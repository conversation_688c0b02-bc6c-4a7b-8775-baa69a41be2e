{"queryConfig": {"columns": [{"displayName": "所属管理区域", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "regionName", "tableColumnName": "regionName"}, {"displayName": "所属局站", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "stationId", "tableColumnName": "stationId"}, {"displayName": "机房名称", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "chinaName", "tableColumnName": "chinaName"}], "classId": "spcRoom", "className": "机房"}, "tableConfig": {"columns": [{"displayName": "机房名称", "type": "string", "columnName": "chinaName"}, {"displayName": "所属管理区域", "type": "string", "columnName": "regionName"}, {"displayName": "所属局站", "type": "string", "columnName": "stationId"}, {"displayName": "机房类型", "type": "string", "columnName": "type"}, {"displayName": "机房等级", "type": "string", "columnName": "clazz"}, {"displayName": "产权性质", "type": "string", "columnName": "propChar"}, {"displayName": "局站等级", "type": "string", "columnName": "stationGrade"}, {"displayName": "是否共享", "type": "string", "columnName": "isShare"}, {"displayName": "共享单位", "type": "string", "columnName": "shareUnit"}, {"displayName": "产权归属", "type": "string", "columnName": "propertyBelong"}, {"displayName": "所属专业", "type": "string", "columnName": "speciality"}], "selectColumns": ["deviceName", "chinaName", "regionName", "stationId", "type", "clazz", "propChar", "stationGrade", "isShare", "shareUnit", "propertyBelong", "speciality"]}}