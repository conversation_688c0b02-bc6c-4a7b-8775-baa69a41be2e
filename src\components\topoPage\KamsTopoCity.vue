<!-- 
    拓扑vue样例，由其他前端模块引入nc-ui-topology@2.1.1及以上版本，按照此样例文件使用
    提供了各类参数、扩展方法、回调事件等，可参考下面例子，并结合ht组件自由扩展。
 -->
<template>
  <div id="rmTopoDiv">
    <TopologyIndexVue :type="type" :uuid="uuid" :dataId="dataId" :dataType="dataType" ref="TopologyIndexVue"
                      :callback="topologyCallback" :topologyService="topologyService"
                      @setTopologyView="setTopologyView"></TopologyIndexVue>
  </div>
</template>
<script>
import * as commonUtil from "@/utils/CommonUtil";
let TopologyIndexVue = nc.topo.TopologyComponentIndex;
export default {
  name: "KamsTopoCity",
  components: {
    TopologyIndexVue
  },
  props: {
    uuid: {
      type: String,
      default:''
    },
  },
  data() {
    return {
      //拓扑类型
      type: '',
      //拓扑入参资源id
      dataId: '',
      //拓扑入参资源类型
      dataType: '',
      //拓扑后台服务名称（不传递则默认使用nc-topology）
      topologyService: 'nc-topology',
      //拓扑底层返回拓扑实例，便于扩展
      topologyView: {}
    }
  },
  created: function(){
    this.type = 'resourceTop-City';//JKYYSPTN02  菜单: 双节点双路由-非PON 接入
    this.dataType='KAMS_DISTRICT|||ab7aea50-69c8-4ec0-b232-bc4496b74fc9|';
    this.dataId = this.uuid;
  },
  methods: {
    //子页面返回拓扑实例
    setTopologyView: function(view){
      this.topologyView = view;
      // commonUtil.initTopoView(this);
    },
    //拓扑数据加载后回调方法
    topologyCallback: function(){
      let self = this;
      commonUtil.initTopo(self);
      commonUtil.addTncmMenu(self);
      commonUtil.addHasSubTopoEvent(self);
      //查看
      // commonUtil.addViewPopMenu(self);

      self.topologyView._graphView.addInteractorListener(function (e) {
        if (e.kind === "doubleClickData" &&e.data.getAttr("type") === "DISTRICT") {
          self.provinceVisible = false;
          self.districtVisible = true;
          self.districtUuid = e.data._attrObject.uuid;
          self.$emit('openCityNode', e.data._attrObject.uuid)
        }
      });

    },//设备面板方法
      //拓扑页面扩展方法
    topologyViewExt: function(){
      let view = this.topologyView;

      //自定义扩展toolbar（新增一个按钮）
      var button = new ht.ui.Button();
      button.setStyle('button-minor');
      button.setIcon('ZOOMIN');
      button.setText('放大');
      button.on('click', function(e) {
        view._graphView.zoomIn(true);
      });
      // view._toolBar.addView(button, {
      //     marginTop: 10,
      //     marginLeft: 10
      // });
      view._toolBar.addToDOM(view, { x: 0, y: 0, width: window.innerWidth-240, height: 50 });

      //自定义扩展dataModel（属性变化监听）
      view._dataModel.onDataPropertyChanged = function(data, e){
        console.info(data);
        console.info(e);
      };
    }
  }
}
</script>

<style scoped>

</style>