import axios from "@/utils/api.request";
const BASEURL = '/otnNpv'
const core = {
    // 网元(ACE设备)
    getAceDevice(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getAceDevice`,
        method: "post",
        data: inParams,
      });
    },
    // 板卡
    getAceCard(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getAceCard`,
        method: "post",
        data: inParams,
      });
    },
    // 端口
    getAcePort(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getAcePort`,
        method: "post",
        data: inParams,
      });
    },
    // 链路
    getAceCircuit(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getAceCircuit`,
        method: "post",
        data: inParams,
      });
    },
    // 电路
    getTDwdCircuitDetail(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getTDwdCircuitDetail`,
        method: "post",
        data: inParams,
      });
    },
    // 数通网元与核心网元连接关系
    getTDwdCircuitNet(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getTDwdCircuitNet`,
        method: "post",
        data: inParams,
      });
    },
    // 导出网元(ACE设备)
    exportAceDevice(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/exportAceDevice`,
        method: "post",
        data: inParams,
        responseType: "blob",
      });
    },
    // 导出板卡
    exportAceCard(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/exportAceCard`,
        method: "post",
        data: inParams,
        responseType: "blob",
      });
    },
    // 导出端口
    exportAcePort(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/exportAcePort`,
        method: "post",
        data: inParams,
        responseType: "blob",
      });
    },
    // 导出链路
    exportAceCircuit(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/exportAceCircuit`,
        method: "post",
        data: inParams,
        responseType: "blob",
      });
    },
    // 导出电路
    exportTDwdCircuitDetail(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/exportTDwdCircuitDetail`,
        method: "post",
        data: inParams,
        responseType: "blob",
      });
    },
    // 导出数通网元与核心网元连接关系
    exportTDwdCircuitNet(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/exportTDwdCircuitNet`,
        method: "post",
        data: inParams,
        responseType: "blob",
      });
    },
    // 新增电路
    insertTDwdCircuitDetail(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/insertTDwdCircuitDetail`,
        method: "post",
        data: inParams,
      });
    },
    // 新增数通网元与核心网元连接关系
    insertTDwdCircuitNet(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/insertTDwdCircuitNet`,
        method: "post",
        data: inParams,
      });
    },
    // 修改电路
    updateTDwdCircuitDetail(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/updateTDwdCircuitDetail`,
        method: "post",
        data: inParams,
      });
    },
    // 修改数通网元与核心网元连接关系
    updateTDwdCircuitNet(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/updateTDwdCircuitNet`,
        method: "post",
        data: inParams,
      });
    },
    // 核心网资源-网元
    getTDwdCoreNet(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getTDwdCoreNet`,
        method: "post",
        data: inParams,
      });
    },
    // 导出核心网资源-网元
    exportTDwdCoreNet(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/exportTDwdCoreNet`,
        method: "post",
        data: inParams,
        responseType: "blob",
      });
    },
    // 电路状态下拉框预
    getTransCircuitStatusSelectData(inParams) {
      return axios.request({
        url: `${BASEURL}/transferResources/getTransCircuitStatusSelectData`,
        method: "post",
        data: inParams,
      });
    },
    // A/Z端的归属地市下拉
    getTransCircuitAZSelectData(inParams) {
      return axios.request({
        url: `${BASEURL}/transferResources/getTransCircuitAZSelectData`,
        method: "post",
        data: inParams,
      });
    },
    // 电路速率/电路带宽 下拉
    getTransCircuitRateSelectData(inParams) {
      return axios.request({
        url: `${BASEURL}/transferResources/getTransCircuitRateSelectData`,
        method: "post",
        data: inParams,
      });
    },
    // 拼接路由导入
    getTransRouteImport(inParams) {
      return axios.request({
        url: `${BASEURL}/transferResources/getTransRouteImport`,
        method: "post",
        data: inParams,
      });
    },
    // 电路名称下拉
    getCircuitNameSelectData(inParams) {
      return axios.request({
        url: `${BASEURL}/dataCommunicate/getCircuitNameSelectData`,
        method: "post",
        data: inParams,
      });
    },

}
export default core;