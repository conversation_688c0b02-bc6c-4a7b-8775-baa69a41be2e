<template>
	<!-- 告警监控列表 - 查看详情 -->
	<div
		class="detail-info"
		v-loading="detailLoading"
		element-loading-text="拼命加载中..."
	>
		<div class="cards">
			<!-- <el-card class="box-card" shadow="never" v-for="(i, idx1) in info.detail" :key="'card'+idx1">
				<div slot="header" class="card-title">
					<span>{{i.title?i.title:null}}</span>
				</div>
				<el-row :gutter="16">
					<el-col :span="j.span ? j.span : 8" v-for="(j, idx2) in i.list" :key="'j'+idx2" class="card-items">
						<div class="card-item" v-if="j.v">
							<span class="label"> {{ j.l }}</span>
							<span :class="j.span ? 'val w800' : 'val'" :style="{color: j.v == '一级告警' ? '#FF4349' : j.v == '二级告警' ? '#FF8B00': j.v == '三级告警' ? '#87D067': '0086E5'}" :aria-label="j.v"> {{ j.v }}</span>
						</div>
					</el-col>
				</el-row>
			</el-card> -->
      <el-row>
        <el-col :span="14">
          <div class="list-title">
		      	<span>工单信息</span>
		      </div>
          <div>
            <el-row>
              <el-col :span="8" class="gdxq">
                <el-row class="gdxqtitle"> <img src="../../../assets/images/gdxqicon.png" alt=""> 告警标题</el-row>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="bottom"
                >
                  <template slot="content">
                    <p style="max-width:300px;">
                      {{detailData.alarmTitle}}
                    </p>
                  </template>
                  <!-- <el-row v-if="detailData.alarmTitle.length > 20" class="gdxqbg t-ellipsis"><marquee>{{detailData.alarmTitle}}</marquee></el-row>
                  <el-row v-else class="gdxqbg t-ellipsis">{{detailData.alarmTitle}}</el-row> -->
                  <marquee class="gdxqbg t-ellipsis">{{detailData.alarmTitle}}</marquee>
                </el-tooltip>
                
              </el-col>
              <el-col :span="8" class="gdxq">
                <el-row class="gdxqtitle"> <img src="../../../assets/images/gdxqicon.png" alt=""> 告警发生时间</el-row>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="bottom"
                >
                  <template slot="content">
                    <p style="max-width:300px;">
                      {{detailData.occurrenceTime}}
                    </p>
                  </template>
                  <!-- <el-row v-if="detailData.occurrenceTime.length > 20" class="gdxqbg t-ellipsis"><marquee>{{detailData.occurrenceTime}}</marquee></el-row>
                  <el-row v-else class="gdxqbg t-ellipsis">{{detailData.occurrenceTime}}</el-row> -->
                  <marquee class="gdxqbg t-ellipsis">{{detailData.occurrenceTime}}</marquee>
                </el-tooltip>
              </el-col>
              <el-col :span="8" class="gdxq">
                <el-row class="gdxqtitle"> <img src="../../../assets/images/gdxqicon.png" alt=""> 网元名称</el-row>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="bottom"
                >
                  <template slot="content">
                    <p style="max-width:300px;">
                      {{detailData.neName}}
                    </p>
                  </template>
                  <!-- <el-row v-if="detailData.locationInfo.length > 20" class="gdxqbg t-ellipsis"><marquee>{{detailData.locationInfo}}</marquee></el-row>
                  <el-row class="gdxqbg t-ellipsis">{{detailData.locationInfo}}</el-row> -->
                  <marquee class="gdxqbg t-ellipsis">{{detailData.neName}}</marquee>
                </el-tooltip>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" style="padding: 0 20px;">
                <el-row class="gdxqtitle"> <img src="../../../assets/images/gdxqicon.png" alt=""> 所属区域</el-row>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="bottom"
                >
                  <template slot="content">
                    <p style="max-width:300px;">
                      {{detailData.alarmRegion}}
                    </p>
                  </template>
                  <!-- <el-row v-if="detailData.alarmRegion.length > 20" class="gdxqbg t-ellipsis"><marquee>{{detailData.alarmRegion}}</marquee></el-row>
                  <el-row v-else class="gdxqbg t-ellipsis">{{detailData.alarmRegion}}</el-row> -->
                  <marquee class="gdxqbg t-ellipsis">{{detailData.alarmRegion}}</marquee>
                </el-tooltip>
              </el-col>
              <el-col :span="8" style="padding: 0 20px;">
                <el-row class="gdxqtitle"> <img src="../../../assets/images/gdxqicon.png" alt=""> 设备厂家</el-row>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="bottom"
                >
                  <template slot="content">
                    <p style="max-width:300px;">
                      {{detailData.vendor}}
                    </p>
                  </template>
                  <!-- <el-row v-if="detailData.vendor.length > 20" class="gdxqbg t-ellipsis"><marquee>{{detailData.vendor}}</marquee></el-row>
                  <el-row v-else class="gdxqbg t-ellipsis">{{detailData.vendor}}</el-row> -->
                  <marquee class="gdxqbg t-ellipsis">{{detailData.vendor}}</marquee>
                </el-tooltip>
              </el-col>
              <el-col :span="8" style="padding: 0 20px;">
                <el-row class="gdxqtitle"> <img src="../../../assets/images/gdxqicon.png" alt=""> 工单号</el-row>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="bottom"
                >
                  <template slot="content">
                    <p style="max-width:300px;">
                      {{detailData.workOrderNumberJT}}
                    </p>
                  </template>
                  <!-- <el-row v-if="detailData.mainFormId.length > 20" class="gdxqbg t-ellipsis"><marquee>{{detailData.workOrderNumberJT}}</marquee></el-row>
                  <el-row v-else class="gdxqbg t-ellipsis">{{detailData.workOrderNumberJT}}</el-row> -->
                  <marquee class="gdxqbg t-ellipsis">{{detailData.workOrderNumberJT}}</marquee>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </el-col>
        <el-col :span="10">
          <div class="list-title">
		      	<span>当前轨迹</span>
		      </div>
          <div>
            <el-row style="margin-top: -5px;">
              <el-col :span="12" class="dqhj">
                <el-row class="dqgjtitle">环节名称</el-row>
                <el-row class="dqgj">{{currentProcess[0].status}}</el-row>
                <el-row class="dqgjimg"></el-row>
              </el-col>
              <el-col :span="12" class="dqhj">
                <el-row class="dqgjtitle">处理人</el-row>
                <el-row class="dqgj">{{currentProcess[0].userName}}</el-row>
                <el-row class="dqgjimg"></el-row>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" class="dqhj">
                <el-row class="dqgjtitle">电话</el-row>
                <el-row class="dqgj">{{currentProcess[0].taskOwnerMobile}}</el-row>
                <el-row class="dqgjimg"></el-row>
              </el-col>
              <el-col :span="12" class="dqhj">
                <el-row class="dqgjtitle">处理内容</el-row>
                <el-row class="dqgj">{{currentProcess[0].status == '派单' ? '已派单' : currentProcess[0].status == '接单' ? '已接单' : '返单'}}</el-row>
                <el-row class="dqgjimg"></el-row>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
		</div>

		<div class="list-title-flow">
			<span>流转监控</span>
		</div>
    <!-- <el-button class="manualDispatch" size="mini" @click="manualDispatch">手动派单</el-button> -->
    <el-tooltip
        class="item"
        effect="dark"
        placement="top"
      >
        <template slot="content">
          <p style="max-width:300px;" v-permission="'100006'">
            手动派单
          </p>
        </template>
      <div class="manualDispatch" @click="manualDispatch"></div>
    </el-tooltip>
		<div id="flowChart"></div>
	</div>
</template>

<script>
import * as echarts from 'echarts';
import detailTable from "@/components/common/Table/detailTable.vue";
import marquee from '@/components/common/marquee/index.vue'
import permission from '../permission.js';
export default {
directives: {
  permission
},
  name: "detailModal",
	components: {
		detailTable,
    marquee
	},
  props: {
    mainFormId: {
      type: String
    }
  },
  watch: {
    mainFormId: {
      handler(n, o) {
        this.mainFormId = n
        this.getFlowChartData();
      },
      deep: true
    },
  },
  data() {
    return {
      detailLoading: false,
      info: {
			},
      newPoints: [],
      // 已完成
      completed : "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAF8AAAApCAYAAABTN/UpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAB60lEQVRoge3bP4sTURSG8d+EBUFEWyWuVjai/SKyhVoItlpbWPgNxM7Zyj8giAprZOOCioUgWtjoxrhGkP0Igp0IYiGIWNhoLGbEFEkMOsldmPM0E05y4eU5OXdmYCazttIX/CvrONk/duZz9u0d5Lgw6eK58ri98lj14DxeZZ32UQuLnxTymbABjWmlqgmX8BgvbfR2lrUcS5MsDvn/zxU8VDRgV1nLTdCAkF8NV/EA6zZ6zbKW+0sDQn51XMNdxQTsLmu5MQ0I+dVyHauKCdhT1nIjGhDyq+cmWooJ2FvWckMaEPKnwy0soztuAkL+9Gj504D5spYbaEDIny4txRR0h52E50YsCqqjVR67NnpHLCx+UN4Jxz9/NrRwGy8G7wNC/uxooa24Cmr2t+2LbWfGLCu2+m7WaR8O+bPnBrbiSWw7abiMQyE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/ISE/IQ28wUVkibPUjgZO4ADuYEvaOPWigS84Xn5+hB0pA9WJ33v+d5zCWzxDc+SKoDIGT7g/cFbxlsVz7E+SqEYMe3RkCe/xFKfxeqaJasSo53ZW8RH3cA6dmSWqlp/4mjrEKDJrK+O+P4j7mB/3o01Mwya+gPgFRvxfvsbCbMMAAAAASUVORK5CYII=",
      // 当前
      current : "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAF8AAAApCAYAAABTN/UpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAB/ElEQVRoge3aPWsUURSA4WeXgOCCtkqMFkkaWeuksDE2gpWglYWV+A/EzkmlCIKoELcxoGIhiBY2imwiguQHbCGEbcQgFoKELNj4UcyIKbLrorN7A3PeZoYzc+Hwnjl3Zi63tjUz91Pwr6zibGN97Ysz5yHD1WEHTxTHfaWnVQ2u4E1vdv5kozn9WS6fIQtQH1VWFeE6nmGl1+keKGIZFocZHPL/nxt4Ii/AwSKWGaIAIb8cbuIxVnud7mQRy/ylACG/PG7hgbwDDhWxzIAChPxyuY1leQccLmKZPgUI+eVzFy15BxwpYpkdChDyR8M9LKE9qANC/uho+VOAqSKW2VaAkD9aWvIuaO/0Ep7oMygoj1ZxbPc63YVGc/qj4k+4VqztxPLC6LmEi1hoNKc3iCd/nLTk0/xKr9M90Vhf2wj542VJ/sC3e7Pzx0P++LmDvXgec346NuNTMyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyEhPyF1vMM11BLnUjnqOI0m7mNP2nSqRR1fcao4f4r9KROqEr/n/G84h/d4icm+I4LS2P7C/S7f2PMEr3A0SUYVYqetI4v4gBe4gLdjzahC9Nu3s4xPeIjLeD22jMrlBzZTJ9GP2tbM3KDrx/AIU4Nu2sXU7eIPiF9pgm6LmBZDOAAAAABJRU5ErkJggg==",
      // 未完成
      incomplete : "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAF8AAAApCAYAAABTN/UpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAB30lEQVRoge3bv4rUUBTH8U/CgmChrbL+eYDRN7BamwVbrUXE9Q3EbmOlCIKosGZABRULQbQVZNxF9A1GfAAtLASZyspYJKyiM+ugyVwl59sEDlw4fH+cm5uQZMqqEvwpmzhZrfmUvQYF1uddvNRc97TdVU+4iK1s6LiBj2r5zBlA3lVXPeEKnuKlsX1NrcCleRaH/L/nKh6rA9jf1ApzBBDy2+EaHmHT2HJTK/wmgJDfHtdxXz0BB5paYYcAQn673MA99QQcamqFGQGE/Pa5hVI9AYebWmFKACG/G25jA6OdJiDkd0fpewAHm1rhhwBCfreU6ikYTbsJL81YFLRH2VxHxlYMvNc8CWfNu514vdA953EOKwY+ENvOIilxR30KWia2nUWzoXY+MnYs5C+em9iNZ7Hnp2MSe35CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CQn5CcrzBZWSJe+kdOU7gCO5iV9p2+kVerfmMVXUQT7A3aUc9IodqzRecwjs8Z/sb86BDsuqn/+GyoXWcVYfxNkVTPWHyi3zIhs6of3c5jVeL7qonTJcP2dAqHuACXiyyqxb5iknqJmYwWz5kQ0fxkO1PnP83cv/wAeIbmw9mthvHePUAAAAASUVORK5CYII=",
      // 已完成网关
      yiwancheng:"image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAApCAYAAACoYAD2AAAACXBIWXMAAAsTAAALEwEAmpwYAAADY0lEQVRYhc3ZW4xdYxjG8d+sGUnRyjiES5TRoprtkDgMISoxGIIQh1ASjRiUROpC4kaDNm4qbkgardI43EjazLQRkZpOJIQ0Qseh6LgS2pQyhDiNi3dvs2fPWnv2Yc2sPsnOTtb69vr+6zu97/vsDuN7tKgTsQwXYTEWYj4W4CccxDcYxU68ix9b6aijSch5uKv8OQXvlAE+w1f4Fb/gGHSjByVchl7swHpsw0TekF0YwGP4CC/gLfyT0nYCHSnX5+MmPISk/KzteUGWsBEH8Ag+maF9FuRkn1yDdfgY92N/vQcmM3R4D97Gs7iiAcBGNIFBLMFe7BLrOlP1RvIJ3I5+fNkkRL2RrFU/NmAFtqY16Mr44ZO4ChfjhyY6bEWDYvq3iDU+VNsgbbpX4Bb0mX3Aij7EDXgJZ9ferIUsYY2YgrqLeRb0AVbiDRxVfaMa8jC8jFWaW4N56nVx7q6tvlgN+aAYvU1zCJWmR8XUn1O5UNndh4sQ1qf9Y6bZ3Z2mB3AlrmNyJJeLSJLHOZiHNuBcnMEk5N14viCgNP2OVwSXRGQzC0VkOZS0GTcTkMtENvN3kUQp2i2CzamJiJsjxfJkagS9iUhYdxcMk6VRLE5wMsYKhsnSGE5KRAg6WCxLpn7Ggi4cifGcH95wadCAhhNRk3SLKJHHR47Puhbjiajgjs3xzfPUcTiQiJh9WsEwWerB3kSUo0sKhsnSUowmGMblBcOkqVPU6iOJcBYuFM7DoaRecU7uq2ycnbixUKTpuhOvMZmqrcfD2k9W89IJYtA2Mwk5JNZAf0FQtVol6p3vmWoOVKyPpfijjQ7aLR8WieynhO+YWogN4VOsbqODdtWJF/G0MiDT6+77cBuunjuuKVqL3/Bc9cVayP0iZd+E8+eG638N4HrcgX+rb6TZLO8LN22LOD/nQvficeE/TXNOsgyrrfir/L1S7LTZUBeeEubqpfg6rVE9f3K7KNBXi8XcnS+fRcKeLuECGYDMbKLuwnn4E18IZ2Fem3DH4xm8hzdlTHEzkERSPCB2fJ9I7dbgzCbAOsV0bhQve4Q4j9ep2SRpavbfB2W45bhVvOSwSPfGRK20TXg4R4s89Sxcgm9FmHtVOZI0qlYgq9Uj6vbThRPSLUZ7sAy8B5+LBGZfq538B77vuOY91L7gAAAAAElFTkSuQmCC",
      // 未完成网关
      weiwangcheng: "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAApCAYAAACoYAD2AAAACXBIWXMAAAsTAAALEwEAmpwYAAADaElEQVRYhc3ZW4hVVRzH8c/smaCLxmRRj5U1aaVxrKDLFEVCTTlGRVFGWpBIU1YQ9hD0UlRKD0m9FIiZJV1eAm1GCYnSSSgK0XQqujg+SSmV3Si6TQ//M86ZM3ufmXPOntn+4HBg73X2/7vXWv/1v5wWHw5pUKdjPi7HbMzENEzHTziMbzGA7fgAPzZiqKVOyGNxd/lzFt4rA3yOr/EbfsEMtKMDJVyNTryPNdiMCRueKGQbevAYPsVLeBf/powdQkvK9Wm4FQ8hKT9rS16QJawTS/UIdo8zPgtyxCbdeA67cD8O1XpgMo7Be7EVz4v9Nx7gRDSEdzAX+7BT7OtM1ZrJJ3AnFuLLOiFqzWS1uvEylmJT2oC2jB8+hetxBb6vw2Aj6sUCbBR7vK96QNpyL8Xt6DL5gMP6BDfjFcyrvlkNWcJKscQ1N/Mk6GPh+W/hxMoblZDH4FWsUN8ezFNviHN3VeXFSsjlYvbWTyFUmh4VS3/h8IVh7z5OhLAufNakkXq9O03LcS1uZGQml4hI0ixgXlqLi3AeI5D34MWCgNL0B14TXBKRzcwUkeVo0gbcRkDOF9nMP0USpWivCDZnJyJu9hfLk6l+dCYiYd1bMEyWBjA7wZkYLBgmS4M4IxEh6HCxLJn6GdPbcAJ+zfnhDRdOaUpETdIuokQeHzk+ayF6E1EWnJznm+eoU/BDImL2OQXDZKkD+xJRjs4pGCZLF2AgwTZcUzBMmlpFrd6fiM7CZaLzcDSpU5yTB4cdZztuKRRprBaLTP1IqrYGD2s+Wc1Lp4lJ28AIZJ/YA90FQVVrBd7Ed4xuDizAauFRfzZhoNnyYZbIfko4wOhCrA978GQTBppVqygdnlEGZGzdfR8W4Yap4xqlVfgdL1RerIY8JFL29bhkariOqAc34S78V3kjrc3ykeimbRTn51RoGR4X/acxnZOshtUm/F3+flB42mSoDU+L5upV+CZtUK3+5BZcJxxprUjn8tQs0Z4u4VIZgIzfRN2Ji/GX6A89IPrmzehUPIsdeFvGEtcDSSTFPcLju0RqtxLn1wHWKpZznXjZ48V5vFqVk6Sp3n8flOGW4A7xkttEujcoaqXNoodzkshT5+JK7Bdh7nXlSDJRNQJZqQ5Rt58rOiHtYrZ7y8Bf4QuRwBxs1Mj/2nS3isONIQwAAAAASUVORK5CYII=",
      detailColumns: [
        {
          prop: "alarmSite",
          label: "告警位置",
          align: "center",
        },
        {
          prop: "nodeName",
          label: "告警网元名称",
          align: "center",
        },
        {
          prop: "alarmCategory",
          label: "告警级别",
          align: "center",
        },
        {
          prop: "possibleCauses",
          label: "告警可能原因",
          align: "center",
        },
      ],
      points1:[
          {
            name: "开始", // 
            draggable: true, 
            category: 0, // 已完成：0 当前：1 未完成：2 (控制图例)
            x: -700,
            y: 200,
            target: ["派单"],
            symbol: 'completed', // 已完成：completed 当前：current 未完成：incomplete（控制图标）
          },
          {
            name: "派单",
            category: 0,
            draggable: true,
            x: -500,
            y: 200,
            target: ["通知客户经理","接单"],
            symbol: 'completed',
          },
          {
            name: "通知客户经理",
            category: 1,
            draggable: true,
            x: -500,
            y: 400,
            symbol: 'current',
          },
          {
            name: "接单",
            category: 1,
            draggable: true,
            x: -300,
            y: 200,
            target: ["阶段反馈","返单"],
            symbol: 'current',
          },
          {
            name: "阶段反馈",
            category: 2,
            draggable: true,
            x: -300,
            y: 400,
            target: ["节点"],
            symbol: 'incomplete',
          },
          {
            name: '节点',
            category: 2,
            draggable: true,
            x: -100,
            y: 400,
            target: ["返单"],
            symbol:  'incomplete',
          },
          {
            name: "返单",
            category: 2,
            draggable: true,
            x: -100,
            y: 200,
            target: ["归档"],
            symbol: 2,
          },
          {
            name: "归档",
            category: 2,
            draggable: true,
            x: 100,
            y: 200,
            target: ["结束"],
            symbol: 'incomplete',
          },
          {
            name: "结束",
            category: 2,
            draggable: true,
            x: 300,
            y: 200,
            symbol: 'incomplete',
          },
        ],
      detailData: {
        "mainFormId": "",
        "workOrderNumberJT": "",
        "orderIdentify": "",
        "theme": "",
        "occurrenceTime": "",
        "alarmTitle": "",
        "locationInfo": "",
        "alarmRegion": "",
        "vendor": "",
        "alarmId": "",
        "opLinkVoList": []
      },
      currentProcess: [{
        "mainFormId": "",
            "workOrderNumber": "",
            "orderIdentify": "",
            "status": "",
            "userName": null,
            "disassignObjectName": null,
            "fdbkDetail": null,
            "fdbkTime": null,
            "alarmClearTime": null,
            "stationBackReason": null,
            "routerChangeJobCode": null,
            "hardwareFlag": null,
            "businessName": null,
            "emergencyLevel": null,
            "recoveryPerson": null,
            "recoveryPhone": null,
            "faultNoticeTime": null,
            "processResultDesc": null,
            "alarmId": null,
            "cancelTime": null
      }]
    };
  },
  created(){
    this.getFlowChartData();
  },
  mounted() {
  },
  methods: {
    getFlowChartData(){
      this.detailData = {
        "mainFormId": "",
        "workOrderNumberJT": "",
        "orderIdentify": "",
        "theme": "",
        "occurrenceTime": "",
        "alarmTitle": "",
        "locationInfo": "",
        "alarmRegion": "",
        "vendor": "",
        "alarmId": "",
        "opLinkVoList": []
      }
        this.$api.slaApi.alarmOrderPage(this.mainFormId)
        .then((response) => {
					let res =  {...response};
					if (res) {
						this.detailData = res;
            console.log(this.detailData);
            this.detailData.opLinkVoList = res.opLinkVoList
            // console.log(this.detailData.opLinkVoList);
            if (this.detailData.opLinkVoList) {
              if(this.detailData.opLinkVoList[this.detailData.opLinkVoList.length-1].status == '返单'){

              if (JSON.stringify(this.detailData.opLinkVoList).includes(JSON.stringify('通知客户经理')) === false) {
                var arrNew=[];
                
                this.points1.forEach((value,index,array)=>{
                  if(value.name !=='通知客户经理'){
                    arrNew.push(value);
                  }
                })
                this.points1 = arrNew;
              }

              this.points1.map((i,idx)=>{
                i.category = 0
                i.symbol = 'completed'
              })
              
              } else if(this.detailData.opLinkVoList[this.detailData.opLinkVoList.length-1].status !== '返单'){
              if (JSON.stringify(this.detailData.opLinkVoList).includes(JSON.stringify('通知客户经理')) === false) {
                var arrNew=[];
                
                this.points1.forEach((value,index,array)=>{
                  if(value.name !=='通知客户经理'){
                    arrNew.push(value);
                  }
                })
                this.points1 = arrNew;
              }
              this.detailData.opLinkVoList.map((i,idx)=>{
                this.points1[this.detailData.opLinkVoList.length].category = 1;
                  this.points1[this.detailData.opLinkVoList.length].symbol = 'current';
                  this.points1.slice(0,this.detailData.opLinkVoList.length).map((i,idx)=>{
                    i.category = 0
                    i.symbol = 'completed'
                  });
                  this.points1.slice(this.detailData.opLinkVoList.length+1,this.points1.length).map((i,idx)=>{
                    i.category = 2
                    i.symbol = 'incomplete'
                  });

                  this.detailData.opLinkVoList.filter((i,idx)=>{

                  })
              })
              }
              this.currentProcess = [this.detailData.opLinkVoList[this.detailData.opLinkVoList.length-1]];
              setTimeout(()=>{
                this.initFlowChart();
              },100)
            }
            
					} else {
						setTimeout(() => {
							this.detailData = [];
						}, 3000);
					}
        })
        .catch((err) => {
          console.error(err);
        });
    },
	  initFlowChart(){
		  let echart = echarts.init(document.getElementById('flowChart'))
		  // 指定图表的配置项和数据
      const legend = {
        show: true,
        top: "0%",
        right: "2%",
        textStyle: { color: "#73a2ec" },
        itemWidth: 20,
        itemHeight: 20,
        orient: 'horizontal',
        data: [
          { name: "已完成", "color": "#00f3da" },
          { name: "当前", "color": "#ff505a" },
          { name: "未完成", "color": "#00c4ff" },
        ],
      };
      const series = {
        type: "graph",
        layout: "none", // 图的布局
        symbol: this.completed, // 默认是「未完成」
        symbolSize: [100,40],
        categories: [
          { name: "已完成",itemStyle:{color:"#00f3da"} },
          { name: "当前" ,itemStyle:{color:"#ff505a"}},
          { name: "未完成" ,itemStyle:{color:"#00c4ff"}},
        ],
        grid: {
          top: '10%',
          buttom: '10%'
        },
        label: {
            normal:{
              textStyle: { fontSize: 12, color:  '#00c4ff' },
              show: true,
            }
          },
        edgeSymbol: "circle",
        edgeSymbolSize: 0,
        data: this.points(this.points1),
        links: this.links(this.points1),
        lineStyle: {
          opacity: 0.9,
          width: 2,
          // curveness: 0,
          // color: "#fff",
        },
      };
      let option = {legend,series};

		  echart.setOption(option);
      window.addEventListener("resize", function () {
					echart.resize();
				});
      echart.on("click", (params) => {
        this.currentProcess = this.detailData.opLinkVoList.filter((i,idx)=>{
          return i.status == params.name
        })
        // console.log(this.currentProcess);


      })
	  },
      points(data){
        var res = [];
        for (var i = 0; i < data.length; i++) {
          var item = data[i];
          if (item.symbol) {
              res.push({
                name: item.name,
                value: item.value,
                category: item.category,
                draggable: item.draggable,
                x: item.x,
                y: item.y,
                target: item.target,
                symbol: item.symbol == 'completed' && (item.name == '节点') ? this.yiwancheng : 
                item.symbol == 'completed' && (item.name == '网关1' || item.name == '网关2') ? this.weiwangcheng :
                item.symbol == 'completed' ? this.completed : item.symbol == 'current' ? this.current :  this.incomplete  ,
                symbolOffset: ["15%", 0],
                symbolSize: item.name == '节点' ? [0,0] : [100,40],
                label: {
                  normal:{
                    textStyle: { 
                      fontSize: 12, 
                      color: item.symbol == 'completed' ? '#00f3da' : item.symbol == 'current' ? '#ff505a' : '#00c4ff' },
                    show: item.name == '网关1' || item.name == '网关2' ? false : true,
                  }
                },
              });
          }





        }
        return res;
      },
      links(data) {
        var res = [];
        for (var i = 0; i < data.length; i++) {
          var item = data[i];
          var color = '';
          if (item.symbol == 'completed') {
            // color = 'rgba(0,243,218,.5)'
               color = {
                    type: 'linear',     // 线性渐变
                    x: 1,               // x : 从左向右 1 ——> 0
                    y: 1,                // y：从上向下 1 ——> 0
                    x2: 0,               // x2：从右向左 1 ——> 0
                    y2: 0,               // y2：从下向上 1 ——> 0
                    colorStops: [
                        {offset: 0.2, color: 'rgba(0,243,218,0.8)'},      // offset：坐标为0处的颜色
                        {offset: 0.8, color: 'rgba(0,243,218,0)'}, 
                        {offset: 1, color: 'rgba(0,243,218,0)'}        // offset：坐标为1处的颜色
                    ]
                }
          } else if(item.symbol == 'current') {
            color = {
                    type: 'linear',     // 线性渐变
                    x: 1,               // x : 从左向右 1 ——> 0
                    y: 1,                // y：从上向下 1 ——> 0
                    x2: 0,               // x2：从右向左 1 ——> 0
                    y2: 0,               // y2：从下向上 1 ——> 0
                    colorStops: [
                        {offset: 0.2, color: 'rgba(255,80,90,0.8)'},      // offset：坐标为0处的颜色
                        {offset: 0.8, color: 'rgba(255,80,90,0)'}, 
                        {offset: 1, color: 'rgba(255,80,90,0)'}        // offset：坐标为1处的颜色
                    ]
                }
          } else {
            color = {
                    type: 'linear',     // 线性渐变
                    x: 1,               // x : 从左向右 1 ——> 0
                    y: 1,                // y：从上向下 1 ——> 0
                    x2: 0,               // x2：从右向左 1 ——> 0
                    y2: 0,               // y2：从下向上 1 ——> 0
                    colorStops: [
                        {offset: 0.2, color: 'rgba(0,196,255,0.8)'},      // offset：坐标为0处的颜色
                        {offset: 0.8, color: 'rgba(0,196,255,0)'}, 
                        {offset: 1, color: 'rgba(0,196,255,0)'}        // offset：坐标为1处的颜色
                    ]
                }
          }
          if (item.target) {
            console.debug("item.target", item.target);
            for (var j = 0; j < item.target.length; j++) {
              res.push({
                source: item.name,
                target: item.target[j],
                value: 5,
                ignoreForceLayout:true,
                lineStyle: {
                  normal: {
                    color: color,
                  },
                },
              });
            }
          }
        }
        return res;
      },
    manualDispatch(){
      console.log(this.currentProcess);
      if (this.detailData.alarmId) {
        this.$api.slaApi.manualDispatch(this.detailData.alarmId)
        .then((response) => {
					if (response == '派单成功') {
            // console.log(res);
            this.$message({
              message: response,
              type: 'success'
            });
          } else{
            this.$message.error('派单失败');
          }
        })
      } else {
        this.$message.error('派单失败');
      }
        
    },

  },
};
</script>

<style scoped lang="less">
	.detail-info {
		background: transparent;
    width: 100%;
    max-height: 600px;
    // overflow-y: scroll;
    overflow: hidden;
		padding: 0 0 0 16px;
		::v-deep {
			.el-loading-spinner {
				top: 50%;
				.circular {
					height: 20px;
					width: 42px;
				}
				.el-loading-text,
				.path {
					color: rgba(37, 190, 247, 0.75);
					stroke: rgba(37, 190, 247, 0.75);
				}
			}
		}
		.cards {
      width: 100%;
      height: 45%;
			.box-card {
				border: none;
				color: #E1E9F9;
				background: transparent;
				border-bottom: none;
				border-radius: 0;
				position: relative;
				::v-deep {
					.el-card__header {
						border: none;
					}
					.el-card__body {
						padding: 16px 16px 0;
					}
				}
				.card-title {
					color: #25BEF7;
					font-size: 16px;
					&::before {
						content: '';
						display: block;
						width: 2px;
						height: 14px;
						margin-right: 10px;
						float: left;
						background: #25BEF7;
						margin-top: 3px;
					}
				}
				.card-items {
					.card-item {
						height: 60px;
						display: inline-flex;
						align-items: flex-start;
						.label {
							width: 110px;
							text-align: right;
							display: block;
							line-height: 24px;
						}
						.val {    
							margin-left: 10px;
							height: 50px;
							line-height: 24px;
							width: 210px;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							overflow: hidden;
							text-overflow: ellipsis;
							cursor: pointer;
							&:hover::after {
								content: attr(aria-label);
								position: absolute;
								font-size: 14px;
								line-height: 20px;
								text-align: justify;
								padding: 5px 10px;
								box-shadow: 2px 2px 5px rgba(0,0,0,0.15), -2px -2px 5px rgba(0,0,0,0.15);
								background: #040F2D;
								z-index: 10;
                border: 1px solid #25BEF7;
								border-radius: 3px;
								max-width: 450px;
							}
						}
					}
					&:nth-of-type(3n+1) {
						.card-item .val:hover::after {
							left: 15%;
						}
					}
					&:nth-of-type(3n+2) {
						.card-item .val:hover::after {
							left: 45%;
						}
					}
					&:nth-of-type(3n+3) {
						.card-item .val:hover::after {
							left: 78%;
						}
					}
					&:nth-of-type(-n+3) {
						.card-item .val:hover::after {
							top: -20%;
						}
					}
					&:nth-of-type(n+4):nth-of-type(-n+6) {
						.card-item .val:hover::after {
							top: 50%;
						}
					}
					&:nth-of-type(n+7):nth-of-type(-n+9) {
						.card-item .val:hover::after {
							bottom: 10%;
						}
					}
				}
			}
		}
		
		.list-title {
			color: #fff;
			font-size: 18px;
			padding: 30px 20px 30px;
			// &::before {
			// 	content: '';
			// 	display: block;
			// 	width: 2px;
			// 	height: 16px;
			// 	margin-right: 10px;
			// 	float: left;
			// 	background: #25BEF7;
			// 	margin-top: 3px;
			// }
		}
    .list-title-flow {
      color: #fff;
			font-size: 18px;
			padding: 30px 20px 0px;
    }
		.w800 {
			width: 800px !important;
		}
	}
	#flowChart {
			width: 57vw;
			height: 350px;;
		}
    .gdxq {
      padding: 0px 20px 15px;
      margin-bottom: 5.5%;
    }
    .gdxqtitle {
        font-size: 1.8vh;
        color: #43b0cd;
      }
    .gdxqbg{
      background: url('../../../assets/images/gdxqbg.png');
      background-size: 100% 100%;
      text-align: center;
      height: 45px;
      line-height: 45px;
      font-size: 1.6vh;
      color: #dcbb00;
    }
    .dqgj {
      text-align: center;
      height: 35px;
      line-height: 35px;
      font-size: 1.6vh;
      color: #dcbb00;
    }
    .dqgjtitle {
      text-align: center;
      color: #43b0cd;
      height: 25px;
      line-height: 25px;
      font-size: 18px;
    }
    .dqgjimg {
      background: url('../../../assets/images/dqlc.png');
      background-size: 100% 100%;
      width: 65%;
      height: 35px;
      margin: 0 auto;
      margin-top: -14px;
    }
    .dqhj {
      
      margin-bottom: 40px;
    }
    .t-ellipsis {
    	overflow: hidden;
    	text-overflow: ellipsis;
    	white-space: nowrap;
    }
    .manualDispatch{
      width: 20px;
      height: 20px;
      background: url('../../../assets/images/pdBtn.png');
      background-size: 100% 100%;
      position: absolute;
      // top: 57%;
      right: 250px;
      z-index: 999;
      margin-top: 5px;
    }
    /deep/.el-button--default {
        color: #00c4ff;
        background: transparent;
        border-color: #00c4ff;
    }
    /deep/ .el-button--default:hover:hover, 
    /deep/.el-button--default:active:hover, 
    /deep/.el-button--default.is-plain:hover {
        opacity: 1;
        color: rgba(255,255,255,0.75);
        background: rgba(3,48,91,0.75);
        border-color: rgba(255,255,255,0.75);
    }
</style>
