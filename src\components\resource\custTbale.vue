<template>
  <div style="height: 70%;">
      <div class="nc-query">
          <el-form :inline="true" :model="form" label-width="100px" size="small">
              <el-form-item
                class="formItem"
                label="客户名称"
            >
              <el-input v-model="form.custName" placeholder="客户名称" clearable></el-input>
            </el-form-item>
            <el-form-item
                class="formItem"
                label="客户等级"
            >
              <el-select v-model="form.grade" filterable placeholder="客户等级" clearable>
                    <el-option class="infinite-list-item" label="一级" value="一级"></el-option>
                    <el-option class="infinite-list-item" label="二级" value="二级"></el-option>
                    <el-option class="infinite-list-item" label="三级" value="三级"></el-option>
                    <el-option class="infinite-list-item" label="四级" value="四级"></el-option>
                    <el-option class="infinite-list-item" label="五级" value="五级"></el-option>
                    <el-option class="infinite-list-item" label="六级" value="六级"></el-option>
                  </el-select>
            </el-form-item>
            <el-form-item
                class="formItem"
                label="所属管理区域"
            >
              <el-input v-model="form.regionId" placeholder="所属管理区域" clearable></el-input>
            </el-form-item>
            <el-form-item
                class="formItem"
                label="区域经理"
            >
              <el-input v-model="form.managerMan" placeholder="区域经理" clearable></el-input>
            </el-form-item>
            <el-form-item
                class="formItem"
                label="经理电话"
            >
              <el-input v-model="form.managerTele" placeholder="经理电话" clearable></el-input>
            </el-form-item>
            <el-form-item style="float:right;width:5%">
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
            <el-form-item style="float:right;width:5%">
              <el-button type="primary"  @click="selectAllUserInfo">查询</el-button>
              <!-- @click="getList" -->
            </el-form-item>
          </el-form>
      </div>
      <div style="margin:0 0 15px 0">
            <!-- <el-button size="mini" @click="handleTemplateExport()">导出当前页</el-button> -->
            <el-button size="mini" @click="handleTemplateExport">导出</el-button>
            <el-button size="mini" :disabled= 'multipleSelection.length !== 1' @click="custInfor">确认</el-button>
          </div>
      <el-table 
      v-loading="loading" 
        border 
        :data="tableBody" 
        :header-cell-style="{
				color: '#9ED8FF',
			  	'background-color': 'rgba(15, 52, 124, 1)',
			  	'text-align': 'center',
			  	'font-size': '14px',
			  	'border-right': '1px rgba(15, 0, 137, .8) solid'
			  }"
        height="380"
        style="border-radius: 4px 4px 0 0;
			  border:none;margin-top: 5px;"
              @selection-change="handleSelectionChange">
              <el-table-column
        class-name="tabSel"
          type="selection"
          width="55"
          align="center">
        </el-table-column>
          <el-table-column 
          align='center'
          v-for="(i, idx) in tableColumns" 
          :key="idx" 
          :label="i.displayName" 
          :prop="i.columnName" 
          show-overflow-tooltip>
          </el-table-column>
        </el-table>
        <el-pagination
            class="page-list"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageNumber"
            :page-sizes="[1,10, 20, 30, 40, 50]"
            :page-size="pageSize"
            background
            layout="total, prev, pager, next, sizes"
            :total.sync="total"
            style="float: right;"
        >
        </el-pagination>
  </div>
</template>

<script>
export default {
  props: {
      custDialog: {
        type: Boolean, //要求传递的类型,大写开头
        required: true, //强制传递,否则报错
      },
    },
    watch:{
      custDialog(val){
        if (!val) {
          this.resetQuery()
        }
      }
    },
data(){
    return {
      loading: false,
        visible: false,
        detailModal: false,
        form: {},
        formData: {},
        total: 10,
        pageSize: 10,
        pageNumber: 1,
        allCityData:  [],
        allCountiesData: [],
        allCustUserRoles: [],
        roleList:[],
        custIdList: [],
        tableColumns: [{
            displayName: '客户ID',
            columnName: 'custId'
        },{
            displayName: '客户名称',
            columnName: 'custName'
        },{
          displayName: '所属管理区域',
          columnName: 'regionId'
        },{
            displayName: '状态',
            columnName: 'status'
        },{
            displayName: '客户经理',
            columnName: 'managerMan'
        },{
          displayName: '客户经理电话',
          columnName: 'managerTele'
        },{
            displayName: '客户等级',
            columnName: 'grade'
        },{
            displayName: '服务等级',
            columnName: 'svrLevel'
        },],
        tableBody: [],
        userInformation: [],
        customerAdded: [],
        noCustomerAdded: [],
        customerName: '',
        deleteCustomer: [],
        multipleSelection: []
    }
},
created() {
    this.getAllCity()
    this.getAllCounties()
    this.getcustUserRoles()
    this.selectAllUserInfo()
},
methods: {
    // 表格复选框
    handleSelectionChange(val) {
        this.multipleSelection = val;
        // console.log(this.multipleSelection);
      },
      custInfor(){
        this.$emit('custFrom',this.multipleSelection[0])
      },
      getCust(){
          let options = {
            current: this.pageNumber,
            limit: this.pageSize,
            isQueryCount: true,
          }
      },
  getcustUserRoles(){
    this.$api.slaApi.getcustUserRoles()
      .then(res=>{
          // console.log(res);
          this.allCustUserRoles = res.data
      })  
  },
    getAllCity(){
        // {token:localStorage.getItem("nmgjpwToken")}
      this.$api.slaApi.getAllCity()
      .then(res=>{
          // console.log(res);
          this.allCityData = res.data
      })  
    },
    getAllCounties(){
        this.$api.slaApi.getAllCounties()
      .then(res=>{
          // console.log(res);
          this.allCountiesData = res.data
      }) 
    },
    selectAllUserInfo(){
        let scope = this;
        let queryParam = "";
      let options = {
        current: this.pageNumber,
        limit: this.pageSize,
        isQueryCount: true,
      }
        let arr = []
      Object.keys(this.form).forEach(key => {
       arr.push({
         type:'string',
         talcolumn:key,
         value:scope.form[key]
       })
      })
      let obj = arr.map((i,idx)=>{
        if (i.value) {
          if (i.type == 'enum') {
            return {[i.talcolumn]:{$eq:i.value}}
          } else {
            return {[i.talcolumn]:{$like:i.value}}
          }
          
        }
        
      })
      queryParam = this.transformData(obj).where
      if(queryParam){
        options.where=queryParam;
      }
      let params = {
        options: JSON.stringify(options),
      }
        this.$api.slaApi.getCust(params)
      .then(res=>{
        //   console.log(res);
          this.tableBody = res.data;
          this.total = res.pagination.total;
      })
    },

transformData(data) { 
  let transformedData = { where: {} }; 
  data.forEach(item => { 
    for (let key in item) { 
      let value = item[key]; // 假设统一使用$like作为条件 
        transformedData.where[key] = value; 
      } 
      }); 
      return transformedData; 
      } ,
    // 深拷贝
    extendCopy(p) {
      var c = {};
      for (var i in p) {
        c[i] = p[i];
      }
      c.uber = p;
      return c;
    },
    // 重置表单
    resetQuery(){
      this.form = {
            userName: '',
            city: '',
            counties: ''
        };
        this.pageSize = 10;
        this.pageNumber = 1;
        this.selectAllUserInfo()
    },
    addUser(row){
      this.addCustomer()
      if (row.roleId) {
        this.formData = this.extendCopy(row);
      } else {
        this.formData = {};
      }
      this.visible = true;
    },
    subForm(formData) {
      if (this.formData.roleId) {
        this.$refs.formData.validate(valid => {
          if(valid) {
            this.subEditForm(formData);
          }
        });
      } else {
        this.$refs.formData.validate(valid => {
          if(valid) {
            this.subAddForm(formData);
          }
        });

      }
    },
    subAddForm(){
      this.$refs['formData'].validate((valid) => {
          if (valid) {
            console.log(this.formData);
             this.$api.slaApi.addRole(this.formData)
             .then(res=>{
               console.log(res);
               if (res.resultCode == 200) {
                 this.$message({
                   message: res.data,
                   type: 'success'
                 });
                 this.visible = false;
               } else {
                 this.$message.error('新增失败');
               }
               this.selectAllUserInfo()
             })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    subEditForm(){
      this.$refs['formData'].validate((valid) => {
          if (valid) {
             this.$api.slaApi.updateRole(this.formData)
             .then(res=>{
               if (res.resultCode == 200) {
                 this.$message({
                   message: res.data,
                   type: 'success'
                 });
                 this.visible = false;
               } else {
                 this.$message.error('修改失败');
               }
               this.selectAllUserInfo()
             })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    // 删除角色
    deleteRole(row){
        // deleteRole
        this.$api.slaApi.deleteRole({roleId: row.roleId} )
             .then(res=>{
                 if (res.resultCode == 200) {
                 this.$message({
                   message: res.data,
                   type: 'success'
                 });
                 this.visible = false;
               } else {
                 this.$message.error('删除失败');
               }
               this.selectAllUserInfo()
             })
    },
    resetForm(form) {
    //   this.$refs.formData.resetFields();
    this.formData.roleName = ''
    },
    closeModel() {
    //   this.dialogFormData = {};
      this.visible = false;
    },
    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.selectAllUserInfo();
    },
    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.selectAllUserInfo();
    },
    bindCustomer(row){
        this.detailModal = true;
        this.userInformation = row;
        this.customerAdded = row.custIdList;
        this.deleteCustomer = [];
        this.addCustomer()
    },
    addCustomer(){
      this.noCustomerAdded = [];
        let params = {
          options: JSON.stringify({"current":1,
          "limit":10,
          "isQueryCount":true,
          "where":{
              "custName":{
                  "$like":this.customerName
            }}})}
        this.$api.slaApi.query('pubCust',JSON.stringify(params))
        .then((result) => {
            // console.log(result.data);
            result.data.map(item => {
              this.noCustomerAdded.push({
                custId: item.custId, 
                custName: item.custName 
              })
            })
            // this.selectAllRoleInfo()
            // this.noCustomerAdded = result.data;
        })
    },
    updateCustAccount(){
      let newArr = [...this.customerAdded,...this.userInformation]
      var obj = {};
      newArr = newArr.reduce((current, next) => {
        obj[next.custId] ? "" : obj[next.custId] = true && current.push(next);
        return current;
      }, []);
      this.customerAdded = newArr;
    },
    // handleSelectionChange(val) {
    //   this.deleteCustomer.push(val)
    // },
    // handleSelectionChange2(val) {
    //   // console.log(val);
    //   let userInformation = []
    //   val.map(item => {
    //     userInformation.push({
    //       "userId": this.userInformation.id, 
    //       "custId": item.custId, 
    //       "custName": item.custName
    //     })
    //   })
    //   this.userInformation = userInformation
    // },
    // 删除客户
    deleteCustomers(){
      // console.log(this.customerAdded,this.deleteCustomer);
      this.customerAdded = this.customerAdded.filter(item => {
          return this.deleteCustomer[0].every(item2 => {
              return item.custId != item2.custId;
          })
      });
      // console.log(this.customerAdded);
    },
    // 保存客户信息
    saveCustInformation(){
      // console.log(this.customerAdded);
      let isNull = [{
        "userId": this.userInformation.id, 
          "custId": '', 
          "custName": ''
      }]
      this.$api.slaApi.updateCustAccount({accountCustList: this.customerAdded.length > 0 ? this.customerAdded : isNull})
      .then(res=>{
        console.log(res);
        if (res.resultCode == 200) {
          this.$message({
            message: res.data,
            type: 'success'
          });
          this.detailModal = false;
        } else {
          this.$message.error('绑定失败');
        }
        this.selectAllUserInfo()
      })
    },
    handleTemplateExport(){
      this.loading = true;
      let scope = this;
        let queryParam = "";
      let options = {
        // current: this.pageNumber,
        // limit: this.pageSize,
        isQueryCount: true,
      }
        let arr = []
      Object.keys(this.form).forEach(key => {
       arr.push({
         type:'string',
         talcolumn:key,
         value:scope.form[key]
       })
      })
      let obj = arr.map((i,idx)=>{
        if (i.value) {
          if (i.type == 'enum') {
            return {[i.talcolumn]:{$eq:i.value}}
          } else {
            return {[i.talcolumn]:{$like:i.value}}
          }
          
        }
        
      })
      queryParam = this.transformData(obj).where
      if(queryParam){
        options.where=queryParam;
      }
      let params = {
        options: JSON.stringify(options),
        showColums: JSON.stringify(this.tableColumns),
        type: "xlsx",
        isPage:"0"
      }
      this.$api.slaApi.exportCust(params)
          .then(res =>{
            // 文件下载
            const blob = new Blob([res], {
              // type: 'application/vnd.ms-excel' // 定义格式
              type:'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
            });
            let link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.setAttribute('download', 'oss3.0客户数据.xlsx');
            link.click();
            link = null;

            this.$message({
              message: '下载成功',
              type: 'success',
              duration: 2000,
              showClose: true
            });
            this.dialogExport = false;
            this.loading = false;
          })
          .catch(mgs =>{
            this.$message({
              message: '下载失败',
              type: 'error',
              duration: 2000,
              showClose: true
            });
            this.dialogExport = false;
            this.loading = false;
          });
    }
}

}
</script>

<style lang="less" scoped>
.nc-query {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
//   height: 10vh;
}
/deep/ .el-checkbox__inner {
    background-color: transparent;
    border: 1px solid #02dbff;
}
::v-deep {
  .el-table__fixed-right-patch {
      background-color: rgba(23, 70, 137, 1);
    }
    .el-table--border th.el-table__cell, .el-table__fixed-right-patch {
      border: none;
    }
    .el-table__fixed-right::before, .el-table__fixed::before {
      background-color: transparent;
    }
		.el-dialog {
			// height: 30vh;
            // width: 60%;
			background: url("../../assets/images/dialogbg.png") center center
              no-repeat;
            background-size: 100% 100%;
			margin-top: 10vh !important;
		}
		.el-dialog__header {
			justify-content: left;
			background: none;
		}
		.el-dialog__title {
			justify-content: left;
            line-height: 20px;
            font-weight: 800;
            height: 20px;
            display: flex;
            color: rgba(255, 255, 255, 1);
            font-size: 22px;
		}
		.el-dialog__title::before ,
		.el-dialog__title::after{
            content:none;
        }
		.el-dialog__headerbtn {
			    width: 30px;
            height: 30px;
            float: right;
            top: 10px;
            right: 10px;
			background: url("../../assets/images/x.png") center center
              no-repeat;
            background-size: 100% 100%;
		}
		.el-dialog__body {
			height: 85%;
			.detail-info {
				max-height: 800px;
				height: 100%;
			}
			.el-form {
				min-height: 100px;
				width: 95%;
				// margin: 30px auto 20px;
        margin-top: 30px;
				text-align: center;
        .el-form-item__label {
          color: #fff;
        }
        .el-input__inner {
					color: #02dbff;
					background: none;
					border: 1px solid rgba(37, 190, 247, 0.5);
					// height: 24px !important;
					line-height: 24px;
				}
        .el-switch__label, .el-checkbox {
          color: #fff;
        }
        .el-button--primary {
					color: #FFF;
          background-color: #409EFF;
          border-color: #409EFF;
					&:focus {
						background-color: #409EFF;
          border-color: #409EFF;
					}
					&:hover {
						background-color: #409EFF;
          border-color: #409EFF;
					}
				}
        .el-select-dropdown__item:hover {
            color: #409EFF;
            background: #40a0ff27;
        }
        .el-button--default {
            color: #409EFF;
            background: transparent;
            border-color: #409EFF;
        }
			}
		}
	}
  .addCustomer {
    height: 5%;
    width: 6%;
    float: left;
    position: relative;
    top: 45%;
    left: 4%;
    background: url("../../assets/images/addCustomer.png") center center
      no-repeat;
    background-size: 100% 100%;
    transform: rotate(180deg);
  }
</style>