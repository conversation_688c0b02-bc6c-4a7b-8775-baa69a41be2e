import axios from "@/utils/api.request";
const BASEURL = '/otnNpv'
const transport = {
  // 传输电路
  // getTransCircuit(inParams) {
  //   return axios.request({
  //     url: `${BASEURL}/transferResources/getTransCircuit`,
  //     method: "post",
  //     data: inParams,
  //   });
  // },
  getTransCircuit(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getSpecialTransCircuit`,
      method: "post",
      data: inParams,
    });
  },
  // 导出传输电路
  exportTransCircuit(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/exportTransCircuit`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 新增传输电路
  insertTransCircuit(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/insertTransCircuit`,
      method: "post",
      data: inParams,
    });
  },
  // 修改传输电路
  updateTransCircuit(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/updateTransCircuit`,
      // 客户新增
      // /resource/pubCust/update
      method: "post",
      data: inParams,
    });
  },
  // 传输路由
  getTransRoute(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransRoute`,
      method: "post",
      data: inParams,
    });
  },
  // 导出传输路由
  exportTransRoute(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/exportTransRoute`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 新增传输路由
  insertTransRoute(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/insertTransRoute`,
      method: "post",
      data: inParams,
    });
  },
  // 导入传输路由
  importTransRoute(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/importTransRoute`,
      method: "post",
      data: inParams,
    });
  },
  
  circuitRouteImport(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/circuitRouteImport`,
      method: "post",
      data: inParams,
    });
  },
  // 修改传输路由
  updateTransRoute(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/updateTransRoute`,
      method: "post",
      data: inParams,
    });
  },
  // 传输路由下拉框
    getTransSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransSelectData`,
      method: "post",
      data: inParams,
    });
  },
  // 传输端口
  getTDwdPort(inParams) {
    return axios.request({
      url: `${BASEURL}/dataCommunicate/getTDwdPort`,
      method: "post",
      data: inParams,
    });
  },
  // 导出端口
  exportTDwdPort(inParams) {
    return axios.request({
      url: `${BASEURL}/dataCommunicate/exportTDwdPort`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 传输网元
  getTransNet(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransNet`,
      method: "post",
      data: inParams,
    });
  },
  // 导出传输网元
  exportTransNet(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/exportTransNet`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 板卡
  getBoardCard(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getBoardCard`,
      method: "post",
      data: inParams,
    });
  },
  // 导出板卡
  exportBoardCard(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/exportBoardCard`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 获取路由导入模板
  downloadModel(inParams) {
    return axios.request({
      url: `${BASEURL}/resources/downloadModel`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 获取路由导出记录
  getTransRouteImportRecord(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransRouteImportRecord`,
      method: "post",
      data: inParams,
    });
  },
  // 下拉框
  getEqpTransSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getEqpTransSelectData`,
      method: "post",
      data: inParams,
    });
  },
  // 端口下拉框
  getPortTransSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getPortTransSelectData`,
      method: "post",
      data: inParams,
    });
  },
  // 电路名称下拉
  getTransCircuitNameSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransCircuitNameSelectData`,
      method: "post",
      data: inParams,
    });
  },
  // 查询端口、设备、设备类型、端口下拉
  getTransCircuitSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransCircuitSelectData`,
      method: "post",
      data: inParams,
    });
  },
  // 查询端口、设备、设备类型下拉
  getTransCircuitSelectDataV2(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransCircuitSelectDataV2`,
      method: "post",
      data: inParams,
    });
  },
  
  updateTDwdCoreNet(inParams) {
    return axios.request({
      url: `${BASEURL}/dataCommunicate/updateTDwdCoreNet`,
      method: "post",
      data: inParams,
    });
  },
  // 拓扑管理菜单列表
  getSpecialCircuit(inParams) {
    return axios.request({
      url: `${BASEURL}/topology/getSpecialCircuit`,
      method: "post",
      data: inParams,
    });
  },
  // 
  
  updateAceDevice(inParams) {
    return axios.request({
      url: `${BASEURL}/dataCommunicate/updateAceDevice`,
      method: "post",
      data: inParams,
    });
  },
  // 获取网元类型下拉
  getRouteTypeSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getRouteTypeSelectData`,
      method: "post",
      data: inParams,
    });
  },
  
  getCircuitNameSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/dataCommunicate/getCircuitNameSelectData`,
      method: "post",
      data: inParams,
    });
  },
  updateAuditInfo(inParams) {
    return axios.request({
      url: `${BASEURL}/audit/updateAuditInfo`,
      method: "post",
      data: inParams,
    });
  },
  
  selectRoleInfo(inParams) {
    return axios.request({
      url: `${BASEURL}/audit/selectRoleInfo`,
      method: "post",
      data: inParams,
    });
  },
  
  getTopologyBaseStatistics(inParams) {
    return axios.request({
      url: `${BASEURL}/topology/getTopologyBaseStatistics`,
      method: "post",
      data: inParams,
    });
  },
  
  getTopologyAlarmStatistics(inParams) {
    return axios.request({
      url: `${BASEURL}/topology/getTopologyAlarmStatistics`,
      method: "post",
      data: inParams,
    });
  },
  getTransCircuitBusNameSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransCircuitBusNameSelectData`,
      method: "post",
      data: inParams,
    });
  },
  
  getTransCircuitGroupNumSelectData(inParams) {
    return axios.request({
      url: `${BASEURL}/transferResources/getTransCircuitGroupNumSelectData`,
      method: "post",
      data: inParams,
    });
  },
}
export default transport;