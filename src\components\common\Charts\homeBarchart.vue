<template>
  <div
    id="centerBarCharts"
    v-loading="uploading"
    element-loading-text="数据加载中"
    element-loading-background="rgba(0, 0, 0, 0)"
    style="min-height: 18vh"
  ></div>
</template>

<script>
import echarts from "echarts";
export default {
  name: "centerBar",
  components: {},
  data() {
    return {
      uploading: false,
      activeCity: ''
    };
  },
  watch: {
    act(n) {
      if (n) {
        this.centerBarCharts();
      }
    },
    mapCityName(n) {
      if (n) {
        this.activeCity = n;
        this.centerBarCharts();
      }
    },
    xData: {
      handler(n, o) {
        if(n && n.length>0 && this.yData && this.yData.length>0) {
          this.centerBarCharts();
        }
      },
      deep: true
    },
    yData: {
      handler(n, o) {
        if(n && n.length>0 && this.xData && this.xData.length>0) {
          this.centerBarCharts();
        }
      },
      deep: true
    },
  },
  props: {
    mapCityName: {
      type: String,
      default: () => "内蒙古",
    },
    xData: {
      type: Array,
      default: () => [
        "内蒙古",
        "呼伦贝尔市",
        "呼和浩特市",
        "包头市",
        "乌海市",
        "乌兰察布市",
        "通辽市",
        "赤峰市",
        "鄂尔多斯市",
        "巴彦淖尔市",
        "锡林郭勒盟",
        "兴安盟",
        "阿拉善盟",
      ],
    },
    yData: {
      type: Array,
      default: () => [0,0,0,0,0,0,0,0,0,0,0,0],
    },
    xUuids: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    setTimeout(() => {
      this.centerBarCharts();
      const resizeOb = new ResizeObserver((entries) => {
        for (const entry of entries) {
          echarts.getInstanceByDom(entry.target).resize();
        }
      });
      resizeOb.observe(document.getElementById("centerBarCharts"));
    }, 10);
  },
  methods: {
    centerBarCharts() {
      let mycenterbar = echarts.init(
        document.getElementById("centerBarCharts")
      );
      const COLOR = [
        ["#3ECEAE", "#72E9D7"],
        ["#F18C5F", "#F9C098"],
        ["#408AF8", "#74BEFC"],
      ];
      let maxValue = Math.max(...this.yData) > 0 && Math.max(...this.yData) < 5 ? 5 : Math.max(...this.yData) == 0 ? 5 : 100;
      let option = {
        tooltip: {
          formatter: function (params) {
            return params[0].name + "<br>线路数量 " + ": " + params[0].data;
          },
          textStyle: {
            color: "rgba(255, 255, 255, 0.8)",
          },
          extraCssText: "box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
          trigger: "axis",
          // box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75);
          backgroundColor: "rgba(0, 10, 49, 0.7)",
          axisPointer: {
            type: "none", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        grid: {
          //整张图的大小，距离left，right，bottom的距离
          left: "3%",
          top: "5%",
          right: "3%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category", //类目轴
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            color: "rgba(255, 255, 255, 0.9)",
          },
          data: this.xData,
        },
        yAxis: {
          type: "value", //y轴为数值
          min: 0,
          max: maxValue,
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(14, 28, 70, 0.8)",
              type: "solid",
            },
          },
          axisLabel: {
            fontSize: 12,
            color: "rgba(255, 255, 255, 0.9)",
          },
        },
        series: [
          {
            barWidth: "16", //俩柱之间的距离
            data: this.yData,
            type: "bar", //柱状图
            showBackground: true, //柱状背景
            backgroundStyle: {
              //背景颜色，加透明度
              color: "rgba(20, 27, 52, 0.8)",
            },
            itemStyle: {
              //柱形图圆角，鼠标移上去效果，如果只是一个数字则说明四个参数全部设置为那么多
              normal: {
                label: {
                  show: true, //开启显示
                  position: "top", //在上方显示
                  textStyle: {
                    //数值样式
                    color: "rgba(255, 255, 255, 0.7)",
                    fontSize: 12,
                  },
                },
                //柱形图圆角，初始化效果
                barBorderRadius: [5, 5, 0, 0],
                color: (params) => {
                  let colors = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#0A9ae0",
                    },
                    {
                      offset: 1,
                      color: "#26d5dd",
                    },
                  ]);
                  if (params.name === this.activeCity) {
                    colors = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#ffa31c",
                      },
                      {
                        offset: 1,
                        color: "#fcb450",
                      },
                    ]);
                  }
                  return colors;
                },
              },
            },
          },
        ],
      };
      option && mycenterbar.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>