<template>
	<div class="mian-border">
		<nav-bar title="资源概览"></nav-bar>
		<div class="zygl-main" v-for="(item, index) in mainData" :key="index">
			<div class="zygl-main-list">
				<el-row>
					<el-col :span="9">
						<span class="title-all">
              <img
								class="zygl-main-img"
								:src="index == 0
                  ? require('@/assets/images/zygl-1.png')
                  : index == 1
                  ? require('@/assets/images/zygl-2.png')
                  : index == 2
                  ? require('@/assets/images/zygl-3.png')
                  : ''"
                alt="" />
            </span>
          </el-col>
					<el-col :span="15">
						<div class="zygl-main-blue-tit">
							{{ item.name }}
						</div>
						<div class="zygl-main-white-tit">
							{{ item.val }}
						</div>
          </el-col>
        </el-row>
			</div>
		</div>
	</div>
</template>

<script>
	import navBar from "@/components/common/home/<USER>";
	export default {
		name: "InfoTable",
		components: { navBar },
		data() {
			return {
				mainData: [
					{
						name: "全区电子政务外网总数",
						val: "",
            key: "ELE_NUM"
					},
					{
						name: "客户数量",
						val: "",
            key: "CUSTOMER_NUM"
					},
					{
						name: "专线数量",
						val: "",
            key: "CIRCUIT_NUM"
					},
				],
			};
		},
		props: {
			// 标题
			title: {
				type: String,
				default: "标题",
			},
			// 标题位置 left | right | center
			titlePosition: {
				type: String,
				default: "left",
			},
			cityUuid: {
				type: String,
				default: () => ''
			}
		},
		watch: {
			cityUuid(n) {
				if(n) {
					this.getResourceInfo();
				}
			}
		},
    mounted() {
      this.getResourceInfo()
    },
    methods: {
      /**
       * <AUTHOR>
       * 资源概览接口 /kams/homepage/getResourceTotalNum
       * 返回值:
          {
              "ELE_NUM": 0, 全区电子政务外网总数
              "CIRCUIT_NUM": 0,  专线数量
              "CUSTOMER_NUM": 0  客户数量
          }
       */
      getResourceInfo() {
        let self = this;
        nc.rapi
          .request({
            url: "/rc-rm-kams-biz/kams/homepage/getResourceTotalNum",
            method: "post",
            data: {
				districtUuid: this.cityUuid ? this.cityUuid : ''
			},
            headers: { "Content-Type": "application/json;charset=UTF-8"},
          })
          .then((res) => {
            // console.log("资源概览result:", res.data);
            self.mainData.map(i => {return i.val = res.data[i.key]})
          })
          .catch((err) => {
            console.error(err);
            self.$message({
              showClose: true,
              duration: 2000,
              type: "error",
              message: "数据加载失败，系统异常!",
            });
          });
      }
    }
	};
</script>

<style lang="scss" scoped>
	.mian-border {
		// height: 55vh;
		padding-bottom: 2vh;
		border: 1px solid rgba(4, 56, 226, 0.58);
	}
	.title-bg {
		width: 100%;
		height: 4vh;
    line-height: 4vh;
		background-image: linear-gradient(
			to right,
			rgba(75, 176, 254, 0.34),
			transparent
		);
	}
	.title-all {
		display: inline-block;
	}

	.zygl-main {
		padding: 1vh 15px 0.5vh;
	}
	.zygl-main-list {
		background: url("../../../assets/images/zygl-bg.png");
		width: 100%;
		height: 8vh;
		background-position: center center;
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
	.zygl-main-img {
		width: 70px;
		padding-left: 20px;
	}
	.zygl-main-blue-tit {
		color: rgba(2, 219, 255, 1);
		font-size: 2.2vh;
		padding-top: 1vh;
	}
	.zygl-main-white-tit {
		color: rgba(255, 255, 255, 1);
		font-size: 2.1vh;
		padding-top: 0;
	}
</style>
