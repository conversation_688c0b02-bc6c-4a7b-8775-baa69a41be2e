<template>
  <div class="topoContainer">
    <!-- <div class="tuli">
      <div class="lujing">
        <span>工作路径</span>
        <div class="workcls"></div>
      </div>
      <div class="lujing">
        <span>保护路径</span>
        <div class="protectCls"></div>
      </div>
    </div>
    <div class="imgTuli">
      <div class="imgCls">
        <span>正常</span>
        <img :src="imgMap.green" />
      </div>
      <div class="imgCls">
        <span>中断</span>
        <img :src="imgMap.red" />
      </div>
      <div class="imgCls">
        <span>未知</span>
        <img :src="imgMap.yellow" />
      </div>
    </div> -->
    <div id="container"></div>
  </div>
</template>
<script>
import G6 from "@antv/g6";
import green from "@/assets/img/topo/green.png";
import yellow from "@/assets/img/topo/yellow.png";
import red from "@/assets/img/topo/red.png";
// import resultJson from "./data.json";
import resultJson from "./data5.json";
import { getHiddenNodes } from './util.js'

let status = {
  0: "正常",
  1: "中断",
  2: "未知",
};
// active：对象已激活，表示对象处于管理状态；inactive：对象未激活，表示对象处于未管理状态；test：对象处于测试状态；
// maintenance：对象处于维护状态；unknown：表示对象的管理状态未知；--：表示该对象里这个属性没意义
let manageStatus = {
  active: "已激活状态",
  inactive: "未管理状态",
  test: "测试状态",
  maintenance: "维护状态",
  unknown: "未知状态",
};
// "enableAson": "", //仅ASON设备支持此参数。网元使能智能。0=网元智能关闭；1=网元智能开启（光层和电层智能关闭）；2=光层智能开启；3=电层智能开启；4=光层和电层智能都开启
let enableAsonStatus = {
  0: "网元智能关闭",
  1: "网元智能开启（光层和电层智能关闭）",
  2: "光层智能开启",
  3: "电层智能开启",
  4: "光层和电层智能都开启",
};
let dashStyle = {
  lineDash: [10, 5],
  endArrow: true,
};

export default {
  name: "Topo",
  props: {
    // sendData: {
    //   type: Object,
    // },
  },
  data() {
    return {
      combosStyle: {},
      sendData: {},
      jsonData: {
        // id: "01",
        // label: "业务类型",
        // img: img1,
        // children: [],
        nodes: [],
      },
      imgMap: {
        green,
        yellow,
        red,
      },
    };
  },
  watch: {
    sendData: {
      handler(n, o) {
        console.log(n);
        let { nodes, edges } = n || [];
        const container = document.getElementById("container");
        const scrollWidth = container.scrollWidth;
        // console.log(scrollWidth)
        let allNodes = this.getNodes(nodes);

        // let edges = [];
        let combos = this.getCombos();
        let hiddenNodes = getHiddenNodes(combos, this.combosStyle);
        // console.log(hiddenNodes)

        nodes = [...allNodes, ...hiddenNodes];
        // edges = this.getEdges(primary);

        let jsonData = {
          nodes,
          edges,
          combos,
        };

        this.jsonData = jsonData;
        // this.jsonData = dataJson;
        this.updateTopo();
        // this.drawLink();
      },
    },
  },
  mounted() {
    // console.log(topoData);
    this.sendData = resultJson;
    this.initTopo();
  },
  methods: {
    savePosition() {

    },
    //数组去重
    deduplication(arr) {
      let newArr = [];
      let idArr = [];
      for (let item of arr) {
        if (!idArr.includes(item.label)) {
          newArr.push(item);
          idArr.push(item.label);
        }
      }
      return newArr;
    },
    // 线段数组
    getEdges(nodeArray) {
      let links = [];
      let source_targetArr = [];
      let typeCls = {};
      // endArrow: true,
      // if (flag === "protection") {
      //   typeCls = {
      //     style: dashStyle,
      //   };
      // } else {
      //   typeCls = {};
      // }
      for (let nodeList of nodeArray) {
        for (let i = 0; i < nodeList.length; i++) {
          let item = nodeList[i];
          let source = item.aportId;
          let target = item.zportId;
          if (!source_targetArr.includes(source + '_' + target)) {
            links.push({
              source,
              target,
              style: { endArrow: true, }
            });
            source_targetArr.push(source + '_' + target)
          }
        }
      }
      // console.log(source_targetArr)
      // console.log(links)
      G6.Util.processParallelEdges(links);
      return links;
    },
    dealEdges(linkList) {
      let links = [];
      let tmpArr = [];
      let repeatArr = [];
      //   sourceAnchor: 4,
      // targetAnchor: 2,

      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (!tmpArr.includes(idStr)) {
          tmpArr.push(idStr);
        } else {
          repeatArr.push(idStr);
        }
      }
      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (repeatArr.includes(idStr)) {
          if (item.type === "line-arrow") {
            //工作路径
            links.push({
              ...item,
              sourceAnchor: 4,
              targetAnchor: 2,
            });
          } else {
            //保护路径
            links.push({
              ...item,
              sourceAnchor: 5,
              targetAnchor: 3,
            });
          }
        } else {
          links.push(item);
        }
      }

      return links;
    },
    getCombos() {
      return [
        { id: "combo1", label: "骨干层", type: 'backbone', x: this.combosStyle.width, y: this.combosStyle.height * 1, },
        { id: "combo2", label: "核心层", type: 'region', x: this.combosStyle.width, y: this.combosStyle.height * 2.2, },
        { id: "combo3", label: "汇聚层", type: "local", x: this.combosStyle.width, y: this.combosStyle.height * 3.4, },
        { id: "combo4", label: "接入层", type: "access", x: this.combosStyle.width, y: this.combosStyle.height * 5, },
      ]
    },
    getNodes(datas) {
      let allNodes = [];
      let allNodeIds = [];
      let comboMap = {
        骨干层: 'combo1',
        核心层: 'combo2',
        汇聚层: 'combo3',
        接入层: 'combo4',
      }
      // for (let item of datas) {
      //   for (let item2 of item) {
      //     if (!allNodeIds.includes(item2.aportId)) {
      //       allNodes.push(item2);
      //       allNodeIds.push(item2.aportId);
      //     }
      //   }
      // }
      let nodes = datas.map(item => {
        return {
          id: item.id,
          label: `${item?.label?.trim()}`,
          img: green,
          comboId: comboMap[item.netLevelName],
          data: item
        }
      })
      // console.log(allNodes, allNodeIds)


      return nodes;
    },
    initTopo() {
      const container = document.getElementById("container");
      const width = container.scrollWidth;
      const height = container.scrollHeight || 800;
      this.combosStyle.width = width * 0.5;
      this.combosStyle.height = height * 0.15;

      const tooltip = this.drawTootip();

      this.registerCombo("backbone", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("region", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("local", { width: this.combosStyle.width, height: this.combosStyle.height });
      // this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height, refY: 90 });
      this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height * 1.5 });

      this.registerEdge("line-arrow");

      // this.registerCustomNode("myNode");

      const graph = new G6.Graph({
        container: "container",
        width,
        height,
        groupByTypes: false,
        // linkCenter: true,
        modes: {
          default: [
            // {
            //   type: "collapse-expand",
            //   onChange: function onChange(item, collapsed) {
            //     const data = item.getModel();
            //     data.collapsed = collapsed;
            //     return true;
            //   },
            // },
            "drag-canvas",
            // "zoom-canvas",
            "drag-node",

            "drag-combo",
            // "collapse-expand-combo",
            // "activate-relations",
            // "click-select",
          ],
        },
        defaultNode: {
          size: [40, 40],
          type: "image",
          img: green,
          color: "#ffffff",
          style: {
            // lineWidth: 20,
            stroke: "#000",
            fill: "#C6E5FF",
            // radius: 5
          },
        },
        // defaultCombo: {
        //   type: "cRect",
        //   size: [200, 100],
        // },
        maxZoom: 1.5,
        // defaultEdge: {
        //   shape: "quadratic",
        //   style: {
        //     endArrow: true,
        //     lineWidth: 2,
        //     stroke: "#ccc",
        //   },
        // },
        defaultEdge: {
          type: "quadratic",
          labelCfg: {
            autoRotate: true,
          },
        },
        // plugins: [tooltip],
        layout: {
          // type: 'concentric',
          // spacing: 1,

          // rankdir: "TB", // 可选，默认为图的中心
          // align: "DL", // 可选
          // nodesep: 30, //节点间距（px）。在 rankdir 为 'TB' 或 'BT' 时是节点的水平间距；在 rankdir 为 'LR' 或 'RL' 时代表节点的竖直方向间距
          // ranksep: 30, // 层间距（px）。在 rankdir 为 'TB' 或 'BT' 时是竖直方向相邻层间距；在 rankdir 为 'LR' 或 'RL' 时代表水平方向相邻层间距
          // controlPoints: true, // 是否保留布局连线的控制点
        },
      });

      graph.node(function (node) {
        let position = "bottom";
        let rotate = 0;
        if (!node.children) {
          position = "bottom";
          rotate = -Math.PI / 8;
        }
        return {
          label: node.label,
          labelCfg: {
            position,
            // offset: 50,
            style: {
              textAlign: "center",
              rotate,
              fill: "#fff",
            },
          },
        };
      });

      graph.on('node:dragend', (evt) => {
        // const { item } = evt;
        // const model = item.getModel();
        // const { x, y, width, height } = item.getBBox();
        // const bboxCache = model.bboxCache || {};
        // bboxCache.x = x;
        // bboxCache.y = y;
        // bboxCache.width = width;
        // bboxCache.height = height;
        // const edges = this.graph.save().edges;
        const { nodes, edges, combos } = this.graph.save();
        console.log(nodes, edges, combos)
        let param = {
          nodes: JSON.stringify(nodes),
          edges: JSON.stringify(edges),
          combos: JSON.stringify(combos)
        };

        console.log(param)

        // graph.getEdges().forEach((edge, i) => {
        //   console.log(edges, i);
        //   console.log(1111111)
        //   graph.updateItem(edge, {
        //     curveOffset: edges[i].curveOffset,
        //     curvePosition: edges[i].curvePosition,
        //   });
        // });
        // graph.updateItem(model.id, {
        //   bboxCache,
        // });
        // console.log(bboxCache)
      });


      if (typeof window !== "undefined")
        window.onresize = () => {
          if (!graph || graph.get("destroyed")) return;
          if (!container || !container.scrollWidth || !container.scrollHeight)
            return;
          graph.changeSize(container.scrollWidth, container.scrollHeight);
        };
      graph.data(this.jsonData);
      graph.render();
      this.graph = graph;
    },
    updateTopo() {
      this.graph.data(this.jsonData);
      this.graph.render();
      setTimeout(() => {
        // this.graph.fitView();
      });
    },
    registerEdge(linkName) {
      G6.registerEdge(linkName, {
        itemType: "edge",
        draw: function draw(cfg, group) {
          var startPoint = cfg.startPoint,
            endPoint = cfg.endPoint;

          var keyShape = group.addShape("path", {
            attrs: {
              path: [
                ["M", startPoint.x, startPoint.y],
                ["L", endPoint.x, endPoint.y],
              ],
              stroke: "#0CC",
              lineWidth: 1,
              // startArrow: {
              //   path: "M 10,0 L -10,-10 L -10,10 Z",
              //   path: G6.Arrow.vee(15, 20, 15),
              //   d: 15,
              // },
              endArrow: {
                path: G6.Arrow.vee(5, 5, 5),
                d: 5,
              },
            },
          });
          return keyShape;
        },
      });
    },

    registerCombo(comboName, cusConf) {
      G6.registerCombo(
        comboName,
        {
          drawShape: function drawShape(cfg, group) {
            const self = this;
            // 获取配置中的 Combo 内边距
            cfg.padding = cfg?.padding || [0, 0, 0, 0];
            cfg.labelCfg = {
              refY: cusConf?.refY || 40,
              position: "top",
              style: {
                fontSize: 18,
                fill: "#fff",
              },
            }
            cfg.size = [cusConf?.width || cfg.width, cusConf?.height || cfg.height];
            // 获取样式配置，style.width 与 style.height 对应 rect Combo 位置说明图中的 width 与 height
            const style = self.getShapeStyle(cfg);
            // 绘制一个矩形作为 keyShape，与 'rect' Combo 的 keyShape 一致
            const rect = group.addShape("rect", {
              attrs: {
                ...style,
                x: -style.height / 2 - cfg.padding[0],
                y: -style.width / 2 - cfg.padding[3],
                lineWidth: 2,
                fill: "rgba(255,255,255,0.1)",
                opacity: 0.5,
                stroke: "#0AEFC5",
                lineDash: [5, 5],
              },
              draggable: false,
              name: "combo-keyShape" + comboName, // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
            });

            return rect;
          },
        },
        "rect"
      );
    },

    // 画画线
    drawLink() {
      console.log(this.jsonData.nodes);
      let len = this.jsonData.nodes.length || 0;
      let nodeList = this.jsonData.nodes;

      for (let i = 0; i < len; i++) {
        if (nodeList[i + 1]) {
          let item1 = nodeList[i];
          let item2 = nodeList[i + 1];
          let source = item1.id;
          let target = item2.id;

          this.graph.addItem("edge", {
            source,
            target,
          });
        }
      }
    },
    // 提示框
    drawTootip() {
      let tooltip = new G6.Tooltip({
        offsetX: 10,
        offsetY: 20,
        getContent(e) {
          const outDiv = document.createElement("div");
          outDiv.style.width = "300px";
          // console.log(e.item.getModel());
          let curData = e.item.getModel();

          if (curData?.data) {
            let obj = curData?.data;
            outDiv.innerHTML = `
              <ul>
                <li class="node-cls">
                  <span>网元ID : </span> <span>${obj.nodeId}</span>
                </li>
                <li class="node-cls">
                  <span>网元名称 : </span> <span>${obj.name}</span>
                </li>
                <li class="node-cls">
                  <span>网元端口Id : </span><span>${obj.linkTpId}</span>
                </li>
                <li class="node-cls">
                  <span>设备IP : </span><span>${obj.ipAddress}</span>
                </li>
                <li class="node-cls">
                  <span>设备型号 : </span><span>${obj.productName}</span>
                </li>
                <li class="node-cls">
                  <span>网元通信状态 : </span>
                  <span> ${status[obj.communicationState] || "--"}</span>
                </li>
                <li class="node-cls">
                  <span>管理状态 : </span>
                  <span> ${manageStatus[obj.adminStatus] || "--"}</span>
                </li>
                <li class="node-cls">
                  <span>网元使能智能 : </span>
                  <span> ${enableAsonStatus[obj.enableAson] || "--"}</span>
                </li>

                <li class="node-cls">
                  <span>入端口名 : </span><span>${obj.tpNameEnter || "--"
              }</span>
                </li>
                <li class="node-cls">
                  <span>入端口ID : </span><span>${obj.portIdEnter || "--"
              }</span>
                </li>
                <li class="node-cls">
                  <span>入端口类型 : </span><span>${obj.tpTypeEnter || "--"
              }</span>
                </li>

                <li class="node-cls">
                  <span>出端口名 : </span><span>${obj.tpNameOut || "--"}</span>
                </li>
                <li class="node-cls">
                  <span>出端口ID : </span><span>${obj.portIdOut || "--"}</span>
                </li>
                <li class="node-cls">
                  <span>出端口类型 : </span><span>${obj.tpTypeOut || "--"
              }</span>
                </li>
              </ul>
            `;
          } else if (curData.label) {
            outDiv.innerHTML = `
              <ul>
                <li>业务名称：${curData.label || ""}</li>
              </ul>
            `;
          } else {
            return "";
          }

          return outDiv;
        },
        itemTypes: ["node"],
      });

      return tooltip;
    },
  },
};
</script>

<style lang="less" scoped></style>

<style lang="less">
.g6-component-tooltip {
  .node-cls {
    display: flex;

    span {
      display: flex;
      align-items: center;
      height: 30px;
    }

    :first-child {
      width: 90px;
    }
  }
}

.topoContainer {
  position: relative;

  #container {
    display: flex;
  }

  .tuli {
    position: absolute;
    bottom: 20px;
    left: 30px;
    padding: 16px;

    .lujing {
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .workcls {
        width: 80px;
        height: 2px;
        background-color: #2ecccc;
      }

      .protectCls {
        width: 80px;
        height: 2px;
        background: repeating-linear-gradient(to right,
            #fff 0px,
            #fff 0px,
            #ccc 4px,
            #ccc 8px);
      }
    }
  }

  .imgTuli {
    position: absolute;
    bottom: 20px;
    left: 250px;
    padding: 16px;
    display: flex;

    .imgCls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;

      img {
        width: 30px;
        margin-left: 8px;
      }
    }
  }
}
</style>
