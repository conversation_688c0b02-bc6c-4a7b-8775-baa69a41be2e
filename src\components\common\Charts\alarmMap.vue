<template>
  <div class="element_num bg cf">
    <div class="content tac">
      <search-query @transfer="getsearch"></search-query>
      <!-- <el-select
        v-model="form.city"
        placeholder="请选择地区"
        @change="changeCity"
      >
        <el-option
          v-for="(i, idx) in cityList"
          :key="idx"
          :label="i.label"
          :value="i.value"
        >
        </el-option>
      </el-select> -->

      <div
        id="nmg_map"
        class="map-box"
        ref="nmgMap"
        :style="{height:`${518*contrastRadio}px`}"
      ></div>
    </div>
  </div>
</template>

<script>
import searchQuery from "@/components/common/home/<USER>";
import echarts from "echarts";
import { innerMongoliaMap } from "@/assets/js/nmg-map.js";
export default {
  name: "nmg_maps",
  props: {
    contrastRadio:{
        type: Number,
        default: 1
    },
  },
  components: {
    searchQuery,
  },
  data() {
    return {
      cityInfo: {},
      cityIndex: 0,
      form: {
        city: undefined,
      },
      cityList: [],
      labelCol: { span: 4 },
      wrapperCol: { span: 12, flex: 1 },
    };
  },
  watch: {
    innerMongoliaMap: {
      handler(n, o) {
        if (n && n.length > 0) {
          this.cityList = n.map((i) => ({
            key: i.value,
            ...i,
          }));
          this.changeMap("内蒙古自治区", 0);
        }
      },
      deep: true,
    },
  },

  mounted() {
    this.$nextTick(() => {
      if (innerMongoliaMap && innerMongoliaMap.length > 0) {
        this.cityList = innerMongoliaMap.map((i) => ({
          key: i.value,
          ...i,
        }));
      }
      this.changeCity("10");
    });
  },
  methods: {
    /**
     * <AUTHOR>
     * 切换地区
     */
    getsearch(val) {
      this.changeCity(val);
    },
    changeCity(cityVal) {
      if (cityVal) {
        let cityName = "";
        this.cityIndex = null;
        this.cityList.map((i, idx) => {
          if (i.value === cityVal) {
            cityName = i.label;
            this.cityIndex = idx;
            this.cityInfo = i;
            this.changeMap(cityName, this.cityIndex);
            let data = [];
            this.initMap(data);
            echarts.registerMap(cityName, i.json);
          }
        });
        // console.log('cityInfo', cityInfo);
      }
    },
    /**
     * <AUTHOR>
     * 切换地图
     */
    changeMap(mapName, mapIndex) {
      echarts.registerMap(mapName, innerMongoliaMap[mapIndex].json);
    },
    /**
     * 初始化地图
     * <AUTHOR>
     */
    initMap(data) {
      let cityName = this.cityInfo.label ? this.cityInfo.label : null;
      // let cityZoom =  this.contrastRadio;//this.cityInfo.cityZoom ? this.cityInfo.cityZoom : 0.98;
      let cityZoom =  this.cityInfo.cityZoom ? this.cityInfo.cityZoom : 0.98;
      if (!data) return;
      let width = document.getElementById("nmg_map").clientWidth;
      let option = {
        title: {
          show: false,
        },
        tooltip: {
          show: true,
          trigger: "item",
          triggerOn: "mousemove|click",
          backgroundColor: "rgba(255,255,255, 0.9)",
          borderColor: "#FF9800",
          borderWidth: 1,
          textStyle: {
            color: "#fff",
          },
          triggerTooltip: true,
          confine: true, // 将 tooltip 框限制在图表的区域内
          formatter: (params) => {
            // console.log("params", params);
            // this.currentArea = params.data;
            // let name = params.name;
            // let marker = params.marker;
            // let num = params.data.alarmNum ? params.data.alarmNum : 0;
            let str = `<div>
								<p>${params.name}</p>
							</div>`;
            return str;
          },
        },
        geo: [
          // geo中设置的map的zlevel等级为1在series map的上层
          {
            // 地图背景层
            type: "map",
            map: cityName,
            zLevel: -3,
            zoom: cityZoom,
            aspectScale: 1,
            layoutCenter: ["50%", "50%"],
            layoutSize: width,
            roam: false,
            scaleLimit: {
              //滚轮缩放的极限控制
              min: 0.5,
              max: 5,
            },
            silent: true, // 图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件
            label: {
              // 城市名称
              show: false,
              emphasis: {
                // 鼠标经过样式
                show: false,
              },
            },
            itemStyle: {
              // borderWidth: 2,
              // borderColor: 'rgba(19, 184, 240, 1)',
              // shadowColor: 'rgba(19, 184, 240, 1)',
              // shadowOffsetY: 2,
              // shadowBlur: 5,
              areaColor: "#1E4791",
              emphasis: {
                label: {
                  show: false,
                },
                borderWidth: 0,
                areaColor: "#1E4791",
                // borderColor: 'rgba(19, 184, 240, 0.8)'
              },
            },
          },
          // { // 地图外边框
          //   type: 'map',
          //   map: cityName,
          //   zLevel: -2,
          //   zoom: cityZoom,
          //   aspectScale: 1,
          //   layoutCenter: ["50%", "50%"],
          //   layoutSize: width,
          //   roam: false,
          //   scaleLimit: { //滚轮缩放的极限控制
          //     min: 0.5,
          //     max: 5
          //   },
          //   silent: true, // 图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件
          //   label: { // 城市名称
          //     show: false,
          //     emphasis: { // 鼠标经过样式
          //       show: false,
          //     }
          //   },
          //   itemStyle: {
          //     borderWidth: 3,
          //     borderColor: 'rgba(162, 231, 255, 0.77)',
          //     emphasis: {
          //       borderWidth: 3,
          //       borderColor: 'rgba(162, 231, 255, 0.77)',
          //     }
          //   },
          // },
        ],
        series: [
          {
            // 地图表层
            type: "map",
            map: cityName,
            zLevel: -1,
            layoutCenter: ["50%", "50%"], //地图位置 left / right / top / bottom / width / height / 如果宽高比大于 1 则宽度为 100，如果小于 1 则高度为 100，保证了不超过 100x100 的区域
            layoutSize: width, // 地图大小，支持相对于屏幕宽高的百分比或者绝对的像素大小
            aspectScale: 1, // 地图的长宽比
            zoom: cityZoom, //当前视角的缩放比例
            roam: false, //是否开启平游或缩放 默认不开启。如果只想要开启缩放或者平移，可以设置成'scale'或者'move'。设置成true为都开启
            scaleLimit: {
              //滚轮缩放的极限控制
              min: 0.5,
              max: 5,
            },
            silent: false, // 图形是否不响应和触发鼠标事件，默认 false，即响应和触发鼠标事件
            // 显示城市名称
            label: {
              show: true,
              textStyle: {
                color: "#021B30",
                fontSize: 12,
              },
              emphasis: {
                // 鼠标经过样式
                color: "#021B30",
                show: true,
                fontSize: 14,
                fontWeight: "bold",
              },
            },
            // 区域颜色
            itemStyle: {
              areaColor: "#25BEF7",
              borderWidth: 1, // 区块的边界宽度
              borderColor: "rgba(162, 231, 255, 0.77)", // 区块的边框颜色
              // opacity: 1, // 支持从 0 到 1 的数字，为 0 时不绘制该图形
              // 高亮时区域的样式
              emphasis: {
                borderWidth: 2, // 区块的边界颜色
                areaColor: "#FFCB31",
                borderColor: "rgba(162, 231, 255, 0.77)", // 区块的边框颜色
              },
            },
            // 区域 hover 效果(高亮的样式)
            // emphasis: {
            //   /*
            //   * label所有属性
            //   * { show, position, distance, rotate, offset, formatter, color, fontStyle, fontWeight, fontFamily, fontSize, align, verticalAlign, lineHeight, backgroundColor, borderColor, borderWidth, borderRadius, padding, shadowColor, shadowBlur, shadowOffsetX, shadowOffsetY, width, height, textBorderColor, textBorderWidth, textShadowColor, textShadowBlur, textShadowOffsetX, textShadowOffsetY, rich }
            //   */
            //   label: {
            //     show: true, // 是否显示标签
            //     // textStyle: {
            //     //   color: "#FFFFFF",
            //     //   fontSize: 12,
            //     // },
            //     // position: 'top',
            //     /**
            //      * 标签的位置
            //      * 支持：top / left / right / bottom / inside / insideLeft / insideRight / insideTop / insideBottom / insideTopLeft / insideBottomLeft / insideTopRight / insideBottomRight / 绝对的像素值:[10, 10] / 相对的百分比: position: ['50%', '50%']
            //      */
            //     // 标签的位置,
            //     // distance: 5, // 距离图形元素的距离。当 position 为字符描述值（如 'top'、'insideRight'）时候有效
            //     // rotate: 30, // 标签旋转。从 -90 度到 90 度。正值是逆时针
            //     // offset: [30, 40], // 是否对文字进行偏移。默认不偏移。
            //     // formatter: '{b}: {@score}', // 标签内容格式器，支持字符串模板和回调函数两种形式，字符串模板与回调函数返回的字符串均支持用 \n 换行。
            //     /*
            //     * 字符串模板 模板变量有：
            //     * { a }：系列名
            //     * { b }：数据名
            //     * { c }：数据值
            //     * { @xxx }：数据中名为'xxx'的维度的值，如{ @product }表示名为'product'` 的维度的值
            //     * {@[n]}：数据中维度n的值，如{@[3]}` 表示维度 3 的值，从 0 开始计数。
            //     */
            //     color: '#FFFFFF',
            //     // fontStyle: 'normal', // 字体风格 'normal' / 'italic' / 'oblique'
            //     // fontWeight: 'normal', // 字体粗细 'normal' / 'bold' / 'bolder' / 'lighter' / 100
            //     // fontFamily: 'Microsoft YaHei', // 字体系列 'serif' / 'monospace' / 'Arial' / 'Courier New'...
            //     fontSize: 14,
            //     // align: 'center', // 文字水平对齐方式，默认自动 'left' / 'center' / 'right'
            //     // verticalAlign: 'bottom', // 文字垂直对齐方式，默认自动
            //     // backgroundColor: 'transparent', //文字块背景色
            //     // 也可以直接使用图片，例如：
            //     // backgroundColor: {
            //     //   image: 'xxx/xxx.png'
            //     //   // 这里可以是图片的 URL，
            //     //   // 或者图片的 dataURI，
            //     //   // 或者 HTMLImageElement 对象，
            //     //   // 或者 HTMLCanvasElement 对象。
            //     // },
            //     // borderColor: 'transparent', // 文字块边框颜色
            //     // borderWidth: 0, // 文字块边框宽度
            //     // borderRadius: 0, // 文字块的圆角
            //     // padding: 0, // 文字块的内边距 number or Array [3, 4, 5, 6]
            //     // shadowColor: 'transparent', // 文字块的背景阴影颜色
            //     // shadowBlur: 0, // 文字块的背景阴影长度
            //     // shadowOffsetX: 0, // 文字块的背景阴影 X 偏移
            //     // shadowOffsetY: 0, // 文字块的背景阴影 Y 偏移
            //     // width: 60, // 指定的是内容宽，不包含 padding 可以数字或百分比
            //     // height: 60, // 高， 一般不指定，默认
            //     // textBorderColor: 'transparent', // 文字本身的描边颜色
            //     // textBorderWidth: 0, // 文字本身的描边宽度
            //     // textShadowColor: 'transparent', // 文字本身的阴影颜色
            //     // textShadowBlur: 0, // 文字本身的阴影长度
            //     // textShadowOffsetX: 0, // 文字本身的阴影 X 偏移
            //     // textShadowOffsetY: 0, // 文字本身的阴影 Y 偏移

            //     //   rich: { // 自定义富文本样式，配合 formatter 使用
            //     //     /**
            //     //      * 样式全部属性
            //     //      * { color , fontStyle , fontWeight , fontFamily , fontSize , align , verticalAlign , lineHeight , backgroundColor , borderColor , borderWidth , borderRadius , padding , shadowColor , shadowBlur , shadowOffsetX , shadowOffsetY , width , height , textBorderColor , textBorderWidth , textShadowColor , textShadowBlur , textShadowOffsetX , textShadowOffsetY }
            //     //      */
            //     //     a: {
            //     //       color: 'red',
            //     //       lineHeight: 10
            //     //     },
            //     //     b: {
            //     //       backgroundColor: {
            //     //         image: '/static/icon.png', // static路径下的图片可以找到
            //     //       },
            //     //       height: 40
            //     //     },
            //     //     x: {
            //     //       fontSize: 18,
            //     //       fontFamily: 'Microsoft YaHei',
            //     //       borderColor: '#449933',
            //     //       borderRadius: 4
            //     //     },
            //     //   },
            //     //   formatter: [
            //     //     '{a|样式a}',
            //     //     '{b|样式b} 默认样式 {x|样式x}'
            //     //   ].join('\n'),

            //   },
            // },
            tooltip: {
              show: true,
              trigger: "item",
              triggerOn: "mousemove|click",
              backgroundColor: "rgba(1, 13, 34, 0.4);",
              borderColor: "#3341B6",
              borderWidth: 1,
              shadowColor: "rgba(255,255,255,0.5)", // 阴影颜色
              shadowBlur: 10, // 背景阴影长度
              shadowOffsetX: 2, // 背景阴影 X 偏移
              shadowOffsetY: 2, // 背景阴影 Y 偏移
              textStyle: {
                color: "#FFFFFF",
              },
              formatter: (params) => {
                // console.log('params', params);
                this.currentArea = params.data;
                let name = params.name;
                let marker = params.marker;
                let str = `<div>
                    <p><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FFCB31;"></span> ${params.name}</p>
                  </div>`;
                return str;
              },
            },
          },
          {
            name: "时延散点地图",
            type: "effectScatter",
            coordinateSystem: "geo",
            showEffectOn: "render",
            // geoIndex: 2, //
            zlevel: 0, // 用于 Canvas 分层，不同zlevel值的图形会放置在不同的 Canvas 中，Canvas 分层是一种常见的优化手段。我们可以把一些图形变化频繁（例如有动画）的组件设置成一个单独的zlevel。需要注意的是过多的 Canvas 会引起内存开销的增大zlevel 大的 Canvas 在上面
            rippleEffect: {
              number: 2, // 波纹的数量。
              period: 5, // 动画的周期，秒数。
              scale: 4, // 动画中波纹的最大缩放比例。
              brushType: "stroke", // 波纹的绘制方式，可选 'stroke' 和 'fill'。
            },
            tooltip: {
              // 时延撒点鼠标经过显示的提示框
              trigger: "item",
              backgroundColor: "transparent",
              borderColor: "transparent",
              extraCssText: "z-index:100;color:#fff;",
              confine: true, //是否将 tooltip 框限制在图表的区域内
              formatter: function (params, ticket, callback) {
                //根据业务自己拓展要显示的内容
                var res = "";
                var name = params.name;
                var count = params.value ? params.value : 0;
                res = `<div style="box-shadow: 0 0 10px #3BD9D9; padding: 10px; position: absolute; top: 0; left:0; border-radius: 4px; border: 1px solid #04b9ff; background: linear-gradient(to bottom,  #51bfd4 0%,rgba(35,90,178,.8) 100%);">
                    <div style='color:yellow; font-size: 14px;'>${name}</div>
                    <div style="display: flex; align-items: center;padding-top: 6px;">
                      <div style="height: 6px; width: 6px; border-radius: 50%; background:yellow; margin-right: 10px;"></div>
                      <span style='color:#fff;font-size: 12px;margin-right: 20px;'>坐标</span>
                      <span style="font-size: 12px;font-family: 'PangMenZhengDao'">[${count}]</span>
                    </div>
                  </div>`;
                return res;
              },
            },
            symbolSize: 6,
            label: {
              formatter: "{b}",
              position: [26, -12],
              color: "red",
              fontSize: 16,
              fontWeight: 600,
              textBorderWidth: 0,
              textShadowBlur: 0,
              borderWidth: 1,
              borderRadius: 6,
              borderColor: "#41ADCC",
              padding: [6, 10, 6, 10],
              backgroundColor: "rgba(0,0,0, 0.6)",
            },
            itemStyle: {
              color: "yellow",
            },
            data: [
              // {
              //   name: "内蒙古阿拉善盟额济纳旗居延街001",
              //   value: [101.0593104, 41.95590676]
              // }, {
              //   name: "内蒙古阿拉善盟额济纳旗居延街002",
              //   value: [100.0593104, 41.96590676]
              // }
            ],
          },
          // 飞线 1
          {
            type: "lines", // 绘制连线
            zlevel: 0,
            geoIndex: 2,
            large: true,
            selectedMode: "single",
            effect: {
              show: true,
              period: 2, // 箭头指向速度，值越小速度越快
              trailLength: 0.02, // 特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: "arrow",
              symbolSize: 4, // 图标大小
            },
            lineStyle: {
              normal: {
                color: "#ffd000",
                type: "dotted",
                width: 1, // 线条宽度
                opacity: 0.1, // 尾迹线条透明度
                curveness: -0.3, // 尾迹线条曲直度
              },
            },
            label: {
              show: true,
              position: "middle",
              color: "#ffd000",
              fontSize: 16,
              fontWeight: 300,
              textBorderWidth: 0,
              textShadowBlur: 0,
              borderRadius: 5,
              padding: [6, 10, 6, 10],
              backgroundColor: "rgba(0,0,0,0.25)",
            },
            // emphasis: {
            //   lineStyle: {
            //     type: 'solid'
            //   },
            //   label: {
            //     show: true
            //   }
            // },
            // select: {
            //   label: {
            //     show: true
            //   }
            // },
            // data: []
            data: [
              // {
              //   fromName: "内蒙古阿拉善盟额济纳旗居延街001",
              //   toName: "内蒙古阿拉善盟额济纳旗居延街002",
              //   coords: [[101.0593104, 41.95590676], [100.0593104, 41.96590676]],
              //   name: "111"
              // }
            ],
          },
          // 飞线 2
          {
            type: "lines", // 绘制连线
            zlevel: 0,
            geoIndex: 2,
            large: true,
            selectedMode: "single",
            effect: {
              show: true,
              period: 2, // 箭头指向速度，值越小速度越快
              trailLength: 0.02, // 特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: "arrow",
              symbolSize: 4, // 图标大小
            },
            lineStyle: {
              normal: {
                color: "#ffd000",
                type: "dotted",
                width: 1, // 线条宽度
                opacity: 0.1, // 尾迹线条透明度
                curveness: 0.3, // 尾迹线条曲直度
              },
            },
            label: {
              show: true,
              position: "middle",
              color: "#ffd000",
              fontSize: 16,
              fontWeight: 300,
              textBorderWidth: 0,
              textShadowBlur: 0,
              borderRadius: 5,
              padding: [6, 10, 6, 10],
              backgroundColor: "rgba(0,0,0,.36)",
            },
            // emphasis: {
            //   lineStyle: {
            //     type: 'solid'
            //   },
            //   label: {
            //     show: true
            //   }
            // },
            // select: {
            //   label: {
            //     show: true
            //   }
            // },
            data: [],
          },
          // 飞线 3
          {
            type: "lines", // 绘制连线
            zlevel: 0,
            geoIndex: 2,
            large: true,
            selectedMode: "single",
            effect: {
              show: true,
              period: 2, // 箭头指向速度，值越小速度越快
              trailLength: 0.02, // 特效尾迹长度[0,1]值越大，尾迹越长重
              symbol: "arrow",
              symbolSize: 4, // 图标大小
            },
            lineStyle: {
              normal: {
                color: "#ffd000",
                type: "dotted",
                width: 1, // 线条宽度
                opacity: 0.1, // 尾迹线条透明度
                curveness: -0.05, // 尾迹线条曲直度
              },
            },
            label: {
              show: true,
              position: "middle",
              color: "#ffd000",
              fontSize: 16,
              fontWeight: 300,
              textBorderWidth: 0,
              textShadowBlur: 0,
              borderRadius: 5,
              padding: [6, 10, 6, 10],
              backgroundColor: "rgba(0,0,0,.36)",
            },
            // emphasis: {
            //   lineStyle: {
            //     type: 'solid'
            //   },
            //   label: {
            //     show: true
            //   }
            // },
            // select: {
            //   label: {
            //     show: true
            //   }
            // },
            data: [],
          },
        ],
      };

      let echart = echarts.init(document.getElementById("nmg_map"));
      echart.setOption(option);

      // echart.on("mousemove", (params) => {
      //   console.log('params', params);
      //   echart.dispatchAction({
      //     type: 'showTip',
      //     geoIndex: 0,
      //     seriesIndex: 0,
      //     name: params.name,
      //   })
      // })
      echart.off("click");
      // 地图点击事件
      echart.on("click", (params) => {
        // console.log("地图点击事件params", params);
      });
    },
  },
};
</script>

<style scoped>
.bg {
  /* background: url("@/assets/img/myPic/bgi/bgi-1.gif") no-repeat; */
  background-size: 100% 100%;
  min-height: 100vh;
}

.map-box {
  width: 100%;
  display: inline-flex;
  justify-content: center;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  /* border: 1px dashed deepskyblue; */
  border-radius: 20px;
}
</style>
