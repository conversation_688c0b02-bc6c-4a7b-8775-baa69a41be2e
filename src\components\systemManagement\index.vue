<template><!-- 告警监控 -->
<div class="alarm-monitor cf">
  <div class="con-top">
    <el-row :gutter="20" class="container">
      <el-col :span="4" class="muen-left">
        <div class="menu-bg">系统管理</div>
        <el-menu default-active="userManagement" class="el-menu-vertical-demo" @select="handleMenuSelect">

          <el-menu-item index="userManagement">
            <span slot="title">用户管理</span>
          </el-menu-item>
          <el-menu-item index="roleManagement">
            <span slot="title">角色管理</span>
          </el-menu-item>
          <el-menu-item index="funcManagement">
            <span slot="title">功能点管理</span>
          </el-menu-item>
          <el-menu-item index="menuManagement">
            <span slot="title">菜单管理</span>
          </el-menu-item>
        </el-menu>
      </el-col>
      <el-col :span="19" class="content_right">
        <user-management v-if="classId == 'userManagement'" />
        <role-management v-if="classId == 'roleManagement'" />
        <func-management v-if="classId == 'funcManagement'" />
        <menu-management v-if="classId == 'menuManagement'" />
      </el-col>
    </el-row>
  </div>
</div>
</template>

<script>
import userManagement from './userManagement.vue';
import roleManagement from './roleManagement.vue';
import funcManagement from './funcManagement.vue';
import menuManagement from './menuManagement.vue';

const cityOptions = ['机房名称', '机房ID', '所属区域管理', '机房类型'];
export default {
  data() {
    return {
      // 遮罩层
      personLoading: false,
      dialogExport: false,
      loading: false,
      formLoading: false,
      showSearch: true,  // 显示搜索条件
      pageType: 0,
      targetId: "",
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      tableColumns: {},//表头
      resourceData: [],
      resourceApis: "/rc-rm-kams-biz/resource/",
      queryForm: {}, // 查询表单数据
      queryFields: [],
      options: [],
      currentPage: 1,

      dialogFormVisible: false,
      checkAll: false,
      checkedCities: [],
      checkedCitiesa: [],
      cities: cityOptions,
      isIndeterminate: true,
      allOptions: [],
      tableColumnAll: [],
      restaurants: [],
      state: '',
      timeout: null,
      count: 1, // 下拉页数
      query: '',
      queryParam: '',
      classId: 'userManagement',
      limit: 10,
      tableColumn: [],
      classIdS: '',
      fileType: 'xlsx',
      lists: [],
      className: ''




    };
  },
  components: {
    userManagement,
    roleManagement,
    funcManagement,
    menuManagement,
  },
  watch: {
    alarmListData: {
      // eslint-disable-next-line no-unused-vars
      handler(n, o) { },
      deep: true,
    },
    rBarData: {
      // eslint-disable-next-line no-unused-vars
      handler(n, o) { },
      deep: true,
    },
  },
  created() {

  },
  mounted() {

  },
  methods: {
    handleMenuSelect(index) {
      this.classId = index;
    },
  }
};
</script>

<style scoped lang="less">
.alarm-monitor {
  width: 100%;
  overflow: hidden;
  position: relative;

  .container {
    background-color: transparent;
  }

  .muen-left {
    height: 85vh;
    margin: 20px 20px 0 20px;
    background: url("../../assets/images/menu_left.png") center center no-repeat;
    background-size: 100% 100%;
  }

  .menu-bg {
    background: url("../../assets/images/menu-bg-yes.png") center center no-repeat;
    background-size: 100% 100%;
    height: 50px;
    margin: 25px;
    font-size: 20px;
    text-align: center;
    line-height: 50px;
  }

  ::v-deep .el-menu {
    border: none;
    background-color: transparent;
    margin: 0 20px;
  }

  ::v-deep .el-menu-item {
    background-color: rgba(9, 33, 86, .8);
    color: #fff;
    text-align: center;
    border-bottom: 1px rgb(7 13 26) solid;
    border-radius: 5px;

    &:hover {
      color: #fff;
      background: rgba(15, 52, 124, .5);

      .star {
        color: #333333;
      }
    }

    &.is-active {
      color: #fff;
      background: rgba(15, 52, 124, .5);

      & span {
        color: #ffffff;
      }

      & .star {
        color: #ffffff;
      }

      & :hover .star {
        color: #ffffff;
      }
    }
  }

  .content_right {
    height: 85vh;
    margin: 20px 0px 0 0;
    padding: 0 30px !important;

    background: url("../../assets/images/content_right.png") center center no-repeat;
    background-size: 100% 100%;

    .nc-query {
      border-radius: 2px;
      background-color: rgba(23, 70, 137, 0.25);
      padding: 24px 16px 12px;
      margin: 25px 0px;
      height: 10vh;
    }

    /deep/.el-form-item__label {
      color: #fff;
    }

    /deep/.el-input__inner {
      background-color: transparent;
      border: 1px solid rgba(37, 190, 247, 0.5);
    }

    /deep/.el-button--primary {
      color: #FFF;
      background-color: #409EFF;
      border-color: #409EFF;
    }

    /deep/.el-button--default {
      color: #409EFF;
      background-color: transparent;
      border-color: #409EFF;
    }
  }

  ::v-deep {

    // .el-table__body {
    // 	tr {
    // 		cursor: pointer;
    // 	}
    // }
    .el-table,
    .el-table__expanded-cell {
      background-color: transparent !important;
    }

    .el-table thead.is-group th.el-table__cell {
      background-color: transparent !important;
    }

    /*定义滚动条轨道 内阴影+圆角*/
    .el-table__body-wrapper::-webkit-scrollbar-track {
      box-shadow: inset 0 0 0Px #163479 !important;
      border-radius: 10Px;
      background-color: #163479 !important;
    }

    .el-table tr {
      color: #fff;
      background-color: transparent;
      cursor: pointer;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: #06529d !important;
      cursor: pointer;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped:hover>td.el-table__cell {
      background-color: #06529d !important;
      cursor: pointer;
    }

    .el-table--striped {
      background-color: rgba(12, 33, 87, 0) !important;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
      background: rgba(12, 21, 69, 1);
    }

    .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
      width: 10Px;
    }

    /*定义滑块 内阴影+圆角*/
    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      border-radius: 10Px;
      box-shadow: inset 0 0 0Px #4C72C9;
      background: linear-gradient(-55deg, #4C72C9 0%, #4C72C9 100%);
    }

    .el-table__body-wrapper::-webkit-scrollbar {
      width: 8Px; // 横向滚动条
      height: 8Px; // 纵向滚动条 必写
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: transparent;
    }

    .el-table--border th.el-table__cell.gutter {
      display: none;
    }

    /* 表格鼠标悬浮时的样式（高亮） */
    .el-table--enable-row-hover .el-table__body tr:hover {
      background-color: rgba(255, 255, 255, 0);
    }

    /*表格鼠标悬停的样式（背景颜色）*/
    .el-table tbody tr:hover>td {
      background-color: rgba(255, 255, 255, 0);
    }

    .el-table__body .el-table__row.hover-row td {
      background-color: rgba(255, 255, 255, 0);
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      // border-bottom: 1px solid rgba(15, 52, 124, .5);
      border: 1px solid transparent;
    }

    .el-table--border .el-table__cell,
    .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
      // border-right: 1px solid rgba(15, 52, 124, .5);
      border: 1px solid transparent;
    }

    .el-table tr:nth-child(even) {
      background: rgba(40, 72, 121, 0.29);
    }


  }

  /deep/.el-select-dropdown__item.hover {
    color: #fff;
    background: rgba(4, 56, 226, 0.29);
  }

  /deep/.el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: transparent;
    border: 1px solid rgba(4, 56, 226, 0.58);
  }

  /deep/.el-pagination__total {
    color: #fff;
  }

  /deep/.el-input__inner {
    color: #fff;
    background-color: transparent;
    border: 1px solid rgba(4, 56, 226, 0.58);
  }

}
</style>