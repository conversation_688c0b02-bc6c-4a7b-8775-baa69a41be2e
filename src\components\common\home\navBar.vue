<template>
  <div class="title-bg">
    <span class="title-all line-white"></span>
    <span class="title-all title-white">{{ title }}</span>
  </div>
</template>

<script>
export default {
  name: "navbar",
  props: {
    title: {
      type: String,
      default: null,
    },
  },
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.title-all {
  display: inline-block;
}
.line-white {
  height: 2vh;
  width: 4px;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 5px 0px rgba(255, 255, 255, 1);
  vertical-align: middle;
  margin-right: 10px;
}
.title-white {
  color: #fff;
}

.see-all {
  color: rgba(2, 219, 255, 1);
  font-size: 14px;
  float: right;
  padding-right: 20px;
}
.title-bg {
  width: 100%;
  height: 4vh;
  line-height: 4vh;
  background-image: linear-gradient(
    to right,
    rgba(75, 176, 254, 0.34),
    transparent
  );
}
</style>
