<template>
	<!-- 自定义列表 -->
	<div class="cust-table hf">
		<el-table
			height="200"
			:data="tableList"
			:header-cell-style="{
				color: '#9ED8FF',
				'background-color': 'rgba(15, 52, 124, 0.57)',
				'text-align': 'center',
				'font-size': '14px',
			}"
			v-loading="loading"
			element-loading-text="拼命加载中..."
			:highlight-current-row="false"
			class="table-part fs18 wf"
			@row-click="rowClick"
			:rowKey="(record) => record.index"
			:row-class-name="setRowClassName"
			:show-overflow-tooltip="false"
		>
			<!-- :rowKey="record => record.resourceName || record.svcTitle" -->
			<!-- 列渲染 -->
			<el-table-column
				v-for="(i, idx) in colConfigs"
				:key="'col' + idx"
				:prop="i.prop"
				:label="i.label"
				:align="i.align"
				:width="i.width"
				:show-overflow-tooltip="true"
			>
				<template slot-scope="scope">
					<div>
						<span v-if="i.prop === 'num' && scope.row.num !== null" :style="Math.round(parseFloat(scope.row.num.substr(0, 5)) * 1000) / 1000 >= custPercent ? 'color: red !important;' : '' ">
							<i class="el-icon-warning" v-if="scope.row.num && Math.round(parseFloat(scope.row.num.substr(0, 5)) * 1000) / 1000 >= custPercent"></i>
							<span class="ml10" :style="Math.round(parseFloat(scope.row.num.substr(0, 5)) * 1000) / 1000 >= custPercent ? 'color: red !important;' : '' ">{{scope.row.num ? Math.round(parseFloat(scope.row.num.substr(0, 5)) * 1000) / 1000 + `%` : null}}</span>
						</span>
						<span v-else :style="scope.row.num && Math.round(parseFloat(scope.row.num.substr(0, 5)) * 1000) / 1000 >= custPercent ? 'color: red !important;' : ''">{{ scope.row[i.prop] ? scope.row[i.prop] : "" }}</span>
					</div>
				</template>
			</el-table-column>

			<div slot="empty">
				<i class="el-icon-warning fs16"></i>
				<span class="ml10 fs16">暂无数据！</span>
			</div>
		</el-table>
	</div>
</template>

<script>
	export default {
		name: "custTable",
		props: {
			list: {
				type: Array,
				default: () => [],
			},
			colConfigs: {
				type: Array,
				default: () => [],
			},
			loading: {
				type: Boolean,
				default: () => false,
			},
			selRow: {
				type: Object,
				default: () => {},
			},
			percent: {
				type: Number,
				default: () => null,
			},
			tabName: {
				type: String,
				default: () => "",
			},
			rKey: {
				type: String,
				default: () => "",
			},
		},
		data() {
			return {
				tableList: [],
				columns: [],
				resourceName: "",
			};
		},
		watch: {
			list: {
				handler(n, o) {
					n.map((i, idx) => {
						i.idx = idx + 1;
					});
					this.tableList = n;
				},
				deep: true,
			},
			colConfigs: {
				handler(n, o) {
					this.columns = n;
				},
				deep: true,
			},
			selRow: {
				handler(n, o) {
					this.$nextTick(() => {
						this.rowClick(n);
					});
				},
				deep: true,
			},
			percent(n) {
				this.custPercent = n;
			},
			tabName(n) {
				if (n) {
					this.resourceName = n;
				}
			},
			rKey(n) {},
		},
		mounted() {
			let list = this.list;
			list.map((i, idx) => {
				i.idx = idx + 1;
			});
			this.$nextTick(() => {
				this.tableList = list ? list : [];
				this.columns = this.colConfigs ? this.colConfigs : [];
				this.custPercent = this.percent ? this.percent : null;
			});
		},
		methods: {
			/**
			 * <AUTHOR>
			 * 点击列表回调
			 */
			rowClick(row) {
				this.resourceName = row.resourceName;
				this.$emit("getSelectedRow", row.resourceName);
			},
			/**
			 * <AUTHOR>
			 * 设置选中行样式
			 */
			setRowClassName({ row }) {
				if (this.resourceName === row.resourceName) {
					// return "custRow";
					return "current-row";
				}
			},
		},
	};
</script>

<style scoped lang="less">
	.cust-table {
		min-height: 200px;
		::v-deep {
			// .el-table {
			// 	background: none;
			// 	&::before {
			// 		height: 0;
			// 		background: none;
			// 	}
			// 	tr {
			// 		background: none;
			// 	}
			// 	thead {
			// 		font-size: 14px;
			// 		font-family: "PingFangSC-Regular, PingFang SC";
			// 		// color: #E1E9F9;
			// 		height: 40px;
			// 		background-color: rgba(15, 52, 124, 0.57);
			// 		&.has-gutter {
			// 			height: 40px;
			// 			line-height: 40px;
			// 			.cell {
			// 				height: 40px;
			// 				line-height: 40px;
			// 			}
			// 		}
			// 	}
			// 	.el-table__header-wrapper {
			// 		height: 40px;
			// 		line-height: 40px;
			// 		border: 1px solid rgba(24, 78, 138, 1);
			// 	}
			// 	.el-table__body-wrapper {
			// 		overflow-y: scroll;
			// 		overflow-x: hidden;
			// 		position: relative;
			// 		max-height: 220px;
			// 		&::-webkit-scrollbar-thumb {
			// 			border-radius: 10px;
			// 			box-shadow: inset 0 0 6px transparent;
			// 			-webkit-box-shadow: inset 0 0 6px transparent;
			// 			background-color: transparent;
			// 		}
			// 		&::-webkit-scrollbar-track {
			// 			box-shadow: inset 0 0 6px transparent;
			// 			-webkit-box-shadow: inset 0 0 6px transparent;
			// 			border-radius: 10px;
			// 			background-color: transparent;
			// 		}
			// 		.el-table__row {
			// 			background: rgba(0, 0, 0, 0);
			// 		}
			.el-table__body {
				tr {
					cursor: pointer;
				}
			}
			// 				&:hover {
			// 					.el-table__cell {
			// 						color: #606266;
			// 						background: rgba(0, 0, 0, 0);
			// 					}
			// 				&.el-table__row {
			// 					td:hover {
			// 						color: #373d41;
			// 						background: rgba(0, 0, 0, 0);

			// 				}
			// 			}
			// 		}
			// 	}
			// 	.cell {
			// 		font-weight: 500;
			// 		font-size: 14px;
			// 	}
			// 	.el-table__empty-block {
			// 		min-height: 160px;
			// 	}
			// }
			// .el-table td.el-table__cell,
			// .el-table th.el-table__cell.is-leaf {
			// 	border-bottom: none;
			// 	padding: 0;
			// }
			// .el-loading-spinner {
			// 	.el-loading-text,
			// 	.path {
			// 		color: #02dbff;
			// 		stroke: #02dbff;
			// 	}
			// }

			.red {
				color: red;
			}
		}
	}
</style>
<style lang="less" scoped>
	.current-row {
		.el-table__cell {
			background: rgba(21, 100, 154, 1) !important;
			span {
				color: #e1e9f9 !important;
			}
		}
		&:hover {
			color: #e1e9f9 !important;
			background: rgba(21, 100, 154, 1) !important;
			.el-table__cell {
				background: rgba(21, 100, 154, 1) !important;
				span {
					color: #e1e9f9 !important;
				}
			}
		}
	}
	// .el-table__row. {

	// }
</style>
