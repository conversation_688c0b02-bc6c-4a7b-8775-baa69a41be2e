export default class core{
    constructor() {
    }
    checkApiIsIgnore(ignoreApis, api, method) {
        let isIgnore = false;
        for (let reg of ignoreApis) {
            const curReg = new RegExp(reg.replace(/\//g, '\\/').replace(/\:+/g, '\\'));
            if (curReg.test(`${method.toLowerCase()}@${api}`)) {
                isIgnore = true;
                break;
            }
        }
        return isIgnore;
    }
    // 获取路径
    getPathName(url) {
        const index = url.indexOf('?');
        if (index >= 0) return url.substr(0, index);
        return url;
    }
}
