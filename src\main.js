import Vue from "vue";
import VueRouter from "vue-router";
import router from "./router";
import store from "./store";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import App from "./App.vue";
// import "./assets/normalize.css";
import "./assets/css/comme.css";
import "./assets/css/nmgjpw.css";
import "./assets/js/flexible";
import "@/utils/filter";

window.nc = window.nc || {};
// 将自动注册所有组件为全局组件
import Vcomp from "./components/index";

import Storage from "vue-ls";

import api from "./api";
import clickoutside from "element-ui/src/utils/clickoutside";

window.nc = window.nc || {};

// Vue使用 Router插件
Vue.use(VueRouter);

Vue.directive("clickoutside", clickoutside);
Vue.prototype.$api = api;

var Vuels_options = {
  namespace: "pro__", // key prefix
  name: "ls", // name variable Vue.[ls] or this.[$ls],
  storage: "local", // storage name session, local, memory
};

Vue.use(Storage, Vuels_options);

window.$Socket = null;

Vue.use(Vcomp);
import echarts from "echarts";
Vue.prototype.$echarts = echarts;
Vue.config.productionTip = false;

Vue.use(ElementUI);

import scroll from "vue-seamless-scroll";
Vue.use(scroll);
import VueScroll from "@david-j/vue-j-scroll";
Vue.use(VueScroll);
//引入组件库
import jvuewheel from "@jyeontu/jvuewheel";
//引入样式
import "@jyeontu/jvuewheel/lib/jvuewhell.css";
Vue.use(jvuewheel);
import splitPane from 'vue-splitpane'
// 注册为全局组件
Vue.component('split-pane', splitPane);

var Token = Vue.ls.get("Access-Token");
window.token = Token;

window.autoTimer = 5 * 60 * 1000; // 自动轮播三分钟

const EventBus = new Vue();
Vue.prototype.$bus = EventBus;

new Vue({
  router,
  store,
  mounted() {
    Vue.ls.on("Access-Token", function (token) {
      window.token = token;
    });
    Vue.ls.on("Login_Userinfo", function (val) {
      store.commit("Set_userInfo", val);
    });
    Vue.ls.on("ROOM_ID", function (roomid) {
      store.state.ROOM_ID = roomid;
    });
  },
  render: (h) => h(App),
}).$mount("#app");
