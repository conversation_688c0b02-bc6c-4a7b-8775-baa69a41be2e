import axios from "@/utils/api.request";
const BASEURL = '/otnNpv'
const core_network_data = {
  // 告警详情
  getCoreAlarmDetail(inParams) {
    return axios.request({
      url: `${BASEURL}/monitor/getCoreAlarmDetail`,
      method: "post",
      data: inParams
    });
  },
  // 地市排名
  getCoreAlarmByCity() {
    return axios.request({
      url: `${BASEURL}/monitor/getCoreAlarmByCity`,
      method: "post",
    });
  },
  // 查询核心网数通资源--告警统计信息
  getCoreAlarmStatistics() {
    return axios.request({
      url: `${BASEURL}/monitor/getCoreAlarmStatistics`,
      method: "post",
    });
  },
  // 告警监控-传输资源-警告排名
  selectAmRank(inParams) {
    return axios.request({
      url: `${BASEURL}/monitor/selectAmRank`,
      method: "post",
      data: inParams
    });
  },
  // 告警监控-传输资源-统计信息
  selectAmStatistics(inParams) {
    return axios.request({
      url: `${BASEURL}/monitor/selectAmStatistics`,
      method: "post",
      data: inParams
    });
  },
  // 告警监控-传输资源-警告详情
  selectAmDetail(inParams) {
    return axios.request({
      url: `${BASEURL}/monitor/selectAmDetail`,
      method: "post",
      data: inParams
    });
  },
}
export default core_network_data;