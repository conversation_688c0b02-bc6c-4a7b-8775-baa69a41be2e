<template><!-- 资源管理 -->
<div class="alarm-monitor cf">
  <div class="con-top">
    <el-row :gutter="20" class="container">
      <el-col :span="4" class="muen-left">
        <div class="menu-bg">资源管理</div>
        <el-menu 
          default-active="spcDistrict" 
          class="el-menu-vertical-demo" 
          @select="handleMenuSelect" 
          unique-opened
          active-text-color="#02DBFF"
        >
          <el-submenu index="1">
            <template slot="title">
              <span>空间资源</span>
            </template>
            <el-menu-item-group>
              <el-tooltip class="item" effect="dark" :content="menu.label" placement="right" v-for="menu in spaceMenu" :key="menu.index">
                <el-menu-item :index="menu.index" >{{ menu.label }}</el-menu-item>
              </el-tooltip>
            </el-menu-item-group>
          </el-submenu>
          <el-submenu index="2">
            <template slot="title">
              <span>传输资源</span>
            </template>
            <el-menu-item-group>
              <el-tooltip class="item" effect="dark" :content="menu.label" placement="right" v-for="menu in transportMenu" :key="menu.index">
                <el-menu-item :index="menu.index" >{{ menu.label }}</el-menu-item>
              </el-tooltip>
            </el-menu-item-group>
          </el-submenu>
          <el-submenu index="3">
            <template slot="title">
              <span>核心网数通资源</span>
            </template>
            <el-menu-item-group>
              <el-tooltip class="item" effect="dark" :content="menu.label" placement="right" v-for="menu in coreNetworkDataMenu" :key="menu.index">
                <el-menu-item :index="menu.index" >{{ menu.label.length > 9 ? menu.label.slice(0, 5) + '...' : menu.label }}</el-menu-item>
              </el-tooltip>
            </el-menu-item-group>
          </el-submenu>
          <el-submenu index="4">
            <template slot="title">
              <span>核心网资源</span>
            </template>
            <el-menu-item-group>
              <el-tooltip class="item" effect="dark" :content="menu.label" placement="right" v-for="menu in coreNetwork" :key="menu.index">
                <el-menu-item :index="menu.index" >{{ menu.label }}</el-menu-item>
              </el-tooltip>
            </el-menu-item-group>
          </el-submenu>
        </el-menu>
      </el-col>
      <el-col :span="19" class="content_right">
        <div class="nc-query">
          <el-form :inline="true" :model="queryForm" :label-width="label_width+'px'" style="width: 100%;" ref="formRef1" size="small">
                <el-form-item id="loadMoreSelect" v-for="(field) in queryFields" :key="field.columnName"
                  :label="field.displayName" style="margin-top: 10px;" ref="innerInputRef">
                  <template v-if="field.type == 'string'">
                    <el-input v-model="queryForm[field.columnName]" clearable :disabled="field.columnName=='portName'&&(!queryForm['eqpName']||queryForm['eqpName']=='')"></el-input>
                    <span v-if="field.columnName=='portName'" style="color:#ff5555">查询端口之前必须填写设备名称！</span>
                  </template>
                  <template v-if="field.type == 'enum'">
                      <el-select v-model="queryForm[field.columnName]"
                        remote
                        clearable
                        filterable
                        reserve-keyword
                        :loading="personLoading"
                      >
                        <div
                          class="infinite-list" 
                          style="overflow:auto;height:calc(20vh)"
                        >
                          <el-option
                            class="infinite-list-item"
                            v-for="option in options"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          ></el-option>
                        </div>
                      </el-select>
                    </template>
                    
                </el-form-item>
                
              </el-form>
          <div style="float:right ;width:5%; margin-top: 30px; margin-right: 10px;">
            <el-button @click="resetQuery" @keyup.enter.stop.native @keyup.space.stop.native @click.stop.native size="small">重置</el-button>
          </div>
          <div style="float:right; margin-top: 30px; margin-right: 10px;">
            <el-button type="primary" @click="getList" @keyup.enter.stop.native @keyup.space.stop.native @click.stop.native size="small">查询</el-button>
          </div>
        </div>
        <div style="margin-bottom: 10px;">
          <div style="display: inline;margin:0 2px;" v-permission="'100021'">
            <el-button size="small" icon="" @click="newAdd" v-if="classId == 'cndCircuit' ||classId == 'trsCircuit'">新增</el-button>
          </div>
          <div style="display: inline;margin:0 2px;" v-permission="'100024'">
            <el-button 
              size="small" 
              @click="newAdd" 
              v-if="classId == 'cndRelation'"
            >
              新增
            </el-button>
          </div>
          <div style="display: inline; margin:0 2px;">
            <el-button size="small" icon="" @click="newAdd" v-if="classId == 'trsRouter'">新增</el-button>
          </div>
          <div style="display: inline; margin:0 2px;" v-permission="'100025'">
            <el-button 
              size="small" 
              icon="" 
              :disabled='multipleSelection.length !== 1'
              @click="newAdd(multipleSelection, 'trsCircuit')" 
              v-if="classId == 'cndCircuit' || classId == 'cndRelation' || classId == 'trsCircuit' || classId == 'trsRouter' || classId == 'cnEqp'"
            >
              修改/查看
            </el-button>
          </div>
          <!-- <div style="display: inline;margin:0 2px;" v-permission="'100026'">
            <el-button size="small" icon="" v-if=" classId == 'trsRouter'" @click="dialogImports()">导入</el-button>
          </div> -->
          <!-- <div style="display: inline; margin:0 2px;">
            <el-button size="small" icon="" v-if="classId == 'trsRouter'" @click="dialogImports()">导入</el-button>
          </div> -->
          <el-button size="small" v-if="classId == 'cndCircuit' || classId == 'trsCircuit'" :disabled='multipleSelection.length !== 1' @click="goTopo(multipleSelection)">路由拓扑</el-button>
          <el-button size="small" v-if="classId == 'cndCircuit' || classId == 'trsCircuit'" @click="dialogImport=true">导入</el-button>
          <el-button size="small" icon="el-icon-download" @click="handleTemplateExportdialog">导出</el-button>
        </div>
        <!-- 传输电路新增、编辑 -->
        <el-dialog
          class="trsCircuit router" 
          ref="elTable"
          v-if="(classId == 'trsCircuit' || classId == 'cndCircuit') && dialogAdd"
          :title="`${multipleSelection.length > 0 ? '修改' : '新增'}`" 
          :visible.sync="dialogAdd"
          :close-on-click-modal="false" 
          @close="resetForm()"
        >
        <!-- v-loading="loadingCircuit" -->
          <el-collapse 
            element-loading-text="加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            v-model="activeNames"
          >
            <el-collapse-item 
              title="基本信息" 
              name="1"
            >
            <template slot="title">
               基本信息
               <span class="el-icon-arrow-down"></span>
            </template>
              <el-form 
                :inline="true" 
                :model="pubCustForm" 
                ref="pubCustForm"
                :rules="pubCustFormRules"

              >
                <el-form-item
                    class="formItem"
                    label="业务名称"
                    prop="busName" 
                    @click="eqpDialog=true"
                >
                <!-- :disabled="custDisabled" v-if="!custDisabled"-->
                  <el-input v-if="noCust" v-model="pubCustForm.busName" placeholder="业务名称" clearable @blur="(busNameBefore =='' || busNameBefore == pubCustForm.busName) ? '' : busNameDialog=true">
                    <i slot="suffix" class="el-input__icon el-icon-search search" @click="busDialog=true"></i>
                  </el-input>
                  <el-input v-else v-model="pubCustForm.busName" placeholder="业务名称" clearable>
                  </el-input>
                </el-form-item>
                
                <el-form-item 
                  label="分组编号" 
                  prop="groupNumber" 
                >
                  <el-input v-model="pubCustForm.groupNumber" placeholder="分组编号" clearable>
                    <i v-if="pubCustForm.busName && pubCustForm.busName!==''" slot="suffix" class="el-input__icon el-icon-search search" @click="groupDialog=true"></i>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="电路名称"
                    prop="circuitName" 
                    @click="openEqpDialog(classId)"
                >
                  <el-input v-if="noCust" v-model="pubCustForm.circuitName" placeholder="电路名称"  @focus="openEqpDialog(classId)" clearable>
                    <i slot="suffix" class="el-input__icon el-icon-search search" @click="openEqpDialog(classId)"></i>
                  </el-input>
                  <el-input v-else v-model="pubCustForm.circuitName" placeholder="电路名称" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item 
                label="电路编码"
                prop="circuitCode"
                >
                  <el-input 
                    v-model="pubCustForm.circuitCode" 
                    placeholder="电路编码" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="电路ID" 
                  prop="circuitId"
                >
                  <el-input 
                    v-model="pubCustForm.circuitId" 
                    placeholder="电路ID" 
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                label="调单编号"
                  prop="adjustNum"
                >
                  <el-input 
                    v-model="pubCustForm.adjustNum" 
                    placeholder="调单编号" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="核心网设备名称"
                  prop="coreNetDeviceName" 
                >
                  <!-- <el-input 
                    v-model="pubCustForm.coreNetDeviceName" 
                    placeholder="核心网设备名称" 
                    clearable
                    :disabled="custDisabled"
                  >
                  </el-input> -->
                  <el-input v-model="pubCustForm.coreNetDeviceName" placeholder="核心网设备名称" clearable :disabled="custDisabled" @focus="equipTab('coreNetDeviceName',pubCustForm,'router','核心网专业')">
                    <i v-if="!custDisabled" slot="suffix" class="el-input__icon el-icon-search search" @click="equipTab('coreNetDeviceName',pubCustForm,'router','核心网专业')"></i>
                  </el-input>
                </el-form-item>
                <!-- <el-form-item 
                  label="核心网设备端口名称"
                  prop="coreNetDeviceName" 
                >
                  <el-input v-model="pubCustForm.coreNetDeviceName" placeholder="核心网设备端口名称" clearable :disabled="custDisabled" @focus="equipTab('coreNetDeviceName',pubCustForm,'router','核心网专业')">
                    <i v-if="!custDisabled" slot="suffix" class="el-input__icon el-icon-search search" @click="equipTab('coreNetDeviceName',pubCustForm,'router','核心网专业')"></i>
                  </el-input>
                </el-form-item> -->
                
                <el-form-item 
                  label="本端设备名称" 
                  prop="aDeviceName" 
                >
                  <!-- <el-select
                    v-model="pubCustForm.aDeviceName"
                    placeholder="本端设备名称"
                    clearable
                    :disabled="custDisabled"
                    remote
                    filterable
                    :remote-method="query => filterMethodEqp(query)"
                    @visible-change="handleIChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in aDNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select> -->
                  <el-input v-model="pubCustForm.aDeviceName" placeholder="本端设备名称" clearable :disabled="custDisabled" @focus="equipTab('A',pubCustForm,'router','核心网数通专业')">
                    <i v-if="!custDisabled" slot="suffix" class="el-input__icon el-icon-search search" @click="equipTab('A',pubCustForm,'router','核心网数通专业')"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                label="本端设备端口" 
                prop="aDevicePort" >
                  <!-- <el-select
                    v-model="pubCustForm.aDevicePort"
                    placeholder="本端设备端口"
                    clearable
                    remote
                    filterable
                    :remote-method="query => filterMethod(query)"
                    @visible-change="handleVisibleChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in aBusNetPortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select> -->
                  <el-input v-model="pubCustForm.aDevicePort" placeholder="本端设备端口" clearable>
                    <i v-if="pubCustForm.aDeviceName&&pubCustForm.aDeviceName!==''" slot="suffix" class="el-input__icon el-icon-search search" @click="portTab('A',null,pubCustForm.aDeviceName,pubCustForm)"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                label="本端设备类型"
                prop="aDeviceType" 
                >
                  <el-input 
                    v-model="pubCustForm.aDeviceType" 
                    placeholder="本端设备类型" 
                    :disabled="custDisabled"
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item 
                label="本端归属区域" 
                prop="aARreaName" 
                >
                  <el-input v-model="pubCustForm.aARreaName" placeholder="本端归属地市" clearable :disabled="custDisabled">
                    <i v-if="!custDisabled" slot="suffix" class="el-input__icon el-icon-search search" @click="regionTab('A')"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                label="本端设备所在机房" 
                prop="aDeviceRoom" 
                >
                  <el-input 
                    v-model="pubCustForm.aDeviceRoom" 
                    placeholder="本端设备所在机房" 
                    :disabled="custDisabled"
                    clearable>
                  </el-input>
                </el-form-item>
                
                <el-form-item 
                  label="对端设备名称" 
                  prop="zDeviceName" 
                >
                  <!-- <el-select
                    v-model="pubCustForm.zDeviceName"
                    placeholder="对端设备名称"
                    clearable
                    :disabled="custDisabled"
                    remote
                    filterable
                    :remote-method="query => filterMethodEqp(query)"
                    @visible-change="handleIChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in zDNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select> -->
                  <el-input v-model="pubCustForm.zDeviceName" placeholder="对端设备名称" clearable :disabled="custDisabled" @focus="equipTab('Z',pubCustForm,'router','核心网数通专业,数据网专业-路由器设备,数据网专业-智能城域网')">
                    <i v-if="!custDisabled" slot="suffix" class="el-input__icon el-icon-search search" @click="equipTab('Z',pubCustForm,'router','核心网数通专业,数据网专业-路由器设备,数据网专业-智能城域网')"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="对端设备端口" 
                  prop="zDevicePort" 
                >
                  <!-- <el-select
                    v-model="pubCustForm.zDevicePort"
                    placeholder="对端设备端口"
                    clearable
                    remote
                    filterable
                    :remote-method="query => filterMethod(query)"
                    @visible-change="handleVisibleChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in zBusNetPortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select> -->
                  <!-- <el-input 
                    v-model="pubCustForm.zDevicePort" 
                    placeholder="对端设备端口" 
                    clearable>
                  </el-input> -->
                  <el-input v-model="pubCustForm.zDevicePort" placeholder="对端设备端口" clearable>
                    <i v-if="pubCustForm.zDeviceName&&pubCustForm.zDeviceName!==''" slot="suffix" class="el-input__icon el-icon-search search" @click="portTab('Z',null,pubCustForm.zDeviceName,pubCustForm)"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="对端设备类型" 
                  
                >
                  <el-input 
                    v-model="pubCustForm.zDeviceType" 
                    placeholder="对端设备类型" 
                    :disabled="custDisabled"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                  label="对端归属区域" 
                >
                  <el-input v-model="pubCustForm.zARreaName" placeholder="对端归属地市" clearable :disabled="custDisabled">
                    <i v-if="!custDisabled" slot="suffix" class="el-input__icon el-icon-search search" @click="regionTab('Z')"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="对端设备所在机房" 
                  
                >
                  <el-input 
                    v-model="pubCustForm.zDeviceRoom" 
                    placeholder="对端设备所在机房" 
                    clearable
                    :disabled="custDisabled"
                  >
                  </el-input>
                </el-form-item>
                <!-- <el-form-item label="带宽" >
                  <el-input 
                    v-model="pubCustForm.bandwidth" 
                    placeholder="带宽" 
                    clearable>
                  </el-input>
                </el-form-item> -->
                <el-form-item 
                  label="带宽" 
                  prop="bandwidth"
                >
                  <el-select v-model="pubCustForm.bandwidth" filterable placeholder="带宽" clearable @focus="getAllCounties">
                    <el-option
                    class="infinite-list-item"
                        v-for="(option) in getCircuitRate"
                        :key="option.serialNo"
                        :label="option.circuitRate"
                        :value="option.circuitRate"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  
                  label="电路承载类型" 
                  prop="circuitCarrierType" 
                >
                  <!-- <el-input 
                    v-model="pubCustForm.circuitCarrierType" 
                    placeholder="电路承载类型"
                    :disabled="custDisabled"
                    clearable
                  >
                  </el-input> -->
                  <el-select v-model="pubCustForm.circuitCarrierType" placeholder="电路承载类型" :disabled="custDisabled">
                    <el-option
                      v-if="classId == 'trsCircuit'"
                      label="传输"
                      value="传输">
                    </el-option>
                    <el-option
                      v-else
                      label="光纤直驱"
                      value="光纤直驱">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  label="本对端设备是否同局" 
                  prop="isAzDeviceSame" 
                >
                  <!-- <el-input 
                    v-model="pubCustForm.isAzDeviceSame" 
                    placeholder="AZ端设备是否同局" 
                    clearable
                    :disabled="custDisabled"
                  >
                  </el-input> -->
                  <el-select v-model="pubCustForm.isAzDeviceSame" placeholder="本对端设备是否同局" :disabled="custDisabled">
                    <el-option
                      label="是"
                      value="是">
                    </el-option>
                    <el-option
                      label="否"
                      value="否">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  label="VPN" 
                  prop="vpn" 
                >
                  <el-input 
                    v-model="pubCustForm.vpn" 
                    placeholder="VPN" 
                    
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                  label="是否横联链路" 
                  prop="isHorizontalLink"
                >
                  <!-- <el-input 
                    v-model="pubCustForm.isHorizontalLink" 
                    placeholder="是否横联链路" 
                    clearable
                    :disabled="custDisabled"
                  >
                  </el-input> -->
                  <el-select v-model="pubCustForm.isHorizontalLink" placeholder="是否横联链路" :disabled="custDisabled">
                    <el-option
                      label="是"
                      value="是">
                    </el-option>
                    <el-option
                      label="否"
                      value="否">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  label="路径" 
                  prop="route" 
                >
                  <el-input 
                    v-model="pubCustForm.route" 
                    placeholder="路径" 
                    clearable
                    :disabled="custDisabled"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="全程路由信息">
                  <el-input 
                    v-model="pubCustForm.fullRoutInformation" 
                    placeholder="全程路由信息" 
                    clearable>
                  </el-input>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <el-collapse-item name="2" >
              <template slot="title">
               电路路由
               <span class="el-icon-arrow-down"></span>
               <el-button v-if="classId == 'trsCircuit'" style="margin:0 10px" size="small" @click.native.stop="splictRoutr(routerForm.circuitId,pubCustForm.circuitName)">拼接平台路由导入</el-button>
              <el-select v-model="specialised" size="small" placeholder="请选择" style="margin:0 10px" @change="changeSpecialised(specialised)">
                <el-option
                  v-if="classId == 'trsCircuit'"
                  label="传输路由新增"
                  value="传输路由新增">
                </el-option>
                <el-option
                  label="核心网数通路由新增"
                  value="核心网数通路由新增">
                </el-option>
                <el-option
                  label="核心网路由新增"
                  value="核心网路由新增">
                </el-option>
                <el-option
                  label="跨专业路由新增"
                  value="跨专业路由新增">
                </el-option>
              </el-select>
              <el-tooltip class="item" effect="dark" content="向后插入一行" placement="top">
                <el-button style="float:right;border: none;font-size: 32px;" icon="el-icon-circle-plus-outline" circle @click.native.stop="addList"></el-button>
              </el-tooltip> 
               <el-tooltip class="item" effect="dark" content="预览路由拓扑" placement="top">
                <el-button style="position: absolute;right: 60px;border: none;font-size: 24px;"  icon="el-icon-view" circle @click.native.stop="openDetailTopo()"></el-button>
              </el-tooltip>
              <el-button 
              class="drag-handle" 
              type="text" 
              :icon="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"
              style="position: absolute;right: 20px;font-size: 24px;" 
              @click.native.stop="toggleFullscreen()"></el-button>
              </template>
              <div ref="tableWrapper" class="table-wrapper">
                <!-- <div  v-if="detailTopo" style="width: 100%;height: 360px;">
                  <topo :sendData="tableDatas"></topo>
                </div> -->
                <el-form ref="paramsSettingForm" :model="paramsSettingForm" size="small">
                  
                 <el-table
                   :data="paramsSettingForm.tableData"
                    :row-class-name="setDynamicRowClass"
                   border
                   row-key="id"
                    :header-cell-style="{
                      color: '#9ED8FF',
                        'background-color': 'rgba(15, 52, 124, 1)',
                        'text-align': 'center',
                        'font-size': '14px',
                      }"
                    :height="routerHeight"
                    style="border: none;width: 100%"
                    @cell-dblclick="handleRowDblclick">
                    <el-table-column
                      width="100"
                      label="路由序号"
                      align="center"
                      prop="id"
                      key="col_index"
                      >
                      <template slot-scope="scope">
                       <el-form-item label="" :prop="'tableData.' + scope.$index + '.serialNo'">
                          <span>{{scope.$index + 1}}</span>
                       </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="aBelongSpec"
                      label="A端所属专业"
                      align="center"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.aBelongSpec" placement="top" v-if="scope.row.operator == currentOperator">
                      <el-form-item label="" :prop="'tableData.' + scope.$index + '.aBelongSpec'"
                      :rules="{ required: true, message: 'A端所属专业不能为空', trigger: 'blur' }"
                      v-if="scope.row.operator == currentOperator">
                        <el-select 
                        v-if="specialised == '跨专业路由新增'" 
                        v-model="scope.row.aBelongSpec" 
                        placeholder="A端所属专业" 
                        @change="changeBelongSpec(scope.row, 'a')"
                        clearable >
                          <el-option
                          v-if="classId == 'trsCircuit'"
                            label="传输专业"
                            value="传输专业">
                          </el-option>
                          <el-option
                            label="核心网专业"
                            value="核心网专业">
                          </el-option>
                          <el-option
                            label="核心网数通专业"
                            value="核心网数通专业">
                          </el-option>
                        </el-select>
                        <el-input v-else v-model="scope.row.aBelongSpec" placeholder="A端所属专业" clearable disabled></el-input>
                      </el-form-item>
                        </el-tooltip>
                      <span v-else>{{scope.row.aBelongSpec}}</span>
                     </template>
                    </el-table-column>
                    <el-table-column
                      prop="aTrsNeName"
                      label="A端设备名称"
                      align="center"
                      key="col_atrsNeName"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.aTrsNeName" placement="top" v-if="scope.row.operator == currentOperator">
                          <el-form-item label="" :prop="'tableData.' + scope.$index + '.aTrsNeName'" 
                          :rules="{ required: true, message: '设备名称不能为空', trigger: 'blur' }">
                            <el-input 
                            v-model="scope.row.aTrsNeName" placeholder="A端设备名称" clearable @focus="equipTab('A',scope.row)" @change="handleDeviceNameChange(scope.row, 'A')"></el-input>
                          </el-form-item>
                        </el-tooltip>
                      <span v-else>{{scope.row.aTrsNeName}}</span>
                     </template>
                    </el-table-column>
                    <el-table-column
                      prop="aResoureType"
                      label="A端网元类型"
                      align="center"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.aResoureType" placement="top" v-if="scope.row.operator == currentOperator">
                          <el-form-item label="" :prop="'tableData.' + scope.$index + '.aResoureType'" 
                           :rules="{ required: true, message: '网元类型不能为空', trigger: 'blur' }">
                            <!-- <el-input v-model="scope.row.aResoureType" placeholder="A端网元类型" clearable :disabled="!!scope.row.aResoureType || scope.row.aResoureType==='' "></el-input> -->
                          <el-select
                              v-model="scope.row.aResoureType"
                              :loading="scope.row.loading"
                              placeholder="A端网元类型"
                              :disabled="scope.row.isNetwork"
                              @focus="updateResourceType(scope.row, 'a')"
                            >
                              <el-option
                                v-for="type in scope.row.aNetworkElementTypes"
                                :key="type.routeTypeId"
                                :label="type.routeTypeName"
                                :value="type.routeTypeName"
                              />
                            </el-select>
                          </el-form-item>
                       </el-tooltip>
                      
                      <span v-else>{{scope.row.aResoureType}}</span>
                     </template>
                    </el-table-column>
                    
                    <el-table-column
                      prop="aPortName"
                      label="A端端口"
                      align="center"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.aPortName" placement="top" v-if="scope.row.operator == currentOperator">
                      <el-form-item label="" :prop="'tableData.' + scope.$index + '.aPortName'"
                      v-if="scope.row.operator == currentOperator">
                        <!-- <el-input v-model="scope.row.aPortName" placeholder="A端端口" clearable @focus="equipTab('A',scope.row)"></el-input> -->
                        <el-input v-model="scope.row.aPortName" placeholder="A端端口" clearable>
                          <i v-if="scope.row.aTrsNeName&&scope.row.aTrsNeName!==''" slot="suffix" class="el-input__icon el-icon-search search" @click="portTab('A','route',scope.row.aTrsNeName,scope.row)"></i>
                        </el-input>
                      </el-form-item>
                        </el-tooltip>
                      <span v-else>{{scope.row.aPortName}}</span>
                     </template>
                    </el-table-column>
                    <el-table-column
                      prop="zBelongSpec"
                      label="Z端所属专业"
                      align="center"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.zBelongSpec" placement="top" v-if="scope.row.operator == currentOperator">
                      <el-form-item label="" :prop="'tableData.' + scope.$index + '.zBelongSpec'"
                      v-if="scope.row.operator == currentOperator">
                     <el-select 
                        v-if="specialised == '跨专业路由新增'" 
                        v-model="scope.row.zBelongSpec" 
                        placeholder="Z端所属专业" 
                        clearable >
                          <el-option
                            v-if="classId == 'trsCircuit'"
                            label="传输专业"
                            value="传输专业">
                          </el-option>
                          <el-option
                            label="核心网专业"
                            value="核心网专业">
                          </el-option>
                          <el-option
                            label="核心网数通专业"
                            value="核心网数通专业">
                          </el-option>
                        </el-select>
                        <el-input v-else v-model="scope.row.zBelongSpec" placeholder="Z端所属专业" clearable disabled></el-input>
                      </el-form-item>
                        </el-tooltip>
                      <span v-else>{{scope.row.zBelongSpec}}</span>
                     </template>
                    </el-table-column>
                    
                    <el-table-column
                      prop="zTrsNeName"
                      label="Z端设备名称"
                      align="center"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                       <el-tooltip class="item" effect="dark" :content="scope.row.zTrsNeName" placement="top" v-if="scope.row.operator == currentOperator"> 
                      <el-form-item label="" :prop="'tableData.' + scope.$index + '.zTrsNeName'"
                      v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '设备名称不能为空', trigger: 'blur' }">
                        <el-input v-model="scope.row.zTrsNeName" placeholder="Z端设备名称" clearable @focus="equipTab('Z',scope.row)" @change="handleDeviceNameChange(scope.row, 'Z')"></el-input>
                      </el-form-item>
                       </el-tooltip>
                      <span v-else>{{scope.row.zTrsNeName}}</span>
                     </template>
                    </el-table-column>
                    <el-table-column
                      prop="zResoureType"
                      label="Z端网元类型"
                      align="center"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.zResoureType" placement="top" v-if="scope.row.operator == currentOperator"> 
                      <el-form-item label="" :prop="'tableData.' + scope.$index + '.zResoureType'" 
                        v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '网元类型不能为空', trigger: 'blur' }">
                        <!-- <el-input v-model="scope.row.zResoureType" placeholder="Z端网元类型" clearable :disabled="!!scope.row.zResoureType || scope.row.zResoureType==='' "></el-input> -->
                        <el-select
                          v-model="scope.row.zResoureType"
                          :loading="scope.row.loading"
                          placeholder="Z端网元类型"
                          :disabled="scope.row.isNetwork"
                          @focus="updateResourceType(scope.row, 'z')"
                        >
                          <el-option
                            v-for="type in scope.row.zNetworkElementTypes"
                            :key="type.routeTypeName"
                            :label="type.routeTypeName"
                            :value="type.routeTypeName"
                          />
                        </el-select>
                      </el-form-item>
                        </el-tooltip>
                      <span v-else>{{scope.row.zResoureType}}</span>
                     </template>
                    </el-table-column>
                    <el-table-column
                      prop="zPortName"
                      label="Z端端口"
                      align="center"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.zPortName" placement="top" v-if="scope.row.operator == currentOperator"> 
                      <el-form-item label="" :prop="'tableData.' + scope.$index + '.zPortName'"
                      v-if="scope.row.operator == currentOperator">
                        <!-- <el-input v-model="scope.row.zPortName" placeholder="Z端端口" clearable @focus="equipTab('Z',scope.row)"></el-input> -->
                        <el-input v-model="scope.row.zPortName" placeholder="Z端端口" clearable>
                          <i v-if="scope.row.zTrsNeName&&scope.row.zTrsNeName!==''" slot="suffix" class="el-input__icon el-icon-search search" @click="portTab('Z','route',scope.row.zTrsNeName,scope.row)"></i>
                        </el-input>
                      </el-form-item>
                        </el-tooltip>
                      <span v-else>{{scope.row.zPortName}}</span>
                     </template>
                    </el-table-column>
                    
                    <el-table-column
                      prop="routeIsbak"
                      label="主备"
                      align="center">
                      <template slot-scope="scope">
                        <el-tooltip class="item" effect="dark" :content="scope.row.routeIsbak" placement="top" v-if="scope.row.operator == currentOperator"> 
                          <el-form-item label="" :prop="'tableData.' + scope.$index + '.routeIsbak'"
                          :rules="{ required: true, message: '主备不能为空', trigger: 'change' }"
                          v-if="scope.row.operator == currentOperator">
                          <el-select v-model="scope.row.routeIsbak" filterable placeholder="主备" clearable>
                            <el-option class="infinite-list-item" label="主用" value="主用"></el-option>
                            <el-option class="infinite-list-item" label="备用" value="备用"></el-option>
                          </el-select>
                          </el-form-item>
                        </el-tooltip>
                      <span v-else>{{scope.row.routeIsbak}}</span>
                     </template>
                    </el-table-column>
                    <el-table-column
                      prop="operator"
                      label="录入人员"
                      align="center">
                      <template slot-scope="scope">
                      <span class="operator-cell">{{scope.row.operator}}</span>
                     </template>
                    </el-table-column>
                   <el-table-column label="操作" align="center">
                     <template slot-scope="scope" >
                      <tr 
                      v-if="scope.row.operator == currentOperator"
                        :data-id="scope.row.id"
                      >
                        <td>
                          <el-button class="drag-handle" type="text" icon="el-icon-rank" @mousedown.native.stop></el-button>
                          <!-- <el-button @click.native.prevent="editRow(scope.$index,scope.row)" type="text" icon="el-icon-edit"></el-button> -->
                          <el-button @click.native.prevent="deleteRow(scope.$index,scope.row)" type="text" icon="el-icon-delete"></el-button>
                        </td>
                      </tr> 
                      
                     </template>
                   </el-table-column>
                     </el-table>
                     
                      <el-pagination v-if="totals > 9" @size-change="sizeChange" @current-change="currentChange"
                        :current-page="page" :page-size="size" :page-sizes="pageSizes"
                        layout="total, sizes, prev, pager, next, jumper" :total="totals" style="float: right">
                      </el-pagination>
                </el-form>
              </div>
              
            </el-collapse-item>
            <el-collapse-item name="3" v-if="detailTopo">
              <template slot="title">
               电路路由预览
               <span class="el-icon-arrow-down"></span>
               <el-tooltip class="item" effect="dark" content="刷新路由拓扑" placement="top">
                <el-button style="position: absolute;right: 15px;border: none;font-size: 24px;"  icon="el-icon-refresh-right" circle @click.native.stop="componentKey++"></el-button>
              </el-tooltip>
              </template>
              <div class="table-wrapper">
                <div style="width: 100%;height: 360px;">
                  <topo :sendData="tableDatas" :key="componentKey"></topo>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetForm()">取 消</el-button>
            <el-button type="primary" @click="subForm2()">确 定</el-button>
          </div>
        </el-dialog>
        <!-- 电路新增、编辑 -->
        <el-dialog
          class="trsCircuit router" 
          ref="elTable"
          v-if="classId == 'cndCircuit'"
          :title="`${multipleSelection.length > 0 ? '修改' : '新增'}`" 
          
          :close-on-click-modal="false" 
          @close="resetForm('CirciutForm')"
        >
        <!-- :visible.sync="dialogAdd" -->
          <el-collapse 
            v-model="activeNames"
          >
            <el-collapse-item 
              title="必填项" 
              name="1"
            >
              <el-form 
                :inline="true" 
                :model="pubCustForm" 
                ref="CirciutForm"
                :rules="CirciutRules"
              >
                <el-form-item 
                  label="地市" 
                >
                  <el-input 
                    v-model="pubCustForm.region" 
                    placeholder="地市"
                    
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="核心网设备端口名称" 
                >
                  <el-input 
                    v-model="pubCustForm.coreNetDeviceName" 
                    placeholder="核心网设备端口名称" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="分组编号" 
                  
                >
                  <el-input 
                    v-model="pubCustForm.groupNumber" 
                    placeholder="分组编号" 
                    
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                  label="A端设备名称" 
                  prop="aDeviceName"
                >
                  <!-- <el-input 
                    v-model="pubCustForm.aDeviceName" 
                    placeholder="A端设备名称" 
                    clearable
                  >
                  </el-input> -->
                   <el-select
                    v-model="pubCustForm.aDeviceName"
                    placeholder="A端设备名称"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 4)"
                    
                  >
                    <el-option
                      v-for="item in aNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="A端设备端口" >
                  <!-- <el-input 
                    v-model="pubCustForm.aDevicePort" 
                    placeholder="A端设备端口" 
                    clearable
                  >
                  </el-input> -->
                  <el-select
                    v-model="pubCustForm.aDevicePort"
                    placeholder="A端设备端口"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 5)"
                    
                  >
                    <el-option
                      v-for="item in aPortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="A端设备类型">
                  <el-input 
                    v-model="pubCustForm.aDeviceType" 
                    placeholder="A端设备类型" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item label="A端核心网出局设备所在机房" >
                  <el-input 
                    v-model="pubCustForm.aDeviceRoom" 
                    placeholder="A端核心网出局设备所在机房" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  
                  label="Z端设备名称" 
                  prop="zDeviceName"
                >
                  <!-- <el-input 
                    v-model="pubCustForm.zDeviceName" 
                    placeholder="Z端设备名称"
                    
                    clearable
                  >
                  </el-input> -->
                  <el-select
                    v-model="pubCustForm.zDeviceName"
                    placeholder="Z端设备名称"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 4)"
                    
                  >
                    <el-option
                      v-for="item in aNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  
                  label="Z端设备端口" 
                  
                >
                  <!-- <el-input 
                    v-model="pubCustForm.zDevicePort" 
                    placeholder="Z端设备端口" 
                    clearable
                  >
                  </el-input> -->
                  <el-select
                    v-model="pubCustForm.zDevicePort"
                    placeholder="Z端设备端口"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 5)"
                    
                  >
                    <el-option
                      v-for="item in aPortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  label="Z端设备类型" 
                  
                >
                  <el-input 
                    v-model="pubCustForm.zDeviceType" 
                    placeholder="Z端设备类型" 
                    
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                  label="Z端设备所在机房" 
                  
                >
                  <el-input 
                    v-model="pubCustForm.zDeviceRoom" 
                    placeholder="Z端设备所在机房" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="电路名称" >
                  <el-input 
                    v-model="pubCustForm.circuitName" 
                    placeholder="电路名称" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="电路编码">
                  <el-input 
                    v-model="pubCustForm.circuitCode" 
                    placeholder="电路编码" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item label="带宽" >
                  <el-input 
                    v-model="pubCustForm.bandwidth" 
                    placeholder="带宽" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  
                  label="电路承载类型" 
                  
                >
                  <!-- <el-input 
                    v-model="pubCustForm.circuitCarrierType" 
                    placeholder="电路承载类型"
                    
                    clearable
                  >
                  </el-input> -->
                  <el-select v-model="pubCustForm.circuitCarrierType" placeholder="请选择">
                    <el-option
                      label="传输"
                      value="传输">
                    </el-option>
                    <el-option
                      label="光纤直驱"
                      value="光纤直驱">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  
                  label="AZ端设备是否同局" 
                  
                >
                  <el-input 
                    v-model="pubCustForm.isAzDeviceSame" 
                    placeholder="AZ端设备是否同局" 
                    
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="VPN" 
                  
                >
                  <el-input 
                    v-model="pubCustForm.vpn" 
                    placeholder="VPN" 
                    
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                  label="是否横联链路" 
                >
                  <el-input 
                    v-model="pubCustForm.isHorizontalLink" 
                    placeholder="是否横联链路" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="路径" >
                  <el-input 
                    v-model="pubCustForm.route" 
                    placeholder="路径" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="全程路由信息">
                  <el-input 
                    v-model="pubCustForm.fullRoutInformation" 
                    placeholder="全程路由信息" 
                    clearable>
                  </el-input>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <el-collapse-item name="2" >
              <template slot="title">
               电路路由
               <el-button style="margin:0 10px" size="small" @click.native.stop="splictRoutr(routerForm.circuitId,routerForm.circuitName)">拼接平台路由导入</el-button>
              <el-select v-model="specialised" size="small" placeholder="请选择" @change="changeSpecialised(specialised)">
                <el-option
                  label="传输路由新增"
                  value="传输路由新增">
                </el-option>
                <el-option
                  label="核心网数通路由新增"
                  value="核心网数通路由新增">
                </el-option>
                <el-option
                  label="核心网路由新增"
                  value="核心网路由新增">
                </el-option>
                <el-option
                  label="跨专业路由新增"
                  value="跨专业路由新增">
                </el-option>
              </el-select>
              <el-button 
              class="drag-handle" 
              type="text" 
              :icon="isFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"
              style="position: absolute;right: 20px;font-size: 24px;" 
              @click.native.stop="toggleFullscreen()"></el-button>
              </template>
              <div ref="tableWrapper" class="table-wrapper">
                
                <el-form ref="paramsSettingForm" :model="paramsSettingForm" size="small">
             <el-table
               :data="paramsSettingForm.tableData"
                :row-class-name="setDynamicRowClass"
               border
               row-key="id"
                :header-cell-style="{
                  color: '#9ED8FF',
                    'background-color': 'rgba(15, 52, 124, 1)',
                    'text-align': 'center',
                    'font-size': '14px',
                  }"
                :height="routerHeight"
                style="border: none;width: 100%">
                <el-table-column
                  type="index"
                  width="100"
                  label="路由序号"
                  align="center"
                  key="col_index">
                  <template slot-scope="scope">
                   <el-form-item label="" :prop="'tableData.' + scope.$index + '.serialNo'">
                      <span>{{scope.$index + 1}}</span>
                   </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="aBelongSpec"
                  label="A端所属专业"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aBelongSpec'"
                  v-if="scope.row.operator == currentOperator">
                    <el-select 
                    v-if="specialised == '跨专业路由新增'" 
                    v-model="scope.row.aBelongSpec" 
                    placeholder="A端所属专业" 
                    clearable 
                    @change="updateResourceType(scope.row, 'a')">
                      <el-option
                        label="传输专业"
                        value="传输专业">
                      </el-option>
                      <el-option
                        label="核心网专业"
                        value="核心网专业">
                      </el-option>
                      <el-option
                        label="核心网数通专业"
                        value="核心网数通专业">
                      </el-option>
                    </el-select>
                    <el-input v-else v-model="scope.row.aBelongSpec" placeholder="A端所属专业" clearable disabled></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.aBelongSpec}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="aTrsNeName"
                  label="A端设备名称"
                  align="center"
                  key="col_atrsNeName">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aTrsNeName'" 
                  v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '设备名称不能为空', trigger: 'blur' }">
                    <el-input v-model="scope.row.aTrsNeName" placeholder="A端设备名称" clearable @focus="equipTab('A',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.aTrsNeName}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="aResoureType"
                  label="A端网元类型"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aResoureType'" 
                  v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '网元类型不能为空', trigger: 'blur' }">
                    <el-input v-model="scope.row.aResoureType" placeholder="A端网元类型" clearable :disabled="!!scope.row.aResoureType || scope.row.aResoureType==='' "></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.aResoureType}}</span>
                 </template>
                </el-table-column>
                
                <el-table-column
                  prop="aPortName"
                  label="A端端口"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aPortName'"
                  v-if="scope.row.operator == currentOperator">
                    <el-input v-model="scope.row.aPortName" placeholder="A端端口" clearable @focus="equipTab('A',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.aPortName}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="zBelongSpec"
                  label="Z端所属专业"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zBelongSpec'"
                  v-if="scope.row.operator == currentOperator">
                 <el-select 
                    v-if="specialised == '跨专业路由新增'" 
                    v-model="scope.row.zBelongSpec" 
                    placeholder="Z端所属专业" 
                    clearable 
                    @change="updateResourceType(scope.row, 'z')">
                      <el-option
                        label="传输专业"
                        value="传输专业">
                      </el-option>
                      <el-option
                        label="核心网专业"
                        value="核心网专业">
                      </el-option>
                      <el-option
                        label="核心网数通专业"
                        value="核心网数通专业">
                      </el-option>
                    </el-select>
                    <el-input v-else v-model="scope.row.zBelongSpec" placeholder="Z端所属专业" clearable disabled></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.zBelongSpec}}</span>
                 </template>
                </el-table-column>
                
                <el-table-column
                  prop="zTrsNeName"
                  label="Z端设备名称"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zTrsNeName'"
                  v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '设备名称不能为空', trigger: 'blur' }">
                    <el-input v-model="scope.row.zTrsNeName" placeholder="Z端设备名称" clearable @focus="equipTab('Z',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.zTrsNeName}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="zResoureType"
                  label="Z端网元类型"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zResoureType'" 
                  v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '网元类型不能为空', trigger: 'blur' }">
                    <el-input v-model="scope.row.zResoureType" placeholder="Z端网元类型" clearable :disabled="!!scope.row.zResoureType || scope.row.zResoureType==='' "></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.zResoureType}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="zPortName"
                  label="Z端端口"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zPortName'"
                  v-if="scope.row.operator == currentOperator">
                    <el-input v-model="scope.row.zPortName" placeholder="Z端端口" clearable @focus="equipTab('Z',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.zPortName}}</span>
                 </template>
                </el-table-column>
                
                <el-table-column
                  prop="routeIsbak"
                  label="主备"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.routeIsbak'"
                  :rules="{ required: true, message: '主备不能为空', trigger: 'change' }"
                  v-if="scope.row.operator == currentOperator">
                  <el-select v-model="scope.row.routeIsbak" filterable placeholder="主备" clearable>
                    <el-option class="infinite-list-item" label="主用" value="主用"></el-option>
                    <el-option class="infinite-list-item" label="备用" value="备用"></el-option>
                  </el-select>
                  </el-form-item>
                  <span v-else>{{scope.row.routeIsbak}}</span>
                 </template>
                </el-table-column>
               <el-table-column
                  prop="operator"
                  label="录入人员"
                  align="center">
                  <template slot-scope="scope">
                  <span>{{scope.row.operator}}</span>
                 </template>
                </el-table-column>
               <el-table-column label="操作" align="center">
                 <template slot-scope="scope" >
                  <tr 
                  v-if="scope.row.operator == currentOperator"
                    :data-id="scope.row.id"
                  >
                    <td>
                      <el-button class="drag-handle" type="text" icon="el-icon-rank" @mousedown.native.stop></el-button>
                      <!-- <el-button @click.native.prevent="editRow(scope.$index,scope.row)" type="text" icon="el-icon-edit"></el-button> -->
                      <el-button @click.native.prevent="deleteRow(scope.$index)" type="text" icon="el-icon-delete"></el-button>
                    </td>
                  </tr> 
                  
                 </template>
               </el-table-column>
             </el-table>
              <el-button style="float:right" icon="el-icon-plus" circle @click="addList"></el-button>
              <el-pagination v-if="totals > 9" @size-change="sizeChange" @current-change="currentChange"
                :current-page="page" :page-size="size" :page-sizes="pageSizes"
                layout="total, sizes, prev, pager, next, jumper" :total="totals" style="float: right">
              </el-pagination>
                </el-form>
              </div>
              
            </el-collapse-item>
          </el-collapse>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetForm('CirciutForm')">取 消</el-button>
            <el-button type="primary" @click="subForm2()">确 定</el-button>
          </div>
        </el-dialog>
        <!-- 传输路由新增、编辑 -->
        <el-dialog
          class="trsCircuit router" 
          v-if="classId == 'trsRouter' && dialogAdd"
          :title="`${multipleSelection.length > 0 ? '修改' : '新增'}`" 
          :visible.sync="dialogAdd"
          :close-on-click-modal="false"
          @close="resetForm('routerForm')"
        >
          <el-collapse 
            v-model="activeNames"
          >
            <el-collapse-item 
              title="必填项" 
              name="1"
            >
              <el-form 
                :inline="true" 
                :model="routerForm" 
                :rules="rules" 
                ref="routerForm" 
              >
                <el-form-item
                    class="formItem"
                    label="电路名称"
                    prop="circuitName"
                    @click="eqpDialog=true"
                >
                  <el-input v-model="routerForm.circuitName" placeholder="电路名称" @focus="eqpDialog=true" clearable>
                    <i v-if="!custDisabled" slot="suffix" class="el-input__icon el-icon-search search" @click="eqpDialog=true"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="电路编码" 
                >
                  <el-input 
                    v-model="routerForm.circuitCode" 
                    placeholder="电路编码" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="电路ID" 
                >
                  <el-input 
                    v-model="routerForm.circuitId" 
                    placeholder="电路ID" 
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                  label="电路速率" 
                  prop="circuitRate"
                >
                  <el-select v-model="routerForm.circuitRate" filterable placeholder="电路速率" clearable @focus="getAllCounties">
                    <el-option
                    class="infinite-list-item"
                        v-for="(option) in getCircuitRate"
                        :key="option.serialNo"
                        :label="option.circuitRate"
                        :value="option.circuitRate"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="客户名称"
                    prop="custName"
                >
                  <el-input v-model="routerForm.longCustName" placeholder="客户名称" clearable @focus="custDialog=true">
                    <i slot="suffix" class="el-input__icon el-icon-search search" @click="custDialog=true"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="电路状态" 
                  prop="oprStateId"
                >
                  <el-select v-model="routerForm.oprStateId" placeholder="电路状态" @focus="getPubEnumDefinition" clearable>
                     <el-option
                        class="infinite-list-item"
                        v-for="(option) in enumData"
                        :key="option.serialNo"
                        :label="option.custState"
                        :value="option.serialNo"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="调单编号">
                  <el-input 
                    v-model="routerForm.adjustNum" 
                    placeholder="调单编号" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item label="A端归属区域" >
                  <el-input v-model="routerForm.aRegionId" placeholder="A端归属地市" clearable>
                    <i slot="suffix" class="el-input__icon el-icon-search search" @click="regionTab('A')"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="Z端归属区域" 
                >
                  <el-input v-model="routerForm.zRegionId" placeholder="Z端归属地市" clearable>
                    <i slot="suffix" class="el-input__icon el-icon-search search" @click="regionTab('Z')"></i>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="电路主备" 
                  prop="isbak"
                  >
                  <el-select v-model="routerForm.isbak" placeholder="电路主备" clearable>
                     <el-option
                        label="主用"
                        value="主用"
                    ></el-option>
                    <el-option
                        label="备用"
                        value="备用"
                    ></el-option>
                  </el-select>
                </el-form-item>
                
                <!-- <el-form-item 
                  label="拼接段序号" 
                >
                  <el-input 
                    v-model="routerForm.splicSegNum" 
                    placeholder="拼接段序号" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="A端网元ID" >
                  <el-input 
                    v-model="routerForm.aTrsNeId" 
                    placeholder="A端网元ID" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="A端网元名称">
                  <el-input 
                    v-model="routerForm.aTrsNeName" 
                    placeholder="A端网元名称" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item label="A端端口名称" >
                  <el-input 
                    v-model="routerForm.aPortName" 
                    placeholder="A端端口名称" 
                    clearable>
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="Z端网元ID" 
                >
                  <el-input 
                    v-model="routerForm.zTrsNeId" 
                    placeholder="Z端网元ID"
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="Z端网元名称" 
                >
                  <el-input 
                    v-model="routerForm.zTrsNeName" 
                    placeholder="Z端网元名称" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="Z端端口名称" 
                >
                  <el-input 
                    v-model="routerForm.zPortName" 
                    placeholder="Z端端口名称" 
                    
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item 
                  label="核心网设备名称" 
                >
                  <el-input 
                    v-model="routerForm.coreNetDeviceName" 
                    placeholder="核心网设备名称" 
                    clearable
                  >
                  </el-input>
                </el-form-item>
                <el-form-item 
                  label="A端业务网元名称" 
                  prop="aBusNetName"
                >
                  <el-select
                    v-model="routerForm.aBusNetName"
                    placeholder="A端设备名称"
                    clearable
                    remote
                    filterable
                    :remote-method="query => filterMethodEqp(query)"
                    @visible-change="handleIChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in aDNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  label="A端业务网元端口" 
                  prop="aBusNetPort"
                >
                  <el-select
                    v-model="routerForm.aBusNetPort"
                    placeholder="A端设备端口"
                    clearable
                    remote
                    filterable
                    :remote-method="query => filterMethod(query)"
                    @visible-change="handleVisibleChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in aBusNetPortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item 
                  label="Z端业务网元名称" 
                  prop="zBusNetName" 
                >
                  <el-select
                    v-model="routerForm.zBusNetName"
                    placeholder="A端设备名称"
                    clearable
                    remote
                    filterable
                    :remote-method="query => filterMethodEqp(query)"
                    @visible-change="handleIChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in aDNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  label="Z端业务网元端口" 
                  prop="zBusNetPort"
                >
                  <el-select
                    v-model="routerForm.zBusNetPort"
                    placeholder="A端设备端口"
                    clearable
                    remote
                    filterable
                    :remote-method="query => filterMethod(query)"
                    @visible-change="handleVisibleChange(true)"
                    popper-class="my-cascader-dropdown"
                  >
                    <el-option
                      v-for="item in aBusNetPortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item> -->
              </el-form>
            </el-collapse-item>
            <el-collapse-item name="2">
              <template slot="title">
               电路路由
               <el-button style="margin:0 10px" size="small" @click.native.stop="splictRoutr(routerForm.circuitId,routerForm.circuitName)">拼接平台路由导入</el-button>
              <el-select v-model="specialised" size="small" placeholder="请选择" @change="changeSpecialised(specialised)">
                <el-option
                  label="传输路由新增"
                  value="传输路由新增">
                </el-option>
                <el-option
                  label="核心网路由新增"
                  value="核心网路由新增">
                </el-option>
                <el-option
                  label="跨专业路由新增"
                  value="跨专业路由新增">
                </el-option>
              </el-select>
              </template>
              <el-form ref="paramsSettingForm" :model="paramsSettingForm" size="small">
             <el-table
               :data="paramsSettingForm.tableData"
                :row-class-name="setDynamicRowClass"
               border
               row-key="id"
                :header-cell-style="{
                  color: '#9ED8FF',
                    'background-color': 'rgba(15, 52, 124, 1)',
                    'text-align': 'center',
                    'font-size': '14px',
                  }"
                height="350"
                style="border: none;width: 100%"
                @cell-dblclick="handleRowDblclick">
                <el-table-column
                  type="index"
                  width="100"
                  label="路由序号"
                  align="center"
                  key="col_index">
                  <template slot-scope="scope">
                   <el-form-item label="" :prop="'tableData.' + scope.$index + '.serialNo'">
                      <span>{{scope.$index + 1}}</span>
                   </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="aBelongSpec"
                  label="A端所属专业"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aBelongSpec'">
                  <!-- v-if="scope.row.operator == currentOperator"> -->
                    <el-select 
                    v-if="specialised == '跨专业路由新增'" 
                    v-model="scope.row.aBelongSpec" 
                    placeholder="A端所属专业" 
                    clearable 
                    @change="updateResourceType(scope.row, 'a')">
                      <el-option
                        label="传输专业"
                        value="传输专业">
                      </el-option>
                      <el-option
                        label="核心网专业"
                        value="核心网专业">
                      </el-option>
                    </el-select>
                    <el-input v-else v-model="scope.row.aBelongSpec" placeholder="A端所属专业" clearable disabled></el-input>
                  </el-form-item>
                  <!-- <span v-else>{{scope.row.aBelongSpec}}</span> -->
                 </template>
                </el-table-column>
                <el-table-column
                  prop="aResoureType"
                  label="A端资源类型"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aResoureType'"
                  v-if="scope.row.operator == currentOperator">
                  
                    <el-select 
                      v-model="scope.row.aResoureType" 
                      :disabled="shouldDisableResourceType(scope.row, 'a')"
                    >
                      <el-option 
                        v-for="opt in getResourceTypeOptions(scope.row.aBelongSpec)" 
                        :key="opt"
                        :label="opt" 
                        :value="opt"
                      />
                    </el-select>
                  </el-form-item>
                  <span v-else>{{scope.row.aResoureType}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="aTrsNeName"
                  label="A端设备名称"
                  align="center"
                  key="col_atrsNeName">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aTrsNeName'" 
                  v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '设备名称不能为空', trigger: 'blur' }">
                  
                    <el-input v-model="scope.row.aTrsNeName" placeholder="A端设备名称" clearable @focus="equipTab('A',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.aTrsNeName}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="aPortName"
                  label="A端端口"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.aPortName'"
                  v-if="scope.row.operator == currentOperator">
                    <el-input v-model="scope.row.aPortName" placeholder="A端端口" clearable @focus="equipTab('A',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.aPortName}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="zBelongSpec"
                  label="Z端所属专业"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zBelongSpec'"
                  v-if="scope.row.operator == currentOperator">
                 <el-select 
                    v-if="specialised == '跨专业路由新增'" 
                    v-model="scope.row.zBelongSpec" 
                    placeholder="Z端所属专业" 
                    clearable 
                    @change="updateResourceType(scope.row, 'z')">
                      <el-option
                        label="传输专业"
                        value="传输专业">
                      </el-option>
                      <el-option
                        label="核心网专业"
                        value="核心网专业">
                      </el-option>
                    </el-select>
                    <el-input v-else v-model="scope.row.zBelongSpec" placeholder="Z端所属专业" clearable disabled></el-input>
                    <!-- <el-input v-model="scope.row.zBelongSpec" placeholder="Z端所属专业" clearable @focus="equipTab('A',scope.row)"></el-input> -->
                  </el-form-item>
                  <span v-else>{{scope.row.zBelongSpec}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="zResoureType"
                  label="Z端资源类型"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zResoureType'"
                  v-if="scope.row.operator == currentOperator">
                  <el-select 
                      v-model="scope.row.zResoureType" 
                      :disabled="shouldDisableResourceType(scope.row, 'z')"
                    >
                      <el-option 
                        v-for="opt in getResourceTypeOptions(scope.row.zBelongSpec)" 
                        :key="opt"
                        :label="opt" 
                        :value="opt"
                      />
                    </el-select>
                    <!-- <el-input v-model="scope.row.zResoureType" placeholder="Z端资源类型" clearable @focus="equipTab('A',scope.row)"></el-input> -->
                  </el-form-item>
                  <span v-else>{{scope.row.zResoureType}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="zTrsNeName"
                  label="Z端设备名称"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zTrsNeName'"
                  v-if="scope.row.operator == currentOperator" :rules="{ required: true, message: '设备名称不能为空', trigger: 'blur' }">
                    <el-input v-model="scope.row.zTrsNeName" placeholder="Z端设备名称" clearable @focus="equipTab('Z',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.zTrsNeName}}</span>
                 </template>
                </el-table-column>
                <el-table-column
                  prop="zPortName"
                  label="Z端端口"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.zPortName'"
                  v-if="scope.row.operator == currentOperator">
                    <el-input v-model="scope.row.zPortName" placeholder="Z端端口" clearable @focus="equipTab('Z',scope.row)"></el-input>
                  </el-form-item>
                  <span v-else>{{scope.row.zPortName}}</span>
                 </template>
                </el-table-column>
                
                <el-table-column
                  prop="routeIsbak"
                  label="主备"
                  align="center">
                  <template slot-scope="scope">
                  <el-form-item label="" :prop="'tableData.' + scope.$index + '.routeIsbak'"
                  :rules="{ required: true, message: '主备不能为空', trigger: 'change' }"
                  v-if="scope.row.operator == currentOperator">
                  <el-select v-model="scope.row.routeIsbak" filterable placeholder="主备" clearable>
                    <el-option class="infinite-list-item" label="主用" value="主用"></el-option>
                    <el-option class="infinite-list-item" label="备用" value="备用"></el-option>
                  </el-select>
                  </el-form-item>
                  <span v-else>{{scope.row.routeIsbak}}</span>
                 </template>
                </el-table-column>
               <el-table-column
                  prop="operator"
                  label="录入人员"
                  align="center">
                  <template slot-scope="scope">
                  <span>{{scope.row.operator}}</span>
                 </template>
                </el-table-column>
               <el-table-column label="操作" align="center">
                 <template slot-scope="scope" >
                  <tr 
                  v-if="scope.row.operator == currentOperator"
                    :data-id="scope.row.id"
                  >
                    <td>
                      <el-button class="drag-handle" type="text" icon="el-icon-rank" @mousedown.native.stop></el-button>
                      <el-button @click.native.prevent="deleteRow(scope.$index,scope.row)" type="text" icon="el-icon-delete"></el-button>
                    </td>
                  </tr> 
                  
                 </template>
               </el-table-column>
             </el-table>
              <el-button style="float:right" icon="el-icon-plus" circle @click="addList"></el-button>
              <el-pagination v-if="totals > 9" @size-change="sizeChange" @current-change="currentChange"
                :current-page="page" :page-size="size" :page-sizes="pageSizes"
                layout="total, sizes, prev, pager, next, jumper" :total="totals" style="float: right">
              </el-pagination>
           </el-form>
            </el-collapse-item>
          </el-collapse>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetForm('routerForm')">取 消</el-button>
            <el-button type="primary" @click="subRouterForm()">确 定</el-button>
          </div>
        </el-dialog>
        <!-- 数通网元与核心网元连接关系新增/编辑 -->
        <el-dialog
          class="trsCircuit" 
          v-if="classId == 'cndRelation'"
          :title="`${multipleSelection.length > 0 ? '修改' : '新增'}`" 
          :visible.sync="dialogAdd"
          :close-on-click-modal="false" 
          @close="resetForm()"
        >
          <el-collapse 
            v-model="activeNames"
          >
            <el-collapse-item 
              title="必填项" 
              name="1"
            >
              <el-form 
                :inline="true" 
                :model="pubCustForm" 
                ref="pubCustForm" 
              >
                <el-form-item 
                  label="核心网设备名称"
                >
                  <!-- <el-input 
                    v-model="pubCustForm.coreNetDeviceName" 
                    placeholder="核心网设备名称"
                    v-if="!isCoreNameEdit"
                  ></el-input> -->
                  <el-select
                    v-model="pubCustForm.coreNetDeviceName"
                    placeholder="核心网设备名称"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 1)"
                  >
                  <!-- v-if="isCoreNameEdit" -->
                    <el-option
                      v-for="item in coreNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                  <!-- <el-cascader 
                    v-model="pubCustForm.coreNetDeviceName" 
                    placeholder="核心网设备名称"
                    clearable
                    remote
                    filterable
                    :remote-method="query => filterMethod(query, 1)"
                    @visible-change="handleVisibleChange(true, 1)"
                    :options="coreNameOptions"
                    class="custom-cascader"
                  /> -->
                </el-form-item>
                <el-form-item 
                  label="核心网设备端口"
                  
                >
                  <!-- <el-input 
                    v-model="pubCustForm.coreNetDevicePort" 
                    placeholder="核心网设备端口"
                    v-if="!isCorePortEdit"
                  ></el-input> -->
                  <el-select
                    v-model="pubCustForm.coreNetDevicePort"
                    placeholder="核心网设备端口"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 2)"
                    
                  >
                    <el-option
                      v-for="item in corePortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item 
                  label="核心网设备IP地址" 
                >
                  <el-input 
                    v-model="pubCustForm.coreNetDeviceIp" 
                    placeholder="核心网设备IP地址"
                  ></el-input>
                  <!-- <el-select
                    v-model="pubCustForm.coreNetDeviceIp"
                    placeholder="核心网设备IP地址"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 3)"
                    
                  >
                    <el-option
                      v-for="item in coreIpOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select> -->
                </el-form-item>
                <el-form-item 
                  label="A端设备名称" 
                >
                  <!-- <el-input 
                    v-model="pubCustForm.adeviceName" 
                    placeholder="A端设备名称"
                    v-if="!isANameEdit"
                  ></el-input> -->
                  <el-select
                    v-model="pubCustForm.adeviceName"
                    placeholder="A端设备名称"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 4)"
                    
                  >
                    <el-option
                      v-for="item in aNameOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="A端设备端口" >
                  <!-- <el-input 
                    v-model="pubCustForm.adevicePort" 
                    placeholder="A端设备端口"
                    v-if="!isAPortEdit"
                  ></el-input> -->
                  <el-select
                    v-model="pubCustForm.adevicePort"
                    placeholder="A端设备端口"
                    clearable
                    filterable
                    allow-create
                    @visible-change="handleVisibleChange(true, 5)"
                    
                  >
                    <el-option
                      v-for="item in aPortOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetForm()">取 消</el-button>
            <el-button type="primary" @click="subForm2()">确 定</el-button>
          </div>
        </el-dialog>
        <!-- 网元修改 -->
        <el-dialog
          class="trsCircuit" 
          v-if="classId == 'cnEqp' && dialogAdd"
          title="修改" 
          ref="elTable"
          :visible.sync="dialogAdd"
          :close-on-click-modal="false"
          @close="resetForm('cnEqpForm')"
        >
          <el-form 
                :inline="true" 
                :model="cnEqpForm" 
                :rules="rules" 
                ref="cnEqpForm" 
              >
                <el-form-item
                    class="formItem"
                    label="设备名称"
                    prop="deviceName"
                >
                  <el-input v-model="cnEqpForm.deviceName" placeholder="设备名称" disabled clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="网元物理端口"
                    prop="netPhyPort"
                >
                  <el-input v-model="cnEqpForm.netPhyPort" placeholder="网元物理端口" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="网元业务接口"
                    prop="netBusPort"
                >
                  <el-input v-model="cnEqpForm.netBusPort" placeholder="网元业务接口" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="网元业务IP/Mask"
                    prop="netBusIp"
                >
                  <el-input v-model="cnEqpForm.netBusIp" placeholder="网元业务IP/Mask" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="交换机对应vlan"
                    prop="switchVlan"
                >
                  <el-input v-model="cnEqpForm.switchVlan" placeholder="交换机对应vlan" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="交换机L3SW名称"
                    prop="switchL3sw"
                >
                  <el-input v-model="cnEqpForm.switchL3sw" placeholder="交换机L3SW名称" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="交换机L3SW IP地址"
                    prop="switchL3swIp"
                >
                  <el-input v-model="cnEqpForm.switchL3swIp" placeholder="交换机L3SW IP地址" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="交换机L3SW端口(下联网元方向)"
                    prop="switchL3swPortDown"
                >
                  <el-input v-model="cnEqpForm.switchL3swPortDown" placeholder="交换机L3SW端口(下联网元方向)" clearable>
                  </el-input>
                </el-form-item>
                <el-form-item
                    class="formItem"
                    label="交换机L3SW端口(上联CE方向)"
                    prop="switchL3swPortCe"
                >
                  <el-input v-model="cnEqpForm.switchL3swPortCe" placeholder="交换机L3SW端口(上联CE方向)" clearable>
                  </el-input>
                </el-form-item>

                <el-form-item
                    class="formItem"
                    label="承载业务-地市"
                    prop="phyCity"
                >
                  <el-input v-model="cnEqpForm.phyCity" placeholder="承载业务-地市" clearable>
                  </el-input>
                </el-form-item>
               </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetForm('cnEqpForm')">取 消</el-button>
            <el-button type="primary" @click="subCnEqpForm()">确 定</el-button>
          </div>
        </el-dialog>
        <!-- 路由导入 -->
        <!-- <el-dialog 
          class="trsCircuit" 
          v-if="classId == 'trsRouter'" 
          title="路由导入" 
          :visible.sync="dialogRouter"
          :close-on-click-modal="false" 
          @close="resetForm"
        >
          <el-tabs 
            tab-position="left" 
            style="height: 600px;"
          >
            <el-tab-pane>
              <span slot="label" @click="clearImmediate"><el-button type="primary" icon="el-icon-tickets"
                  circle></el-button><br /> 数据导入</span>
              <el-form ref="form" :model="formImport" label-width="80px" class="formImport">
                <el-form-item label="导入文件">
                  <el-input v-model="formImport.fileName" clearable @clear="clearImport"></el-input>
                </el-form-item>
                <el-upload class="upload-demo" ref="upload" action="https://jsonplaceholder.typicode.com/posts/"
                  :on-change="handleChange" :on-remove="handleRemove" :before-upload="beforeUpload"
                  :show-file-list="false" :auto-upload="false" :limit="1" style="float: left;margin-left: 10px">
                  <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                  <el-button style="margin-left: 10px;" size="small" type="success"
                    @click="submitUpload('')">导入文件</el-button>
                  <div slot="tip" class="el-upload__tip" style="color:red">文件类型为xls、xlsx并且上传文件大小不能超过 50MB!</div>
                </el-upload>
              </el-form>
              <div class="tabTitle">问题数据列表 : </div>
              <el-table ref="multipleTables" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" border :data="resourceDataImport1" :header-cell-style="{
                color: '#9ED8FF',
                'background-color': 'rgba(15, 52, 124, 1)',
                'text-align': 'center',
                'font-size': '14px',
                'border-right': '1px rgba(15, 0, 137, .8) solid'
              }" height="470" style="border-radius: 4px 4px 0 0;
              border:none;" @selection-change="handleSelectionChange">
                <el-table-column align="center" v-for="(i, idx) in tableColumnsImport1" :key="idx"
                  :label="i.displayName" :prop="i.columnName" show-overflow-tooltip>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane>
              <span slot="label" @click="getImportRecord"><el-button type="primary" icon="el-icon-document"
                  circle></el-button><br /> 导入记录</span>
              <el-form ref="form" :model="formImport" label-width="80px">
                <el-form-item label="模板名称">
                  <el-input v-model="formImportRecords.templateName" placeholder="模板名称" clearable
                    @clear="clearImport"></el-input>
                </el-form-item>
                <el-form-item label="导入状态">
                  <el-select v-model="formImportRecords.status" filterable placeholder="客户等级" clearable>
                    <el-option class="infinite-list-item" label="全部成功" value="全部成功"></el-option>
                    <el-option class="infinite-list-item" label="全部失败" value="全部失败"></el-option>
                    <el-option class="infinite-list-item" label="部分成功" value="部分成功"></el-option>
                    <el-option class="infinite-list-item" label="校验完成" value="校验完成"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="操作时间">
                  <el-date-picker v-model="formImportRecords.operationTime" type="datetimerange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss">
                  </el-date-picker>
                </el-form-item>
                <el-form-item style="float:right;width:30%">
                  <el-button type="primary" @click="getImportRecord">查询</el-button>
                  <el-button @click="resetImport">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table ref="multipleTables" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.8)" border :data="resourceDataImport" :header-cell-style="{
                color: '#9ED8FF',
                'background-color': 'rgba(15, 52, 124, 1)',
                'text-align': 'center',
                'font-size': '14px',
                'border-right': '1px rgba(15, 0, 137, .8) solid'
              }" height="40vh" style="border-radius: 4px 4px 0 0;
              border:none; " @selection-change="handleSelectionChange"
                :default-sort="{ prop: 'createdTime', order: 'descending' }">
                <el-table-column class-name="tabSel" v-if="classId == 'pubCust' || classId == 'trsCircuit'"
                  :type="classId == 'pubCust' || classId == 'trsCircuit' ? 'selection' : 'index'" width="55"
                  align="center">
                </el-table-column>
                <el-table-column sortable align="center" v-for="(i, idx) in tableColumnsImport" :key="idx"
                  :label="i.displayName" :prop="i.columnName" show-overflow-tooltip>
                </el-table-column>
              </el-table>
              <el-pagination class="page-list" @size-change="handleSizeChangeImport"
                @current-change="handleCurrentChangeImport" :current-page.sync="formImportRecords.pageNum"
                :page-sizes="[10, 20, 30, 40, 50]" :page-size="formImportRecords.pageSize" background
                layout="total, prev, pager, next, sizes" :total.sync="formImportRecords.total"
                style="float: right;margin-top:10px">
              </el-pagination>
            </el-tab-pane>
            <el-tab-pane disabled>
              <span 
                slot="label" 
                @click="downloadModelRecord"
              ><el-button 
                type="primary" 
                icon="el-icon-download"
                circle
              /><br />模板下载</span>
            </el-tab-pane>
          </el-tabs>
        </el-dialog> -->
        <!-- 导出 -->
        <el-dialog 
          title="导出" 
          :visible.sync="dialogExport"
        >
          <el-radio-group 
            v-model="fileType"
          >
            <el-radio 
              label="xlsx"
            >
              xlsx文件
            </el-radio>
          </el-radio-group>
          <div 
            slot="footer" 
            class="dialog-footer"
          >
            <el-button 
              type="primary" 
              @click="handleTemplateExport()"
            >
              确 定
            </el-button>
          </div>
        </el-dialog>
        <!-- 列表1 -->
        <el-table 
        class="table-list"
          ref="multipleTable" 
          v-loading="loading" 
          element-loading-background="rgba(0, 0, 0, 0.8)"
          border 
          :data="resourceData" 
          :header-cell-style="{
            color: '#9ED8FF',
            'background-color': 'rgba(15, 52, 124, 1)',
            'text-align': 'center',
            'font-size': '14px',
            'border-right': '1px rgba(15, 0, 137, .8) solid'
          }" 
          :height="classId == 'cndCircuit' ? '55vh' : '60vh'"
          style="border-radius: 4px 4px 0 0;
          border:none;"
          @selection-change="handleSelectionChange"
          :cell-style="{height: '40px'}"
          v-if="classId == 'trsCircuit'"
          @cell-dblclick="handleRowDblclick"
          :cell-class-name="setOperationColumnClass"
        >
          <el-table-column 
            fixed="left"
            class-name="tabSel" 
            v-if="classId == 'trsCircuit' || classId == 'cndCircuit' || classId == 'cndRelation' || classId == 'trsRouter'"
            :type="classId == 'trsCircuit' || classId == 'cndCircuit' || classId == 'cndRelation' || classId == 'trsRouter' ? 'selection' : 'index'" 
            width="55" 
            align="center"
          >
          </el-table-column>
          <el-table-column 
            v-for="(i, idx) in tableColumns" :key="idx"
            :label="i.displayName"
            :prop="i.columnName"
            show-overflow-tooltip
            :sortable="i.columnName == 'auditStatus'?true:false"
            :width="tableColumns.length>7?'220':'auto'"
            align="center"
          >
          </el-table-column>
          <el-table-column
            v-if="roleList.indexOf('审核专员') !== -1 && classId !== 'cndCircuit'"
            fixed="right"
            label="操作"
            width="200"
            align="center"
            style="background: #000;">
            <template slot-scope="scope" >
              <el-link type="success" :disabled="scope.row.auditStatus == '通过'" @click="notPassed(scope.row,'通过')" style="margin:0 10px">通过</el-link>
              <el-link type="danger" :disabled="scope.row.auditStatus == '通过'" @click="notPassed(scope.row,'不通过')">不通过</el-link>
            </template>
          </el-table-column>
        </el-table>
        <!-- 列表2 -->
        <el-table 
          ref="multipleTable" 
          v-loading="loading" 
          element-loading-background="rgba(0, 0, 0, 0.8)"
          border
          :data="resourceData" 
          :header-cell-style="{
            color: '#9ED8FF',
            'background-color': 'rgba(15, 52, 124, 1)',
            'text-align': 'center',
            'font-size': '14px',
            'border-right': '1px rgba(15, 0, 137, .8) solid'
          }" 
          :height="classId == 'cndCircuit' ? '55vh' : '60vh'"
          style="border-radius: 4px 4px 0 0;
          border:none;"
          @selection-change="handleSelectionChange"
          :cell-style="{height: '40px'}"
          v-if="classId != 'trsCircuit'"
          class="table-class table-list"
          @cell-dblclick="handleRowDblclick"
          :cell-class-name="setOperationColumnClass"
        >
          <el-table-column 
            fixed="left"
            class-name="tabSel" 
            v-if="classId == 'trsCircuit' || classId == 'cndCircuit' || classId == 'cndRelation' || classId == 'trsRouter' || classId == 'cnEqp'"
            :type="classId == 'trsCircuit' || classId == 'cndCircuit' || classId == 'cndRelation' || classId == 'trsRouter' || classId == 'cnEqp'? 'selection' : 'index'" 
            width="55" 
            align="center"
          >
          </el-table-column>
          <el-table-column 
            v-for="(i, idx) in tableColumns" :key="idx"
            :label="i.displayName"
            :prop="i.columnName"
            show-overflow-tooltip
            :sortable="i.columnName == 'auditStatus'?true:false"
            :width="tableColumns.length>7?'220':'auto'"
            align="center"
          >
            <template slot-scope="scope" >
              <template v-if="i.columnName == 'netGrade'">
                  <el-select
                    v-model="scope.row[i.columnName]"
                    placeholder="网络层级"
                    clearable
                    remote
                    size="mini"
                    @change="handleNeNameChange(scope.row,scope.row[i.columnName])"
                  >
                    <el-option
                      label="接入网"
                      value="接入网">
                    </el-option>
                    <el-option
                      label="汇聚层"
                      value="汇聚层">
                    </el-option>
                    <el-option
                      label="核心层"
                      value="核心层">
                    </el-option>
                  </el-select>
              </template>
              <span v-else>{{scope.row[i.columnName]}}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="roleList.indexOf('审核专员') !== -1 && classId == 'cndCircuit'"
            fixed="right"
            label="操作"
            width="200"
            align="center"
            style="background: #000;">
            <template slot-scope="scope" >
              <el-link type="success" :disabled="scope.row.auditStatus == '通过'" @click="notPassed(scope.row,'通过')" style="margin:0 10px">通过</el-link>
              <el-link type="danger" :disabled="scope.row.auditStatus == '通过'" @click="notPassed(scope.row,'不通过')">不通过</el-link>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <el-pagination 
          class="page-list" 
          @size-change="handleSizeChange" 
          @current-change="handleCurrentChange"
          :current-page.sync="pageNumber" 
          :page-sizes="[10, 20, 30, 40, 50]" 
          :page-size="pageSize" background
          layout="total, prev, pager, next, sizes" 
          :total.sync="total" 
          style="float: right">
        </el-pagination>
        <!-- 电路导入 -->
        <el-dialog class="trsCircuit" v-if="classId == 'cndCircuit' || classId == 'trsCircuit'" title="导入" :visible.sync="dialogImport" :close-on-click-modal="false" @close="clearImmediate">
          <el-tabs tab-position="left" style="height: 600px;">
            <el-tab-pane>
              <span slot="label"  @click="clearImmediate"><el-button type="primary" icon="el-icon-tickets" circle></el-button><br/> 数据导入</span>
              <el-form ref="formImport" :inline="true" :model="formImport" label-width="80px" class="formImport">
                <el-form-item label="导入文件" style="float: left;margin-left: 10px">
                  <el-input v-model="formImport.fileName" clearable @clear="clearImport"></el-input>
                </el-form-item>
                <el-form-item style="margin-bottom:0px">
                <el-upload
                    class="upload-demo"
                    ref="upload"
                    action="https://jsonplaceholder.typicode.com/posts/"
                    :on-change="handleChange"
                    :on-remove="handleRemove"
                    :before-upload="beforeUpload"
                    :file-list="fileList"
                    :show-file-list="false"
                    :auto-upload="false"
                    :limit="1"
                    style="float: left;margin-left: 10px">
                    <el-button slot="trigger" size="small" type="primary" :disabled="loadingImport">选取文件</el-button>
                    <el-button style="margin-left: 10px;" size="small" type="success" :disabled="loadingImport" @click="submitUpload('')">导入</el-button>
                    <el-button size="small" type="primary" :disabled="loadingImport" @click="clearImmediate()">重置</el-button>
                    <div slot="tip" class="el-upload__tip" style="color:red">文件类型为xls、xlsx并且上传文件大小不能超过 50MB!</div>
                  </el-upload>
                </el-form-item>
              </el-form>
              
              <div class="tabTitle">问题数据列表 : </div>
              <el-table 
              ref="multipleTables"
              v-loading="loadingImport" 
              element-loading-text="导入中..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)"
              border 
              :data="resourceDataImport1" 
              :header-cell-style="{
                color: '#9ED8FF',
                  'background-color': 'rgba(15, 52, 124, 1)',
                  'text-align': 'center',
                  'font-size': '14px',
                  'border-right': '1px rgba(15, 0, 137, .8) solid'
                }"
              height="470"
              style="border-radius: 4px 4px 0 0; border:none;"
              @selection-change="handleSelectionChange">
                <el-table-column align="center" v-for="(i, idx) in tableColumnsImport1" :key="idx" :label="i.displayName" :prop="i.columnName" show-overflow-tooltip>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane disabled>
              <span slot="label" @click="downloadModelRecord"><el-button type="primary" icon="el-icon-download" circle></el-button><br/> 模板下载</span>
            </el-tab-pane>
          </el-tabs>
        </el-dialog>
        <!-- 3.0客户查询 -->
        <el-dialog class="custDialog" title="客户查询" :visible.sync="custDialog" :close-on-click-modal="false">
          <cust-tbale :custDialog="custDialog" @custFrom = "getCustFrom"/>
        </el-dialog>
        <!-- 3.0电路名称查询 -->
        <el-dialog class="custDialog" title="电路查询" :visible.sync="eqpDialog" :close-on-click-modal="false">
          <eqp-Tbale :eqpDialog="eqpDialog" :className="classId" @eqpFrom="eqpFrom" @noCustInfor="noCustInfor"/>
        </el-dialog>
        <!-- 3.0电路名称查询 -->
        <el-dialog class="custDialog" title="电路查询" :visible.sync="eqpCndDialog" :close-on-click-modal="false">
          <eqpCndTable :eqpCndDialog="eqpCndDialog" :className="classId" @eqpCndFrom="eqpCndFrom" @noCustInfor="noCustInfor1"/>
        </el-dialog>
        <!-- 3.0设备查询 -->
        <el-dialog class="equipDialog" title="设备查询" :visible.sync="equipDialog" :close-on-click-modal="false">
          <!-- <eqp-Tbale :eqpDialog="eqpDialog" @eqpFrom="eqpFrom"/> -->
          <equip-tbale :equipDialog="equipDialog" :belongSpec="belongSpecType" @equipFrom="equipFrom"/>
        </el-dialog>
        <!-- 3.0管理区域查询 -->
        <el-dialog class="custDialog" title="区域查询" :visible.sync="regionDialog" :close-on-click-modal="false">
          <region-tbale :regionDialog="regionDialog" :deviceName="deviceName" @regionFrom = "getRegionFrom"/>
        </el-dialog>
        <!-- 3.0端口查询 -->
        <el-dialog class="custDialog" title="端口查询" :visible.sync="portDialog" :close-on-click-modal="false">
          <port-tbale :portDialog="portDialog" @portFrom = "portFrom" :deviceName="deviceName"/>
        </el-dialog>
        <!-- 拼接路由 -->
        <el-dialog class="equipDialog" :title="routerForm.circuitName" :visible.sync="splictRoutrDialog" :close-on-click-modal="false">
            <el-table 
              ref="multipleTables"
              v-loading="equipLoading" 
              element-loading-text="加载中..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.8)"
              border 
              :data="splicingCircuits" 
              :header-cell-style="{
                color: '#9ED8FF',
                  'background-color': 'rgba(15, 52, 124, 1)',
                  'text-align': 'center',
                  'font-size': '14px',
                  'border-right': '1px rgba(15, 0, 137, .8) solid'
                }"
              height="530"
              style="border-radius: 4px 4px 0 0; border:none;"
              @selection-change="handleSelectionChange">
                <el-table-column type="index" width="100" label="序号"
                                 align="center" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" v-for="(i, idx) in tableSplicingCircuits" :key="idx" :label="i.displayName" :prop="i.columnName" show-overflow-tooltip>
                </el-table-column>
              </el-table>
              <div slot="footer" class="dialog-footer">
                <el-button @click="splictRoutrDialog = false">取 消</el-button>
                <el-button type="primary" @click="splicingCircuitsSum()">确 定</el-button>
              </div>
        </el-dialog>
        <!-- 业务名称查询 -->
        <el-dialog class="equipDialog" title="业务名称查询" :visible.sync="busDialog" :close-on-click-modal="false">
          <bus-Tbale :busDialog="busDialog" @busFrom = "busFrom"/>
        </el-dialog>
        <!-- 分组编码查询 -->
        <el-dialog class="equipDialog" title="分组编码查询" :visible.sync="groupDialog" :close-on-click-modal="false">
          <group-Tbale :groupDialog="groupDialog" @groupForm = "groupForm" :busName="pubCustForm.busName"/>
        </el-dialog>
        <!-- 是否改变所有的业务名称 -->
        <el-dialog
          title="提示"
          :visible.sync="busNameDialog"
          width="30%">
          <span style="color:#fff;">是否将所有的【<span style="font-size:18px;">{{busNameBefore}}</span>】<br/>改为【<span style="font-size:18px;color:greenyellow">{{pubCustForm.busName}}</span>】？</span>
          <span slot="footer" class="dialog-footer">
            <el-button @click="busNameEditFalg = 0;busNameDialog = false">否</el-button>
            <el-button type="primary" @click="busNameEditFalg = 1;busNameDialog = false">是</el-button>
          </span>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</div>
</template>

<script>
const cityOptions = ['机房名称', '机房ID', '所属区域管理', '机房类型'];
import permission from '../common/permission.js';
import Sortable from 'sortablejs';
import eqpTbale from './eqpTable.vue';
import eqpCndTable from './eqpCndTable.vue';
import busTbale from './busTable.vue';
import groupTbale from './groupTbale.vue';
import equipTbale from './equipTbale.vue';
import custTbale from './custTbale.vue';
import regionTbale from './regionTable.vue';
import portTbale from './portTable.vue';
import topo from './routerTopo.vue';
export default {
  directives: {
    permission
  },
  data() {
    var validateNull = (rule, value, callback) => {   
      console.log(value);
         
      if (!value) {
        callback(new Error('请输入内容'));
      } else {
        callback();
      }
    };
    return {
      trsCircuitBeforeJson:'',
      delList:[],
      spaceMenu:[
        {
          index:'spcDistrict',
          label:'区域',
        },
        {
          index:'spcStation',
          label:'局站',
        },
        {
          index:'spcRoom',
          label:'机房',
        },
        {
          index:'spcPosition',
          label:'放置点',
        }
      ],
      transportMenu:[
        {
          index:'trsEqp',
          label:'传输网元',
        },
        {
          index:'trsCard',
          label:'板卡',
        },
        {
          index:'trsPort',
          label:'端口',
        },
        {
          index:'trsCircuit',
          label:'传输电路',
        },
        // {
        //   index:'trsRouter',
        //   label:'传输路由',
        // },
      ],
      coreNetworkDataMenu:[
        {
          index:'cndEqp',
          label:'网元(ACE设备)',
        },
        {
          index:'cndCard',
          label:'板卡',
        },
        {
          index:'cndPort',
          label:'端口',
        },
        {
          index:'cndLink',
          label:'链路',
        },
        {
          index:'cndCircuit',
          label:'电路',
        },
        // {
        //   index:'cndRelation',
        //   label:'数通网元与核心网元连接关系',
        // }
      ],
      coreNetwork:[{
        index: 'cnEqp',
        label: '网元',
      }],
      // 遮罩层
      busNameDialog:false,
      equipLoading:false,
      loadingImport:false,
      groupDialog:false,
      busDialog:false,
      detailTopo: false,
      loadingCircuit:false,
      portDialog: false,
      custDisabled: false,
      splictRoutrDialog: false,
      eqpDialog: false,
      custDialog: false,
      equipDialog: false,
      regionDialog: false,
      eqpCndDialog: false,
      dialogImport: false,
      dialogAdd: false,
      personLoading: false,
      dialogExport: false,
      dialogRouter: false,
      loading: false,
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      tableColumns: {},//表头
      tableColumnsImport: [],
      tableColumnsImport1: [{
        displayName: '详情',
        columnName: 'errorMessageList'
      }],
      resourceData: [],
      resourceDataImport: [],
      resourceDataImport1: [],
      queryForm: {}, // 查询表单数据
      queryFields: [],
      options: [
        {label:'西安', value:'西安'},
        {label:'宝鸡', value:'宝鸡'},
        {label:'咸阳', value:'咸阳'},
        {label:'渭南', value:'渭南'},
        {label:'延安', value:'延安'},
        {label:'榆林', value:'榆林'},
        {label:'铜川', value:'铜川'},
        {label:'汉中', value:'汉中'},
        {label:'安康', value:'安康'},
        {label:'商洛', value:'商洛'}, 
      ],
      // options: [
      //   {label:'待审核', value:'待审核'},
      //   {label:'通过', value:'通过'},
      //   {label:'不通过', value:'不通过'},
      // ],
      currentPage: 1,
      checkAll: false,
      checkedCities: [],
      checkedCitiesa: [],
      cities: cityOptions,
      isIndeterminate: true,
      allOptions: [],
      tableColumnAll: [],
      count: 1, // 下拉页数
      query: '',
      queryParam: {},
      classId: 'spcDistrict',
      limit: 10,
      classIdS: '',
      fileType: 'xlsx',
      lists: [],
      className: '',
      multipleSelection: [],
      activeNames: ['1','2'],
      pubCustForm: {
        id:'',
        busName:'',
        // region:'',
        // coreNetDeviceName:'',
        // groupNumber:'',
        // adeviceName:'',
        // adevicePort:'',
        // adeviceType:'',
        // adeviceRoom:'',
        // zdeviceName:'',
        // zdevicePort:'',
        // zdeviceType:'',
        // zdeviceRoom:'',
        // circuitName:'',
        // circuitCode:'',
        // bandwidth:'',
        // circuitCarrierType:'',
        // isAzdeviceSame:'',
        // vpn:'',
        // isHorizontalLink:'',
        // route:'',
        // fullRoutInformation:'',
        // coreNetDevicePort:'',
        // coreNetDeviceIp:'',
        // aDeviceName:'',
        // aDevicePort:'',
        // aDeviceType:'',
        // aDeviceRoom:'',
        // zDeviceName:'',
        // zDevicePort:'',
        // zDeviceType:'',
        // zDeviceRoom:'',
        // isAzDeviceSame:'',
      },
      routerForm:{
        id: '',
        circuitName: '',
        circuitCode: '',
        circuitRate: '',
        longCustName: '',
        oprStateId: '',
        adjustNum: '',
        aRegionId: '',
        zRegionId: '',
        isbak: '',
        circuitId: '',
        splicSegNum: '',
        aTrsNeId: '',
        aTrsNeName: '',
        aPortName: '',
        zTrsNeId: '',
        zTrsNeName: '',
        zPortName: '',
        coreNetDeviceName: '',
        aBusNetName: '',
        aBusNetPort: '',
        zBusNetName: '',
        zBusNetPort: ''
      },
      allCountiesData: [],
      custDist: [],
      paramsSettingForm: { // 呼叫参数设置
        tableData: []
      },
      formImport: {
        fileName: ''
      },
      fileList: [],
      // 允许的文件类型
      fileTypes: ["xls", "xlsx"],
      // fileType: [ "pdf", "doc", "docx", "xls", "xlsx","txt"],
      // 运行上传文件大小，单位 M
      fileSize: 50,
      // 附件数量限制
      fileLimit: 1,
      //请求头
      headers: { "Content-Type": "multipart/form-data" },
      edit: true,
      currentRow: {},
      enumData: [],
      formImportRecords: {
        templateName: '',
        status: '',
        operationTime: ['', ''],
        pageNum: 1,
        pageSize: 10,
        total: 1,
      },
      tableSplicingCircuits: [],
      splicingCircuits: [],
      page: 1, //第几页
      size: 50, //一页多少条
      totals: 0, //总条目数
      tableDatas: [
      //   {
      //   id: 1,
      //   aBelongSpec: '传输专业',
      //   zBelongSpec: '传输专业',
      //   // detailld: '',
      //   // serialNo: '',
      //   // atrsNeId: '',
      //   // atrsNeName: '',
      //   // aportId: '',
      //   // ztrsNeId: '',
      //   // ztrsNeName: '',
      //   // zportId: '',
      //   // isbak: '',
      //   operator:JSON.parse(localStorage.getItem('user')),
      // },
      ],
      pageSizes: [10, 20, 50,100,1000], //可选择的一页多少条
      pubCustFormRules: {
        busName: 
          [{ required: true, message: '业务名称不能为空', trigger: 'blur' }],
        groupNumber: [{ required: true, message: '分组编号不能为空', trigger: 'blur' }],
        circuitName: [{ required: true, message: '电路名称不能为空', trigger: 'blur' }],
        circuitCode: [{ required: true, message: '电路编码不能为空', trigger: 'blur' }],
        aDeviceName: [{ required: true, message: '本端设备名称不能为空', trigger: 'blur' }],
        aDevicePort: [{ required: true, message: '本端设备端口不能为空', trigger: 'blur' }],
        zDeviceName: [{ required: true, message: '对端设备名称不能为空', trigger: 'blur' }],
        zDevicePort: [{ required: true, message: '对端设备端口不能为空', trigger: 'blur' }],
        bandwidth: [{ required: true, message: '带宽不能为空', trigger: 'blur' }],

        circuitCarrierType: [{ required: true, message: '电路承载类型不能为空', trigger: 'blur' }],
        isAzDeviceSame: [{ required: true, message: '本对端设备是否同局不能为空', trigger: 'blur' }],
        vpn: [{ required: true, message: 'VPN不能为空', trigger: 'blur' }],
        isHorizontalLink: [{ required: true, message: '是否横联链路不能为空', trigger: 'blur' }],
        route: [{ required: true, message: '路径不能为空', trigger: 'blur' }],

      },

      rules: {
        // circuitRate: [
        //   { validator: validateNum, trigger: 'blur' }
        // ],
        // oprStateId: [
        //   { validator: validateNum, trigger: 'blur' }
        // ],
        // isbak: [
        //   { validator: validateNum, trigger: 'blur' }
        // ],
        aBusNetName:[
          { validator: validateNull, trigger: 'change' }
        ],
        aBusNetPort:[
          { validator: validateNull, trigger: 'change' }
        ],
        zBusNetName:[
          { validator: validateNull, trigger: 'change' }
        ],
        zBusNetPort:[
          { validator: validateNull, trigger: 'change' }
        ],
      },
      CirciutRules:{
        circuitName:[
          { validator: validateNull, trigger: 'blur' }
        ],
        zDeviceName:[
          { validator: validateNull, trigger: 'blur' }
        ],
      },
      rowLimit: 3,
      label_width: null,
      coreNameOptions: [],
      corePortOptions: [],
      coreIpOptions: [],
      aNameOptions: [],
      aPortOptions: [],
      aBusNetNameOptions: [],
      aBusNetPortOptions: [],
      zBusNetNameOptions: [],
      zBusNetPortOptions: [],
      moreBtn: false,
      pageCurrent:1,
      aDNameOptions:[],
      zDNameOptions:[],
      aDPortOptions:[],
      zDPortOptions:[],
      isEdit: true,
      isCoreNameEdit:true,
      isCorePortEdit:true,
      isCoreIpEdit:true,
      isANameEdit:true,
      isAPortEdit:true,
      flag: true,
      currentOperator:JSON.parse(localStorage.getItem('user')),
      specialised:'',
      sortable:null,
      maintainAreaId:'',
      getCircuitRate:[],
      routerHeight:350,
      isFullscreen:false,
      originalOffset: 50,  // 非全屏时的偏移量
      belongSpecType: '',
      cnEqpForm:{},
      noCust: true,
      belongSpecRouterType: null,
      deviceName:'',
      loadingStates: {},
      roleList:JSON.parse(localStorage.getItem('role')),
      componentKey:0,
      busNameBefore:'',
      busNameEditFalg: 0,
    }
  },
  components: {
    equipTbale,
    eqpTbale,
    custTbale,
    regionTbale,
    portTbale,
    topo,
    busTbale,
    groupTbale,
    eqpCndTable
    },
  mounted() {
    // 添加窗口大小变化监听器
    window.addEventListener('resize', this.handleResize);
    // 初始化 label_width 和 rowLimit
    this.handleResize();
    window.addEventListener('resize', this.handleResizeForm);
    this.handleResizeForm(); // 初始化时调用一次
    if (this.$refs.formRef1) {
      this.$refs.formRef1.$el.addEventListener('keydown', (e) => {
        if (e.key == 'Enter') {
          this.getList()
        }
      })
    } else {
      console.error('formRef1 is not defined');
    }
    this.calcTableHeight();
    window.addEventListener("resize", this.handleResize);
    // 监听全屏状态变化
    document.addEventListener("fullscreenchange", this.handleFullscreenChange);
    
  },
  beforeDestroy() {
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('resize', this.handleResizeForm);
   // 组件销毁时移除监听器
   const dropdown = document.querySelector('.my-cascader-dropdown');
   if (dropdown) {
     const scrollWrap = dropdown.querySelector('.el-scrollbar__wrap');
     scrollWrap?.removeEventListener('scroll', this.handleScroll);
   }
   window.removeEventListener("resize", this.handleResize);
    document.removeEventListener("fullscreenchange", this.handleFullscreenChange);
  },
  created() {
    this.initColumn('spcDistrict');
    this.getList();
    this.changePage()
  },
  watch: {
    classId(newVal) {
      if (newVal == 'trsCircuit') {
        console.log();
        
      }
    },
    // activeNames(newVal) {
    //   newVal.length > 1 ? this.$refs.iconRef.classList.add('is-active') : this.$refs.iconRef.classList.remove('is-active')
    // },
    queryFields(newVal){
      if (newVal.length <= 3) {
        this.moreBtn = true
      }else {
        this.moreBtn = false
      }
    },
    'paramsSettingForm.tableData': {
    handler(newVal) {
      this.tableDatas.map(item => {
        newVal.map(i => {
          if (item.id && (item.id == i.id)) {
            item.aTrsNeName = i.aTrsNeName
            item.aResoureType = i.aResoureType
            item.aPortName = i.aPortName
            item.zTrsNeName = i.zTrsNeName
            item.zResoureType = i.zResoureType
            item.zPortName= i.zPortName
            item.operator=i.operator
            item.routeIsbak = i.routeIsbak
          }
        })
      });
      newVal.forEach(row => {
        // 监听 A端设备名称
        this.$watch(
          () => row.aTrsNeName,
          (newName) => {
            if (!newName) {
              row.aResoureType = ''; // 清空 A端网元类型
              row.aPortName = '';    // 清空 A端端口
            }
          }
        );

        // 监听 Z端设备名称
        this.$watch(
          () => row.zTrsNeName,
          (newName) => {
            if (!newName) {
              row.zResoureType = ''; // 清空 Z端网元类型
              row.zPortName = '';    // 清空 Z端端口
            }
          }
        );
      });
    },
    deep: true, // 深度监听
    immediate: true // 立即触发
  }
  },
  methods: {
    openEqpDialog(val){
      if (val === 'trsCircuit') {
        this.eqpDialog=true
      } else if (val === 'cndCircuit') {
        this.eqpCndDialog=true
      }
    },
    handleDeviceNameChange(row, type) {
    if (type === 'A' && !row.aTrsNeName) {
      row.aResoureType = ''; // 清空 A端网元类型
      row.aPortName = '';    // 清空 A端端口
    } else if (type === 'Z' && !row.zTrsNeName) {
      row.zResoureType = ''; // 清空 Z端网元类型
      row.zPortName = '';    // 清空 Z端端口
    }
  },
    handleResizeForm() {
    if (window.innerWidth < 768) {
      this.rowLimit = 1; // 小屏幕每行1个表单
    } else if (window.innerWidth < 1024) {
      this.rowLimit = 2; // 中等屏幕每行2个表单
    } else {
      this.rowLimit = 3; // 大屏幕每行3个表单
    }
  },
    // handleChangePort(value) {
    //   // 检查当前值是否在选项列表中
    //   const exists = this.aPortOptions.some(option => option.value === value);
    //   if (!exists) {
    //     // 如果不存在，则添加新选项
    //     this.aPortOptions.push({ value, label: value });
    //   }
    // },
    // 远程搜索下拉列表
    remoteMethod(query) {
      setTimeout(() => {
        this.options = this.lists.filter(item => {
          return item.label.toLowerCase()
            .indexOf(query.toLowerCase()) > -1;
        });
      }, 200);
      this.query = ''
    },
    async copyToClipboard(text) {
      try {
        // 方案1: 使用现代剪贴板API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(text);
          return true;
        }
        // 方案2: 使用传统execCommand作为降级方案
        else {
          const textArea = document.createElement('textarea');
          textArea.value = text;
          textArea.style.position = 'fixed';  // 避免滚动
          document.body.appendChild(textArea);
          textArea.select();
          
          const success = document.execCommand('copy');
          document.body.removeChild(textArea);
          
          if (!success) throw new Error('复制失败');
          return success;
        }
      } catch (err) {
        console.error('复制操作失败:', err);
        
        // 方案3: 显示备用复制界面
        // showFallbackCopyUI(text); // 自定义的备用复制方法
        return false;
      }
    },
    async handleRowDblclick(row, column){
      const cellData = row[column.property];
      if (cellData) {
          try {
        await this.copyToClipboard(cellData);
        this.$message({
          message: '复制成功',
          type: 'success',
        });
      } catch (error) {
        this.$message({
          message: `复制失败-${error}`,
          type: 'error',
        });
      }
      }
      
      
    },
    lazyLoadABusNetName(node, resolve) {
      if (this.aBusNetNameLoading) return;
      this.aBusNetNameLoading = true;
      const { value } = node;
      const params = {
        query: value || '',
        pageNum: this.aBusNetNamePage,
        pageSize: this.aBusNetNamePageSize,
      };
      this.$api.transApi.getTransSelectData(params)
        .then((result) => {
          const options = result.data.records.map(i => ({
            value: i.eqpId,
            label: i.eqpName,
            leaf: true, // 如果没有子节点，设置为 true
          }));
          resolve(options);
          this.aBusNetNameTotal = result.data.total;
          this.aBusNetNameLoading = false;
        })
        .catch(() => {
          this.aBusNetNameLoading = false;
        });
    },

    // 处理分页逻辑
    handleLoadMoreABusNetName() {
      if (this.aBusNetNameLoading || this.aBusNetNamePage * this.aBusNetNamePageSize >= this.aBusNetNameTotal) return;
      this.aBusNetNamePage += 1;
      this.lazyLoadABusNetName({ value: this.routerForm.aBusNetName, level: 1 }, (options) => {
        this.aBusNetNameOptions = this.aBusNetNameOptions.concat(options);
      });
    },
    showInput(){
      this.activeNames.length > 1 ? this.activeNames=['1'] : this.activeNames=['1', '2']
    },
    handleVisibleChange(visible,query) {
      this.pageCurrent = 1
      if (visible) {
        query ? this.filterMethod('', query) : this.filterMethod( ); // 调用 filterMethod 方法
      }
      if (visible) {
        this.$nextTick(() => {
          const dropdown = document.querySelector('.my-cascader-dropdown');
          if (dropdown) {
            const scrollWrap = dropdown.querySelector('.el-scrollbar__wrap');
            if (scrollWrap) {
              // 先移除旧的监听器避免重复绑定
              scrollWrap.removeEventListener('scroll', this.handleScroll);
              // 绑定正确的监听器
              scrollWrap.addEventListener('scroll', this.handleScroll);
            }
          }
        });
      } else {
      // 下拉框关闭时移除监听器
        const dropdown = document.querySelector('.my-cascader-dropdown');
        if (dropdown) {
          const scrollWrap = dropdown.querySelector('.el-scrollbar__wrap');
          scrollWrap?.removeEventListener('scroll', this.handleScroll);
        }
      }
    },
    handleIChange(visible){
      this.pageCurrent = 1
      if (visible) {
        this.filterMethodEqp()
      }
      if (visible) {
        this.$nextTick(() => {
          const dropdown = document.querySelector('.my-cascader-dropdown');
          if (dropdown) {
            const scrollWrap = dropdown.querySelector('.el-scrollbar__wrap');
            if (scrollWrap) {
              // 先移除旧的监听器避免重复绑定
              scrollWrap.removeEventListener('scroll', this.handleScroll);
              // 绑定正确的监听器
              scrollWrap.addEventListener('scroll', this.handleScroll);
            }
          }
        });
      } else {
      // 下拉框关闭时移除监听器
        const dropdown = document.querySelector('.my-cascader-dropdown');
        if (dropdown) {
          const scrollWrap = dropdown.querySelector('.el-scrollbar__wrap');
          scrollWrap?.removeEventListener('scroll', this.handleScroll);
        }
      }
    },
    filterMethodEqp(query){
      // console.log(query);
      
      this.personLoading = true;
      const params = {
        eqpId: "",
        eqpName: query,
        portId: "",
        portName: "",
        pageNum: this.pageCurrent,
        pageSize: 10
      };
      this.$api.transApi.getEqpTransSelectData(params)
      .then((result) => {
        this.aDNameOptions = [...result.data.records.map(i=>{
          return {label: i.eqpName, value: i.eqpName}
        })]
        this.zDNameOptions = [...result.data.records.map(i => {
          return {label: i.eqpName, value: i.eqpName}
        })]
        this.personLoading = false;
      });
    },
    handleScroll(event) {
      const { scrollTop, scrollHeight, clientHeight } = event.target;
      const threshold = 1; // 触底阈值
      // 检查是否滚动到底部
      if (scrollHeight - (scrollTop + clientHeight) < threshold) {
        this.pageCurrent+=1
        this.filterMethod()
      }
    },
    filterMethod(query, columnType) {
      if (columnType < 3) {
        this.personLoading = true;
        const params = {
          coreNetDeviceName: this.pubCustForm.coreNetDeviceName ? this.pubCustForm.coreNetDeviceName : "",
          coreNetDevicePort: this.pubCustForm.coreNetDevicePort ? this.pubCustForm.coreNetDevicePort : "",
          // deviceNameA: this.pubCustForm.aDeviceName ? this.pubCustForm.aDeviceName : "",
          // devicePortA: this.pubCustForm.aDevicePort ? this.pubCustForm.aDevicePort : "",
          columnType: columnType
        };
        this.$api.coreApi.preAddTDwdCircuitNet(params)
        .then((result) => {
          switch (columnType) {
            case 1:
              if (result.data.length > 0 || this.pubCustForm.coreNetDeviceName) {
                this.coreNameOptions = result.data.map(i=>{
                  return {value: i, label: i}
                });
                this.isCoreNameEdit = true;
              } else {
                this.isCoreNameEdit = false;
              }
                          
              break;
            case 2:
              if (result.data.length > 0 || this.pubCustForm.coreNetDevicePort) {
                this.corePortOptions = result.data.map(i=>{
                  return {value: i, label: i}
                });
                this.isCorePortEdit = true;
              } else {
                this.isCorePortEdit = false;
              }        
              break;
            default:
              break;
          }
          this.personLoading = false;
        });
      } else if (columnType >= 3) {
        this.personLoading = true;
        const params = {
          // coreNetDeviceName: this.pubCustForm.coreNetDeviceName ? this.pubCustForm.coreNetDeviceName : "",
          // coreNetDevicePort: this.pubCustForm.coreNetDevicePort ? this.pubCustForm.coreNetDevicePort : "",
          deviceNameA: this.pubCustForm.adeviceName ? this.pubCustForm.adeviceName : "",
          devicePortA: this.pubCustForm.adevicePort ? this.pubCustForm.adevicePort : "",
          columnType: columnType
        };
        this.$api.coreApi.preAddTDwdCircuitNet(params)
        .then((result) => {
          switch (columnType) {
            case 3:
              if (result.data.length > 0 || this.pubCustForm.coreNetDeviceIp) {
                this.coreIpOptions = result.data.map(i=>{
                  return {value: i, label: i}
                });
                this.isCoreIpEdit = true;
              } else {
                this.isCoreIpEdit = false;
              }        
              break;
            case 4:
              if (result.data.length > 0 || this.pubCustForm.adeviceName) {
                this.aNameOptions = result.data.map(i=>{
                  return {value: i, label: i}
                });
                this.isANameEdit = true;
              } else {
                this.isANameEdit = false;
              }         
              break;
            case 5:
              if (result.data.length > 0 || this.pubCustForm.adevicePort) {
                this.aPortOptions = result.data.map(i=>{
                  return {value: i, label: i}
                });
                this.isAPortEdit = true;
              } else {
                this.isAPortEdit = false;
              }
              break;
            default:
              break;
          }
          this.personLoading = false;
        });
      }else{
        this.personLoading = true;
        const params = {
          eqpId: "",
          eqpName: "",
          portId: "",
          portName: query,
          pageNum: this.pageCurrent,
          pageSize: 10
        };
        this.$api.transApi.getPortTransSelectData(params)
        .then((result) => {
          this.aBusNetPortOptions = [...result.data.records.map(i => {
            return {label: i.portName, value: i.portName}
          })]
          this.zBusNetPortOptions = [...result.data.records.map(i => {
            return {label: i.portName, value: i.portName}
          })]
          this.personLoading = false;
        });
      }
    },
    handleResize() {
      // this.rowLimit = Math.floor(this.$refs.collapse.$el.clientWidth / this.$refs.inputRef[0].$el.clientWidth);
    },
    changePage() {
      if (this.tableDatas.length<10) {
        this.paramsSettingForm.tableData = this.tableDatas
      }else {
        this.paramsSettingForm.tableData = this.tableDatas.slice(
          (this.page - 1) * this.size,
          this.page * this.size
        ).map((item) => ({
          ...item,
          // id: index+1
          // || (this.page - 1) * this.size + index + 1 // 确保每项有唯一 id
        }));
      }
      
      this.totals = this.tableDatas.length
    },
    // 电路导入
    dialogImports() {
      // if (this.classId == 'trsCircuit') {
      //   this.dialogImport = true;
      // }else if (this.classId == 'trsRouter') {
      //   this.dialogRouter = true;
      // }
    },
    clearImmediate() {
      this.fileList = [];
      this.$set(this.formImport, 'fileName', '');
      this.resourceDataImport1 = []
    },
    submitUpload(val) {
      if (this.fileList.length != 0) {
        this.loadingImport = true;
        let FormDatas = new FormData();
        FormDatas.append('file', this.fileList[0].raw);
        FormDatas.append('ImportType', val);
        this.$api.transApi.circuitRouteImport(FormDatas, this.headers).then(res => {
          if (res.resultCode == 200) {
            this.$message({
              type: 'success',
              message: '导入成功'
            });
            this.dialogImport = false;
            this.getList();
            this.loadingImport = false;
          } else {
            this.$message({
              type: 'error',
              message: '导入失败'
            });
            this.resourceDataImport1 = res.message.split("];").map((i) => {
              return { errorMessageList: i }
            })
            this.loadingImport = false;
          }
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: '导入失败'
          });
          this.loadingImport = false;
        });
      }
    },
    //上传文件之前
    beforeUpload(file) {
      console.log(this.formImport.fileName,file);
      if (file.type != "" || file.type != null || file.type != undefined) {
        //截取文件的后缀，判断文件类型
        const FileExt = file.name.replace(/.+\./, "").toLowerCase();
        //计算文件的大小
        const isLt5M = file.size / 1024 / 1024 < 50; //这里做文件大小限制
        //如果大于50M
        if (!isLt5M) {
          this.$message({
            message: '上传文件大小不能超过 50MB!',
            type: 'warning'
          });
          return false;
        }
        //如果文件类型不在允许上传的范围内
        if (this.fileType.includes(FileExt)) {
          return true;
        }
        else {
          this.$message.error("上传文件格式不正确!");
          return false;
        }
      }
    },
    // 路由模板下载
    downloadModelRecord() {
      const data = {
        fileName: this.classId == 'trsCircuit'?'电路新增导入模板（传输电路）.xlsx':'电路新增导入模板（电路）.xlsx'
      };
      const formData = new FormData();
      for (const key in data) {
        if (data.hasOwnProperty(key)) {
          formData.append(key, data[key]);
        }
      }
      this.$api.transApi.downloadModel(formData)
      .then(res => {          
          if (res.size) {
            // 文件下载
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
          });
          let link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.setAttribute('download',  this.classId == 'trsCircuit'?'电路新增导入模板（传输电路）.xlsx':'电路新增导入模板（电路）.xlsx');
          link.click();
          link = null;
          this.$message({
            message: '下载成功',
            type: 'success',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
          } else {
            this.$message({
              message: '下载失败',
              type: 'error',
              duration: 2000,
              showClose: true
            });
            this.dialogExport = false;
            this.loading = false;
          }
        })
        .catch(() => {
          this.$message({
            message: '下载失败',
            type: 'error',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
        });
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handleChange(file, fileList) {
      this.formImport.fileName = file.name
      this.fileList = fileList
    },
    clearImport() {
      this.fileList = [];
      this.resourceDataImport1 = [];
      this.$refs.upload.clearFiles();
    },
    handleMenuSelect(index) { 
      this.total = 0     
      this.handleResize()
      // this.rowLimit = Math.floor(this.$refs.collapse.$el.clientWidth/this.$refs.inputRef[0].$el.clientWidth)-1;
      this.classId = index;
      this.initColumn(this.classId);
      this.getList();
      this.$nextTick(() => {
        if (this.$refs.innerInputRef && this.$refs.innerInputRef.length > 0) {
          // const combinedRefs = [...this.$refs.innerInputRef, ...this.$refs.inputRef];
          // const longestItem = combinedRefs.reduce((acc, item) => {
          //   const currentLength = item.$el.outerText.length;
          //   if (currentLength > acc.length) {
          //     return { item, length: currentLength };
          //   }
          //   return acc;
          // }, { item: null, length: 0 });
          // this.label_width = longestItem.item.$el.outerText.length * 20
          this.label_width = 120

        }
      });
    },
    initColumn(classId) {      
      this.queryForm = {};
      this.pageSize = 10;
      this.pageNumber = 1;
      const jsonData = require('../../assets/json/resource/' + classId + '.json');
      this.tableColumnAll = jsonData.tableConfig.columns;
      this.queryFields = jsonData.queryConfig.columns;
      this.className = jsonData.queryConfig.className;
      
      let options = [];
      jsonData.tableConfig.columns.map((i) => {
        options.push(i.columnName);
      });
      this.allOptions = options;
      this.cities = jsonData.tableConfig.columns;
      this.checkedCities = jsonData.tableConfig.selectColumns;
      this.checkedCitiesa = jsonData.tableConfig.selectColumns;
    },
    /**
     * <AUTHOR>
     * 根据 type 获取详情列表
     */
    gettableColumn(key, queryFields) {
      const foundItem = queryFields.find(i => i.columnName === key);
      return foundItem ? foundItem.tableColumnName : null;
    },
    gettableType(key, queryFields) {
      const foundItem = queryFields.find(i => i.columnName === key);
      return foundItem ? foundItem.type : null;
    },

    transformData(data) {
      let transformedData = { where: {} };
      data.forEach(item => {
        for (let key in item) {
          let value = item[key]; // 假设统一使用$like作为条件 
          transformedData.where[key] = value;
        }
      });
      return transformedData;
    },

    getList() {
      let scope = this;
      this.loading = true;
      this.checkedCitiesa = this.checkedCities;
      const filteredObjs = scope.tableColumnAll.filter((obj) => scope.checkedCities.includes(obj.columnName));      
      this.tableColumns = filteredObjs;
      let queryParam = {};//后续封装查询条件
      let options = {
        current: this.pageNumber,
        limit: this.pageSize,
        isQueryCount: true,
      }
      let arr = []
      Object.keys(scope.queryForm).forEach(key => {
        arr.push({
          type: scope.gettableType(key, scope.queryFields),
          talcolumn: scope.gettableColumn(key, scope.queryFields),
          value: scope.queryForm[key]
        })
      })
      let obj = arr.map((i) => {
        if (i.value) {
          if (i.type == 'enum') {
            return { [i.talcolumn]: { $eq: i.value } }
          } else {
            return { [i.talcolumn]: { $like: i.value } }
          }
        }
      })
      queryParam = this.transformData(obj).where
      if(this.flag){
        if (Object.keys(queryParam).length !== 0) {
          options.where = queryParam;
          this.pageNumber = 1;
          this.flag = false
        }
      }
      if (Object.keys(queryParam).length == 0) {
          this.flag = true
        }
      this.doAxios(queryParam)
      .then((result) => {
        if (result.resultCode == 200) {
          let res = result.data;
          this.total = res.total ? res.total : 0;
          this.pageNumber = res.current ? res.current : 1;
          if (res.records && res.records.length > 0) {
            this.$nextTick(() => {
              this.resourceData = res.records;
              this.$refs.multipleTable.doLayout();
            });
            this.loading = false;
          } else {
            this.resourceData = [];
            this.loading = false;
          }  
        } else {
          this.resourceData = [];
          this.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: `${result.code}  ${result.msg}`,
          });
        }
      })
      .catch((err) => {
        console.warn(err);
        this.resourceData = [];
        this.loading = false;
      })
    },
    doAxios(params){
      
      if (this.classId == "cndEqp") {
        this.options = [
        {label:'西安', value:'西安'},
        {label:'宝鸡', value:'宝鸡'},
        {label:'咸阳', value:'咸阳'},
        {label:'渭南', value:'渭南'},
        {label:'延安', value:'延安'},
        {label:'榆林', value:'榆林'},
        {label:'铜川', value:'铜川'},
        {label:'汉中', value:'汉中'},
        {label:'安康', value:'安康'},
        {label:'商洛', value:'商洛'}, 
      ];
        return this.$api.coreApi.getAceDevice({
          neName: params.neName ? params.neName.$like : "",
          neId: params.neId ? params.neId.$like : "",
          neIp: params.neIp ? params.neIp.$like : "",
          city: params.city ? params.city.$eq : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "cndCard") {
        return this.$api.coreApi.getAceCard({
          deviceip: params.deviceip ? params.deviceip.$like : "",
          boardname: params.boardname ? params.boardname.$like : "",
          id: params.id ? params.id.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "cndPort") {
        return this.$api.coreApi.getAcePort({
          portname: params.portname ? params.portname.$like : "",
          id: params.id ? params.id.$like : "",
          deviceid: params.deviceid ? params.deviceid.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
          // 所属板卡ID、端口状态
        })
      } else if (this.classId == "cndLink") {
        return this.$api.coreApi.getAceCircuit({
          id: params.id ? params.id.$like : "",
          linktype: params.linktype ? params.linktype.$like : "",
          adeviceip: params.adeviceip ? params.adeviceip.$like : "",
          adeviceid: params.adeviceid ? params.adeviceid.$like : "",
          aportname: params.aportname ? params.aportname.$like : "",
          zdeviceip: params.zdeviceip ? params.zdeviceip.$like : "",
          zdeviceid: params.zdeviceid ? params.zdeviceid.$like : "",
          zportname: params.zportname ? params.zportname.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "cndCircuit") {
        this.options = [
        {label:'待审核', value:'待审核'},
        {label:'通过', value:'通过'},
        {label:'不通过', value:'不通过'},
      ];
        return this.$api.transApi.getTransCircuit({
          region: params.region ? params.region.$eq : "",
          circuitName: params.circuitName ? params.circuitName.$like : "",
          circuitCode: params.circuitCode ? params.circuitCode.$like : "",
          aDeviceName: params.aDeviceName ? params.aDeviceName.$like : "",
          aDevicePort: params.aDevicePort ? params.aDevicePort.$like : "",
          aDeviceType: params.aDeviceType ? params.aDeviceType.$like : "",
          zDeviceName: params.zDeviceName ? params.zDeviceName.$like : "",
          zDevicePort: params.zDevicePort ? params.zDevicePort.$like : "",
          zDeviceType: params.zDeviceType ? params.zDeviceType.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber,
          circuitCarrierType: '光纤直驱',
          auditStatus: params.auditStatus ? params.auditStatus.$eq : ""
        })
      } else if (this.classId == "cndRelation") {
        return this.$api.coreApi.getTDwdCircuitNet({
          coreNetDeviceName: params.coreNetDeviceName ? params.coreNetDeviceName.$like : "",
          coreNetDeviceIp: params.coreNetDeviceIp ? params.coreNetDeviceIp.$like : "",
          adeviceName: params.adeviceName ? params.adeviceName.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "cnEqp") {
        return this.$api.coreApi.getTDwdCoreNet({
          deviceName: params.deviceName ? params.deviceName.$like : "",
          deviceCode: params.deviceCode ? params.deviceCode.$like : "",
          deviceRoom: params.deviceRoom ? params.deviceRoom.$like : "",
          area: params.area ? params.area.$like : "",
          deviceType: params.deviceType ? params.deviceType.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "trsCircuit") {
        this.options = [
        {label:'待审核', value:'待审核'},
        {label:'通过', value:'通过'},
        {label:'不通过', value:'不通过'},
      ];
        return this.$api.transApi.getTransCircuit({
          id: params.id ? params.id.$like : "",
          province: params.province ? params.province.$like : "",
          region: params.region ? params.region.$eq : "",
          coreNetDeviceName: params.coreNetDeviceName ? params.coreNetDeviceName.$like : "",
          groupNumber: params.groupNumber ? params.groupNumber.$like : "",
          aDeviceName: params.aDeviceName ? params.aDeviceName.$like : "",
          aDevicePort: params.aDevicePort ? params.aDevicePort.$like : "",
          aDeviceType: params.aDeviceType ? params.aDeviceType.$like : "",
          aDeviceRoom: params.aDeviceRoom ? params.aDeviceRoom.$like : "",
          zDeviceName: params.zDeviceName ? params.zDeviceName.$like : "",
          zDevicePort: params.zDevicePort ? params.zDevicePort.$like : "",
          zDeviceType: params.zDeviceType ? params.zDeviceType.$like : "",
          zDeviceRoom: params.zDeviceRoom ? params.zDeviceRoom.$like : "",
          circuitName: params.circuitName ? params.circuitName.$like : "",
          circuitCode: params.circuitCode ? params.circuitCode.$like : "",
          bandwidth: params.bandwidth ? params.bandwidth.$like : "",
          // circuitCarrierType: params.circuitCarrierType ? params.circuitCarrierType.$like : "",
          isAzDeviceSame: params.isAzDeviceSame ? params.isAzDeviceSame.$like : "",
          vpn: params.vpn ? params.vpn.$like : "",
          isHorizontalLink: params.isHorizontalLink ? params.isHorizontalLink.$like : "",
          route: params.route ? params.route.$like : "",
          fullRoutInformation: params.fullRoutInformation ? params.fullRoutInformation.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber,
          circuitCarrierType: '传输',
          auditStatus: params.auditStatus ? params.auditStatus.$eq : ""

        })
      } else if (this.classId == "trsPort") {
        return this.$api.transApi.getTDwdPort({
          portName: params.portName ? params.portName.$like : "",
          cardName: params.cardName ? params.cardName.$like : "",
          eqpName: params.eqpName ? params.eqpName.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "trsRouter") {
        return this.$api.transApi.getTransRoute({
          id: params.id ? params.id.$like : "",
          circuitName: params.circuitName ? params.circuitName.$like : "",
          circuitCode: params.circuitCode ? params.circuitCode.$like : "",
          circuitRate: params.circuitRate ? params.circuitRate.$like : "",
          longCustName: params.longCustName ? params.longCustName.$like : "",
          oprStateId: params.oprStateId ? params.oprStateId.$like : "",
          adjustNum: params.adjustNum ? params.adjustNum.$like : "",
          aRegionId: params.aRegionId ? params.aRegionId.$like : "",
          zRegionId: params.zRegionId ? params.zRegionId.$like : "",
          isbak: params.isbak ? params.isbak.$like : "",
          circuitId: params.circuitId ? params.circuitId.$like : "",
          splicSegNum: params.splicSegNum ? params.splicSegNum.$like : "",
          aTrsNeId: params.aTrsNeId ? params.aTrsNeId.$like : "",
          aTrsNeName: params.aTrsNeName ? params.aTrsNeName.$like : "",
          aPortName: params.aPortName ? params.aPortName.$like : "",
          zTrsNeId: params.zTrsNeId ? params.zTrsNeId.$like : "",
          zTrsNeName: params.zTrsNeName ? params.zTrsNeName.$like : "",
          zPortName: params.zPortName ? params.zPortName.$like : "",
          coreNetDeviceName: params.coreNetDeviceName ? params.coreNetDeviceName.$like : "",
          aBusNetName: params.aBusNetName ? params.aBusNetName.$like : "",
          aBusNetPort: params.aBusNetPort ? params.aBusNetPort.$like : "",
          zBusNetName: params.zBusNetName ? params.zBusNetName.$like : "",
          zBusNetPort: params.zBusNetPort ? params.zBusNetPort.$like : "",
          createOp: params.createOp ? params.createOp.$like : "",
          createDate: params.createDate ? params.createDate.$like : "",
          modifyOp: params.modifyOp ? params.modifyOp.$like : "",
          modifyDate: params.modifyDate ? params.modifyDate.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "trsEqp") {
        return this.$api.transApi.getTransNet({
          id: params.id ? params.id.$like : "",
          eqpName: params.eqpName ? params.eqpName.$like : "",
          districtName: params.districtName ? params.districtName.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "trsCard") {
        return this.$api.transApi.getBoardCard({
          id: params.id ? params.id.$like : "",
          cardName: params.cardName ? params.cardName.$like : "",
          eqpName: params.eqpName ? params.eqpName.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "spcDistrict") {
        return this.$api.spaceApi.getDistrict({
          districName: params.districName ? params.districName.$like : "",
          disLevel: params.disLevel ? params.disLevel.$like : "",
          supDisName: params.supDisName ? params.supDisName.$like : "",
          district: params.district ? params.district.$like : "",
          districtId: params.districtId ? params.districtId.$like : "",
          resType: params.resType ? params.resType.$like : "",
          createOp: params.createOp ? params.createOp.$like : "",
          createDate: params.createDate ? params.createDate.$like : "",
          modifyOp: params.modifyOp ? params.modifyOp.$like : "",
          modifyDate: params.modifyDate ? params.modifyDate.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "spcStation") {
        return this.$api.spaceApi.getStation({
          deviceName: params.deviceName ? params.deviceName.$like : "",
          chinaName: params.chinaName ? params.chinaName.$like : "",
          districName: params.districName ? params.districName.$like : "",
          type: params.type ? params.type.$like : "",
          propChar: params.propChar ? params.propChar.$like : "",
          property: params.property ? params.property.$like : "",
          location: params.location ? params.location.$like : "",
          grade: params.grade ? params.grade.$like : "",
          isShare: params.isShare ? params.isShare.$like : "",
          shareUnit: params.shareUnit ? params.shareUnit.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "spcRoom") {
        return this.$api.spaceApi.getRoom({
          deviceName: params.deviceName ? params.deviceName.$like : "",
          chinaName: params.chinaName ? params.chinaName.$like : "",
          regionName: params.regionName ? params.regionName.$like : "",
          stationId: params.stationId ? params.stationId.$like : "",
          type: params.type ? params.type.$like : "",
          clazz: params.clazz ? params.clazz.$like : "",
          propChar: params.propChar ? params.propChar.$like : "",
          stationGrade: params.stationGrade ? params.stationGrade.$like : "",
          isShare: params.isShare ? params.isShare.$like : "",
          shareUnit: params.shareUnit ? params.shareUnit.$like : "",
          propertyBelong: params.propertyBelong ? params.propertyBelong.$like : "",
          speciality: params.speciality ? params.speciality.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      } else if (this.classId == "spcPosition") {
        return this.$api.spaceApi.getPosition({
          deviceName: params.deviceName ? params.deviceName.$like : "",
          chinaName: params.chinaName ? params.chinaName.$like : "",
          regionName: params.regionName ? params.regionName.$like : "",
          stationName: params.stationName ? params.stationName.$like : "",
          propChar: params.propChar ? params.propChar.$like : "",
          propertyBelong: params.propertyBelong ? params.propertyBelong.$like : "",
          address: params.address ? params.address.$like : "",
          resType: params.resType ? params.resType.$like : "",
          isShare: params.isShare ? params.isShare.$like : "",
          pageSize: this.pageSize,
          pageNum: this.pageNumber
        })
      }
      this.queryParam = {}
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.pageNumber = 1;
      this.getList();
    },
    async resetForm(formName) {
      this.resetQuery();
      this.busNameBefore = '';
      if (this.$refs[formName]) {
        this.$refs[formName].resetFields();
      }
      if (this.sortable) {
        this.sortable.destroy();
        this.sortable = null;
      }
      // let resRouteType = await this.$api.transApi.getRouteTypeSelectData({
      //   "specialty": '传输专业'
      // })
      this.activeNames= ['1','2'];
      this.componentKey = 0;
      this.detailTopo = false;
      this.trsCircuitBeforeJson = '';
      this.delList = [];
      this.loadingCircuit = false;
      this.noCust = true;
      this.dialogAdd = false;
      this.pubCustForm = {
        // id:'',
        // region:'',
        // coreNetDeviceName:'',
        // groupNumber:'',
        // adeviceName:'',
        // adevicePort:'',
        // adeviceType:'',
        // adeviceRoom:'',
        // zdeviceName:'',
        // zdevicePort:'',
        // zdeviceType:'',
        // zdeviceRoom:'',
        // circuitName:'',
        // circuitCode:'',
        // bandwidth:'',
        // circuitCarrierType:'',
        // isAzdeviceSame:'',
        // vpn:'',
        // isHorizontalLink:'',
        // route:'',
        // fullRoutInformation:'',
        // coreNetDevicePort:'',
        // coreNetDeviceIp:'',
      },
      this.routerForm = {
        id: '',
        circuitName: '',
        circuitCode: '',
        circuitRate: '',
        longCustName: '',
        oprStateId: '',
        adjustNum: '',
        aRegionId: '',
        zRegionId: '',
        isbak: '',
        circuitId: '',
        splicSegNum: '',
        aTrsNeId: '',
        aTrsNeName: '',
        aPortName: '',
        zTrsNeId: '',
        zTrsNeName: '',
        zPortName: '',
        coreNetDeviceName: '',
        aBusNetName: '',
        aBusNetPort: '',
        zBusNetName: '',
        zBusNetPort: ''
      },
      
      this.tableDatas = []
      this.changePage()
      // this.options = [
      //   {label:'待审核', value:'待审核'},
      //   {label:'通过', value:'通过'},
      //   {label:'不通过', value:'不通过'},
      // ]
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.initColumn(this.classId);
      this.getList();
    },

    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.getList();
    },
    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getList();
    },
    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChangeImport(val) {
      this.formImportRecords.pageSize = val;
      this.getImportRecord();
    },
    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChangeImport(val) {
      this.formImportRecords.pageNum = val;
      this.getImportRecord();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    async newAdd(val) {
      console.log(val,this.classId);
      
      this.totals = 0;
      this.page = 1; //第几页
      this.size = 10;
      this.specialised='传输路由新增';
      
      // let aRoute = await this.getRouteTypeSelectData('传输专业')
      if (val[0]) {
        if (this.classId == 'cndRelation') {
          this.pubCustForm.coreNetDeviceName = val[0].coreNetDeviceName
          // this.pubCustForm.coreNetDevicePort = val[0].coreNetDevicePort
          // this.pubCustForm.coreNetDeviceIp = val[0].coreNetDeviceIp
          this.pubCustForm.adeviceName = val[0].adeviceName
          this.pubCustForm.adevicePort = val[0].adevicePort
          this.pubCustForm.id = val[0].id
        } else if(this.classId === 'trsCircuit') {
          this.busNameBefore = val[0].busName
          val[0].tDwdRouteDetailList = val[0].routeDetailVOList;
          this.trsCircuitBeforeJson = JSON.stringify(val[0]);
          this.custDisabled = true;
          this.pubCustForm = val[0];
          this.pubCustForm.id = val[0].id 
          this.pubCustForm.busName = val[0].busName
          const KEY_MAP = {
            uniqueId: "uniqueId",
            routeIsbak: "routeIsbak",       
            sign: "operator",             
            adevicePort: "aPortName",  
            atrsNeName: "aTrsNeName",
            atrsNeId: "aTrsNeId",
            aresoureType: "aResoureType",
            aportId: "aPortId", 
            aportName: 'aPortName',
            ztrsNeId: "zTrsNeId",
            ztrsNeName: "zTrsNeName", 
            zportId: "zPortId", 
            zresoureType: "zResoureType",
            zdevicePort: "destDevicePort",
            abelongSpec: "aBelongSpec",  
            zbelongSpec: "zBelongSpec",
            zportName: 'zPortName',
          };
          let renamedArray = val[0].routeDetailVOList.map(obj => {
            const newObj = {};
            for (const [oldKey, newKey] of Object.entries(KEY_MAP)) {
              if (obj.hasOwnProperty(oldKey)) {
                newObj[newKey] = obj[oldKey]; // 将旧属性值赋给新属性名
              }
            }
            return newObj;
          });
          renamedArray = renamedArray.map((item, index) => ({
            ...item,
            id: index+1
          }));
          this.tableDatas = val[0].routeDetailVOList.length>0? renamedArray:   []  
          this.changePage()    
        } else if (this.classId === 'trsRouter') {
          this.routerForm.id = val[0].id
          this.routerForm.circuitName = val[0].circuitName
          this.routerForm.circuitCode = val[0].circuitCode
          this.routerForm.circuitRate = val[0].circuitRate
          this.routerForm.longCustName = val[0].longCustName
          this.routerForm.oprStateId = val[0].oprStateId
          this.routerForm.adjustNum = val[0].adjustNum
          this.routerForm.aRegionId = val[0].aRegionId
          this.routerForm.zRegionId = val[0].zRegionId
          this.routerForm.isbak = val[0].isbak
          this.routerForm.circuitId = val[0].circuitId
          this.routerForm.splicSegNum = val[0].splicSegNum
          this.routerForm.aTrsNeId = val[0].aTrsNeId
          this.routerForm.aTrsNeName = val[0].aTrsNeName
          this.routerForm.aPortName = val[0].aPortName
          this.routerForm.zTrsNeId = val[0].zTrsNeId
          this.routerForm.zTrsNeName = val[0].zTrsNeName
          this.routerForm.zPortName = val[0].zPortName
          this.routerForm.coreNetDeviceName = val[0].coreNetDeviceName
          this.routerForm.aBusNetName = val[0].aBusNetName
          this.routerForm.aBusNetPort = val[0].aBusNetPort
          this.routerForm.zBusNetName = val[0].zBusNetName
          this.routerForm.zBusNetPort = val[0].zBusNetPort
          // createOp
          // createDate
          // modifyOp
          // modifyDate
          // pageNum
          // pageSize
          this.changeSpecialised(this.specialised)
        }else if(this.classId === 'cndCircuit') {
          
          val[0].tDwdRouteDetailList = val[0].routeDetailVOList;
          this.trsCircuitBeforeJson = JSON.stringify(val[0]);
          this.custDisabled = true;
          this.pubCustForm.region = val[0].region
          this.pubCustForm = val[0];
          // this.pubCustForm.coreNetDeviceName = val[0].coreNetDeviceName
          // this.pubCustForm.groupNumber = val[0].groupNumber
          // this.pubCustForm.aDeviceName = val[0].aDeviceName
          // this.pubCustForm.aDevicePort = val[0].aDevicePort
          // this.pubCustForm.aDeviceType = val[0].aDeviceType
          // this.pubCustForm.aDeviceRoom = val[0].aDeviceRoom
          // this.pubCustForm.zDeviceName = val[0].zDeviceName
          // this.pubCustForm.zDevicePort = val[0].zDevicePort
          // this.pubCustForm.zDeviceType = val[0].zDeviceType
          // this.pubCustForm.zDeviceRoom = val[0].zDeviceRoom
          // this.pubCustForm.circuitName = val[0].circuitName
          // this.pubCustForm.circuitId = val[0].circuitId
          // this.pubCustForm.adjustNum = val[0].adjustNum
          // this.pubCustForm.circuitCode = val[0].circuitCode
          // this.pubCustForm.bandwidth = val[0].bandwidth
          // this.pubCustForm.circuitCarrierType = val[0].circuitCarrierType
          // this.pubCustForm.isAzDeviceSame = val[0].isAzDeviceSame
          // this.pubCustForm.vpn = val[0].vpn
          // this.pubCustForm.isHorizontalLink = val[0].isHorizontalLink
          // this.pubCustForm.route = val[0].route
          // this.pubCustForm.fullRoutInformation = val[0].fullRoutInformation
          this.pubCustForm.id = val[0].id
          this.$set(this.pubCustForm, 'busName', val[0].busName);
          this.$nextTick(() => {
            this.$refs.pubCustForm.clearValidate();
          });
          const KEY_MAP = {
            uniqueId: "uniqueId",
            routeIsbak: "routeIsbak",       
            sign: "operator",             
            adevicePort: "aPortName",  
            atrsNeName: "aTrsNeName",
            atrsNeId: "aTrsNeId",
            aresoureType: "aResoureType",
            aportId: "aPortId", 
            aportName: 'aPortName',
            ztrsNeId: "zTrsNeId",
            ztrsNeName: "zTrsNeName", 
            zportId: "zPortId", 
            zresoureType: "zResoureType",
            zdevicePort: "destDevicePort",
            abelongSpec: "aBelongSpec",  
            zbelongSpec: "zBelongSpec",
            zportName: 'zPortName',
          };
          let renamedArray = val[0].routeDetailVOList.map(obj => {
            const newObj = {};
            for (const [oldKey, newKey] of Object.entries(KEY_MAP)) {
              if (obj.hasOwnProperty(oldKey)) {
                newObj[newKey] = obj[oldKey]; // 将旧属性值赋给新属性名
              }
            }
            return newObj;
          });
          renamedArray = renamedArray.map((item, index) => ({
            ...item,
            id: index+1
          }));
          this.tableDatas = val[0].routeDetailVOList.length>0? renamedArray:   []   
          this.changePage()    
        } else if(this.classId === 'cnEqp') {
          this.cnEqpForm = {...val[0]}
        }
        this.splictRoutrDialog = false;
        this.custDisabled = true;
      } else {
        this.custDisabled = false;
        this.pubCustForm = {}
        this.multipleSelection = []
        this.$refs.multipleTable.clearSelection();
      }
      this.specialised = (this.classId == 'trsCircuit')?"传输路由新增":"核心网数通路由新增";
      this.pubCustForm.circuitCarrierType = (this.classId == 'trsCircuit')?"传输":"光纤直驱";
      this.dialogAdd = true;
      // if (!val[0].routeDetailVOList) {
      //   this.changeSpecialised(this.specialised)
      // }  
      if (this.classId == 'trsCircuit' || this.classId == 'cndCircuit'){
        this.$nextTick(() => {
        // 阻止默认行为
        document.body.ondrop = function (event) {
          event.preventDefault();
          event.stopPropagation();
        };
        this.rowDrop();
      });
      }
    },
    // async subForm2() {
    //   const { coreNetDeviceName, coreNetDeviceIp, ...filteredPubCustForm } = this.pubCustForm;
    //   const tableData = this.paramsSettingForm.tableData.map(({ id, aResoureType,zResoureType,operator, ...rest }, index) => ({
    //       ...rest,
    //       ...(operator !== undefined && { sign: operator }), 
    //       splicSegNum: index + 1,
    //       ...(aResoureType !== undefined && { aRouteType: aResoureType }), 
    //       ...(zResoureType !== undefined && { zRouteType: zResoureType }), 
    //     }));
    //   if (this.multipleSelection.length > 0) {
    //     let params2 = {
    //         id: this.pubCustForm.id,
    //         coreNetDeviceName: coreNetDeviceName,
    //         coreNetDeviceIp: coreNetDeviceIp,
    //         adeviceName: this.pubCustForm.adeviceName,
    //         // coreNetDevicePort: this.pubCustForm.coreNetDevicePort,
    //         // adevicePort: this.pubCustForm.adevicePort,
    //         ...filteredPubCustForm,
    //         tDwdRouteDetailList: tableData,
    //         "auditStatus": '待审核',
    //         "auditOp": null,
    //         "auditDate":  null,
    //         "auditReason": null,
    //     };
    //     params2.tDwdRouteDetailList=params2.tDwdRouteDetailList.map(({ id, aResoureType,zResoureType,operator, ...rest }, index) => ({
    //       ...rest,
    //         "auditStatus": '待审核',
    //         "auditOp": null,
    //         "auditDate":  null,
    //         "auditReason": null,
    //     }));
        
        
    //     let tDwdRouteDetailList =JSON.parse(this.trsCircuitBeforeJson).tDwdRouteDetailList.map(({ id, aresoureType,zresoureType, ...rest }, index) => ({
    //       ...rest,
    //       splicSegNum: index + 1,
    //       ...(aresoureType !== undefined && { arouteType: aresoureType }), 
    //       ...(zresoureType !== undefined && { zrouteType: zresoureType }), 
    //     }));
    //     JSON.parse(this.trsCircuitBeforeJson).tDwdRouteDetailList = tDwdRouteDetailList;
    //     let jsonData = JSON.parse(this.trsCircuitBeforeJson);
    //     jsonData.tDwdRouteDetailList = tDwdRouteDetailList;
        
    //     let params;
    //       params = {
    //         id: this.pubCustForm.id,
    //         coreNetDeviceName: coreNetDeviceName,
    //         coreNetDeviceIp: coreNetDeviceIp,
    //         adeviceName: this.pubCustForm.adeviceName,
    //         // coreNetDevicePort: this.pubCustForm.coreNetDevicePort,
    //         // adevicePort: this.pubCustForm.adevicePort,
    //         ...filteredPubCustForm,
    //         tDwdRouteDetailList: tableData,
    //         "beforeJson": JSON.stringify(jsonData),
    //         "afterJson":JSON.stringify(params2),
    //         "delList":this.delList,
    //         "auditStatus": '待审核',
    //         "auditOp": null,
    //         "auditDate":  null,
    //         "auditReason": null,
    //       }
          
    //     let res;
    //     if (this.classId == 'cndRelation') {
    //       res = await this.$api.coreApi.updateTDwdCircuitNet(params)
    //     } else if (this.classId == 'trsCircuit') {
    //       this.loadingCircuit = true;
    //       res = await this.$api.transApi.updateTransCircuit(params)
    //     } else if (this.classId == 'cndCircuit') {
    //       this.loadingCircuit = true;
    //       res = await this.$api.transApi.updateTransCircuit(params)
    //     } 
    //     if (res.code == 200) {
    //         this.$message({
    //           message: this.multipleSelection.length == 0 ? '新增成功' : '修改成功',
    //           type: 'success'
    //         });
    //         this.dialogAdd = false;
    //         this.getList();
    //       } else {
    //         // this.dialogAdd = false;
    //         this.$message.error(this.multipleSelection.length == 0 ? '新增失败' : '修改失败');
    //       }
    //   }else {
    //     let params2 = {
    //         id: this.pubCustForm.id,
    //         ...this.pubCustForm,
    //       circuitCarrierType: this.classId == 'trsCircuit' ? '传输' : '光纤直驱',
    //       tDwdRouteDetailList: tableData,
    //       "auditStatus": '待审核',
    //         "auditOp": null,
    //         "auditDate":  null,
    //         "auditReason": null,
    //     };
        
    //     params2.tDwdRouteDetailList=params2.tDwdRouteDetailList.map(({ id, aResoureType,zResoureType,operator, ...rest }, index) => ({
    //       ...rest,
    //         "auditStatus": '待审核',
    //         "auditOp": null,
    //         "auditDate":  null,
    //         "auditReason": null,
    //     }));
    //     let tDwdRouteDetailList =JSON.parse(this.trsCircuitBeforeJson).tDwdRouteDetailList.map(({ id, aresoureType,zresoureType, ...rest }, index) => ({
    //       ...rest,
    //       splicSegNum: index + 1,
    //       ...(aresoureType !== undefined && { arouteType: aresoureType }), 
    //       ...(zresoureType !== undefined && { zrouteType: zresoureType }), 
    //     }));
    //     JSON.parse(this.trsCircuitBeforeJson).tDwdRouteDetailList = tDwdRouteDetailList;
    //     let jsonData = JSON.parse(this.trsCircuitBeforeJson);
    //     jsonData.tDwdRouteDetailList = tDwdRouteDetailList;
    //     let params = {
    //       id: this.pubCustForm.id,
    //       ...this.pubCustForm,
    //       circuitCarrierType: this.classId == 'trsCircuit' ? '传输' : '光纤直驱',
    //       tDwdRouteDetailList: tableData,
    //       "beforeJson": JSON.stringify(jsonData),
    //       "auditStatus": '待审核',
    //       "afterJson":JSON.stringify(params2),
    //       "auditOp": null,
    //       "auditDate":  null,
    //       "auditReason": null,
    //     }
        
    //     if (this.classId == 'cndRelation') {
    //       let params = {
    //         coreNetDeviceName: this.pubCustForm.coreNetDeviceName ? this.pubCustForm.coreNetDeviceName : "",
    //         // coreNetDevicePort: this.pubCustForm.coreNetDevicePort ? this.pubCustForm.coreNetDevicePort : "",
    //         // coreNetDeviceIp: this.pubCustForm.coreNetDeviceIp ? this.pubCustForm.coreNetDeviceIp : "",
    //         adeviceName: this.pubCustForm.adeviceName ? this.pubCustForm.adeviceName : "",
    //         adevicePort: this.pubCustForm.adevicePort ? this.pubCustForm.adevicePort : "",
    //       }
          
      
    //       this.$api.coreApi.insertTDwdCircuitNet(params).then(res => {
    //         if (res.code == 200) {
    //           this.$message({
    //             message: this.multipleSelection.length == 0 ? '新增成功' : '修改成功',
    //             type: 'success'
    //           });
    //           this.loadingCircuit = false;
    //           this.dialogAdd = false;
    //           this.getList();
    //         } else {
    //           this.$message.error(this.multipleSelection.length == 0 ? '新增失败' : '修改失败');
    //           this.loadingCircuit = false;
    //         }
    //       })
    //     } else if (this.classId == 'cndCircuit') {
    //       this.$refs.CirciutForm.validate((valid) => {
    //           if (valid) {
    //           this.$api.transApi.insertTransCircuit(params)
    //           .then(res => {
    //             if (res.code == 200) {
    //               this.$message({
    //                 message: this.multipleSelection.length == 0 ? '新增成功' : '修改成功',
    //                 type: 'success'
    //               });
                  
    //               this.dialogAdd = false;
    //               this.getList();
    //             } else {
    //               this.$message.error(this.multipleSelection.length == 0 ? '新增失败' : '修改失败');
    //               // this.dialogAdd = false;
    //             }
    //           })
    //         } else {
    //           this.$message
    //           .error({
    //             message: '请检查表单输入',
    //             type: 'error'
    //           });
    //           return false;
    //         }
    //       })
    //     }else {
    //       this.$refs.CirciutForm.validate((valid) => {
    //           if (valid) {
    //           this.$api.transApi.insertTransCircuit(params)
    //           .then(res => {
    //             if (res.code == 200) {
    //               this.$message({
    //                 message: this.multipleSelection.length == 0 ? '新增成功' : '修改成功',
    //                 type: 'success'
    //               });
    //               // this.dialogAdd = false;
    //               this.dialogAdd = false;
    //               this.getList();
    //             } else {
    //               this.$message.error(this.multipleSelection.length > 0 ? '修改失败' : '新增失败');
    //               // this.dialogAdd = false;
    //             }
    //           })
    //         } else {
    //           this.$message
    //           .error({
    //             message: '请检查表单输入',
    //             type: 'error'
    //           });
    //           return false;
    //         }
    //       })
    //     }
    //   }
    // },
    async subForm2() {
      try {
        this.loadingCircuit = true;
        this.busNameBefore = '';
        // 安全解析 JSON，为空时返回 { tDwdRouteDetailList: [] }
        const safeJsonParse = (jsonString) => {
          try {
            return jsonString ? JSON.parse(jsonString) : { tDwdRouteDetailList: [] };
          } catch (e) {
            console.error('JSON解析错误:', e);
            return { tDwdRouteDetailList: [] };
          }
        };
        const typeMap = {
          "传输专业": '传输网元',
          核心网专业: '核心网网元',
          核心网数通专业:  '数通网元'
        };
        // 转换表格数据
        const transformTableData = (data) => {
          return data.map(
          ({ id, aResoureType, zResoureType, operator, ...rest }, index) => ({
            ...rest,
            ...(operator !== undefined && { sign: operator }),
            splicSegNum: index + 1,
            ...(aResoureType !== undefined && { aRouteType: aResoureType }),
            ...(zResoureType !== undefined && { zRouteType: zResoureType }),
            ...(typeMap[rest.aBelongSpec] && { aResoureType: typeMap[rest.aBelongSpec] }),
            ...(typeMap[rest.zBelongSpec] && { zResoureType: typeMap[rest.zBelongSpec] }),
            auditStatus: '待审核',
            auditOp: null,
            auditDate: null,
            auditReason: null,
          })
        )};

        // 准备 beforeJson 数据（处理空值）
        const prepareBeforeJson = () => {
          const beforeJson = safeJsonParse(this.trsCircuitBeforeJson);
          const detailList = beforeJson.tDwdRouteDetailList || []; // 默认空数组
          
          const tDwdRouteDetailList = detailList.map(
            ({ id, aresoureType, zresoureType, ...rest }, index) => ({
              ...rest,
              splicSegNum: index + 1,
              ...(aresoureType !== undefined && { arouteType: aresoureType }),
              ...(zresoureType !== undefined && { zrouteType: zresoureType }),
            })
          );
          
          return { ...beforeJson, tDwdRouteDetailList };
        };

        // 主逻辑
        const { coreNetDeviceName, coreNetDeviceIp, ...filteredPubCustForm } = this.pubCustForm;
        if (filteredPubCustForm.routeDetailVOList) filteredPubCustForm.routeDetailVOList = filteredPubCustForm.routeDetailVOList.map(item => ({
          ...item,
          ...(typeMap[item.abelongSpec] && { aResoureType: typeMap[item.abelongSpec] }),
          ...(typeMap[item.zbelongSpec] && { zResoureType: typeMap[item.zbelongSpec] }),
        }))

        const tableData = transformTableData(this.paramsSettingForm.tableData);
        const isUpdate = this.multipleSelection.length > 0;
        const operationText = isUpdate ? '修改' : '新增';

        // 构造参数
        const baseParams = {
          id: this.pubCustForm.id,
          ...(isUpdate ? {
            coreNetDeviceName,
            coreNetDeviceIp,
            adeviceName: this.pubCustForm.adeviceName,
            ...filteredPubCustForm,
          } : {
            ...this.pubCustForm,
            circuitCarrierType: this.classId === 'trsCircuit' ? '传输' : '光纤直驱',
          }),
          tDwdRouteDetailList: tableData,
          auditStatus: '待审核',
          auditOp: null,
          auditDate: null,
          auditReason: null,
        };

        let res;
        if (isUpdate) {
          const params = {
            ...baseParams,
            beforeJson: JSON.stringify(prepareBeforeJson()),
            afterJson: JSON.stringify(baseParams),
              "busNameEditFalg":this.busNameEditFalg,
              "busNameBefore":this.busNameBefore,
            delList: this.delList,
          };

          // 根据 classId 调用不同 API
          switch (this.classId) {
            case 'cndRelation':
              res = await this.$api.coreApi.updateTDwdCircuitNet(params);
              break;
            case 'trsCircuit':
            case 'cndCircuit':
              res = await this.$api.transApi.updateTransCircuit(params);
              break;
          }
        } else {
          // 新增逻辑
          if (this.classId === 'cndRelation') {
            res = await this.$api.coreApi.insertTDwdCircuitNet({
              coreNetDeviceName: this.pubCustForm.coreNetDeviceName || "",
              adeviceName: this.pubCustForm.adeviceName || "",
              adevicePort: this.pubCustForm.adevicePort || "",
            });
          } else {
            // 表单验证并提交
            try {
              const valid = await this.$refs['pubCustForm'].validate();
              if (!valid) {
                this.$message.error('请检查表单输入');
                return;
              }
              res = await this.$api.transApi.insertTransCircuit({
                ...baseParams,
                beforeJson: JSON.stringify(prepareBeforeJson()),
                afterJson: JSON.stringify(baseParams),
              });
            } catch (error) {
              this.$message.error('表单校验失败');
              return;
            }
          }
        }

        // 统一处理结果
        if (res && res.code === 200) {
          this.$message.success(`${operationText}成功`);
          this.dialogAdd = false;
          await this.getList();
        } else {
          this.$message.error(`${operationText}失败`);
        }
      } catch (error) {
        console.error('提交失败:', error);
        this.$message.error('操作失败，请重试');
      } finally {
        this.loadingCircuit = false;
      }
    },
    async subCnEqpForm(){
      if (this.multipleSelection.length > 0) {
        let params = {
          ...this.cnEqpForm,
        }
        const res = await this.$api.transApi.updateTDwdCoreNet(params)
        if (res.code == 200) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.dialogAdd = false;
          this.getList();
        }
      }
    },
    async subRouterForm(){
      if (this.multipleSelection.length > 0) {
        let params = {
          ...this.routerForm,
        }
        const res = await this.$api.transApi.updateTransRoute(params)
        if (res.code == 200) {
          this.$message({
            message: this.multipleSelection.length == 0 ? '新增成功' : '修改成功',
            type: 'success'
          });
          this.dialogAdd = false;
          this.getList();
        } else {
          this.$message.error(this.multipleSelection.length == 0 ? '新增失败' : '修改失败');
        }
      } else {
        this.$refs.routerForm.validate((valid) => {
          if (valid) {
            let params = {
              ...this.routerForm,
              aBusNetName: this.routerForm.aBusNetName,
              aBusNetPort: this.routerForm.aBusNetPort,
              zBusNetName: this.routerForm.zBusNetName,
              zBusNetPort: this.routerForm.zBusNetPort,
            }
            this.$api.transApi.insertTransRoute(params)
            .then(res => {
              if (res.code == 200) {
                this.$message({
                  message: '新增成功',
                  type: 'success'
                });
                this.dialogAdd = false;
                this.getList();
              } else {
                this.$message.error({
                  message: '新增失败',
                  type: 'error'
                });
              }
            })
          } else {
            this.$message.error({
                  message: '请检查表单输入',
                  type: 'error'
                });
            return false;
          }
        });
      }
    },
    splicingCircuitsSum() {
      // this.tableDatas 
      const defaultSpecMap = {
        '传输路由新增': { a: '传输专业', z: '传输专业' },
        '核心网路由新增': { a: '核心网专业', z: '核心网专业' },
        '跨专业路由新增': { a: '', z: '' }
      };
      // const defaultTypeMap = {
      //   '传输路由新增': { a: '传输网元', z: '传输网元' },
      //   '核心网路由新增': { a: '核心网网元', z: '核心网网元' },
      //   '跨专业路由新增': { a: '', z: '' }
      // };
      let pushData = this.splicingCircuits.map((i,index) => {
        return {
          id:this.tableDatas.length + index +1,
          aBelongSpec: defaultSpecMap[this.specialised].a,
          zBelongSpec: defaultSpecMap[this.specialised].z,
          // aResoureType:defaultTypeMap[this.specialised].a,
          // zResoureType:defaultTypeMap[this.specialised].z,
          aTrsNeName: i.atrsNeName,
          aResoureType:i.aeqpType,
          aPortName: i.aportName,
          zTrsNeName: i.ztrsNeName,
          zResoureType:i.zeqpType,
          zPortName: i.zportName,
          routeIsbak: i.isbak,
          operator:JSON.parse(localStorage.getItem('user')),
        }
      })
      this.tableDatas = [...this.tableDatas ,...pushData]
      this.changePage()
      this.splictRoutrDialog = false
    },
    //page改变时的回调函数，参数为当前页码
    currentChange(val) {
      this.page = val;
      this.changePage();
    },
    //size改变时回调的函数，参数为当前的size
    sizeChange(val) {
      this.size = val;
      this.page = 1;
      this.changePage();
      // this.splicingCircuitsSum();
    },

    handleTemplateExportdialog() {
      this.dialogExport = true
    },
    resetImport() {
      this.formImportRecords = {
        templateName: '',
        status: '',
        operationTime: ['', ''],
        pageNum: 1,
        pageSize: 10,
        total: 1,
      }
      this.getImportRecord();
    },
    // 电路导入记录
    getImportRecord() {
      this.tableColumnsImport = [{
        displayName: '模板名称',
        columnName: 'templateName'
      }, {
        displayName: '用户',
        columnName: 'operator'
      }, {
        displayName: '模板数据总量',
        columnName: 'total'
      }, {
        displayName: '校验进度',
        columnName: 'portName'
      }, {
        displayName: '导入状态',
        columnName: 'status'
      }, {
        displayName: '操作时间',
        columnName: 'createdTime'
      }, {
        displayName: '总耗时',
        columnName: 'portName'
      }, {
        displayName: '操作',
        columnName: 'businessType'
      }]
      let params = {
        status: this.formImportRecords.status || '',
        templateName: this.formImportRecords.templateName || '',
        startTime: this.formImportRecords.operationTime ? this.formImportRecords.operationTime[0] : '',
        endTime: this.formImportRecords.operationTime ? this.formImportRecords.operationTime[1] : '',
        pageNum: this.formImportRecords.pageNum,
        pageSize: this.formImportRecords.pageSize,
      }
      this.$api.transApi.getTransRouteImportRecord(params)
        .then(res => {
          this.resourceDataImport = res.data.records;
          this.formImportRecords.total = res.data.total;
        })
    },
    // 导出
    handleTemplateExport() {      
      let scope = this;
      // this.loading = true;
      this.checkedCitiesa = this.checkedCities;
      const filteredObjs = scope.tableColumnAll.filter((obj) => scope.checkedCities.includes(obj.columnName));
      this.tableColumns = filteredObjs;
      let params = {
        pageSize: -1,
        pageNum: 1
      }
      if (this.classId == 'cndCircuit') {
        const temp = [
          'circuitCode',
          'aDeviceName',
          'aDevicePort',
          'aDeviceType',
        ]
        const result = temp.reduce((acc, key) => {
          acc[key] = '';
          return acc;
        }, {});
        Object.assign(params, result);
      } else if (this.classId == 'trsCircuit' || this.classId == 'trsRouter') {
        params = {
          pageNum: "1",
          pageSize: this.total
        }
      } else {
        const temp = this.queryFields.map(i=>i.tableColumnName)
        const result = temp.reduce((acc, key) => {
          acc[key] = '';
          return acc;
        }, {});
        Object.assign(params, result);
      }
      params = {...params, ...this.queryForm,}
      this.doExportAxios(params)
        .then(res => {          
          if (res.size) {
            // 文件下载
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
          });
          let link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.setAttribute('download', scope.className + '数据.' + this.fileType);
          link.click();
          link = null;
          this.$message({
            message: '下载成功',
            type: 'success',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
          } else {
            this.$message({
              message: '下载失败',
              type: 'error',
              duration: 2000,
              showClose: true
            });
          }
        })
        .catch(() => {
          this.$message({
            message: '下载失败',
            type: 'error',
            duration: 2000,
            showClose: true
          });
          this.dialogExport = false;
          this.loading = false;
        });
    },
    doExportAxios(params) {
      if (this.classId == "cndEqp") {
        return this.$api.coreApi.exportAceDevice(params)
      } else if (this.classId == "cndCard") {
        return this.$api.coreApi.exportAceCard(params)
      } else if (this.classId == "cndPort") {
        return this.$api.coreApi.exportAcePort(params)
      } else if (this.classId == "cndLink") {
        return this.$api.coreApi.exportAceCircuit(params)
      } else if (this.classId == "cndCircuit") {
        return this.$api.coreApi.exportTDwdCircuitDetail(params)
      } else if (this.classId == "cndRelation") {
        return this.$api.coreApi.exportTDwdCircuitNet(params)
      } else if (this.classId == "cnEqp") {
        return this.$api.coreApi.exportTDwdCoreNet(params)
      } else if (this.classId == "trsPort") {
        return this.$api.transApi.exportTDwdPort(params)
      } else if (this.classId == "trsCircuit") {
        return this.$api.transApi.exportTransCircuit(params)
      } else if (this.classId == "trsRouter") {
        return this.$api.transApi.exportTransRoute(params)
      } else if (this.classId == "trsEqp") {
        return this.$api.transApi.exportTransNet(params)
      } else if (this.classId == "trsCard") {
        return this.$api.transApi.exportBoardCard(params)
      } else if (this.classId == "spcDistrict") {
        return this.$api.spaceApi.exportDistrict(params)
      } else if (this.classId == "spcStation") {
        return this.$api.spaceApi.exportStation(params)
      } else if (this.classId == "spcRoom") {
        return this.$api.spaceApi.exportRoom(params)
      } else if (this.classId == "spcPosition") {
        return this.$api.spaceApi.exportPosition(params)
      }
    },
    // 传输路由新增切换专业
    changeSpecialised(val){
      // if (val=='传输路由新增') {
      //   this.tableDatas.map(item=>{
      //     item.aBelongSpec = '传输专业';
      //     // item.aResoureType = '传输网元';
      //     item.zBelongSpec = '传输专业';
      //     // item.zResoureType = '传输网元';
      //   })
      //   this.changePage()
      // } else if (val=='核心网数通路由新增') {
      //   this.tableDatas.map(item=>{
      //     item.aBelongSpec = '核心网数通专业';
      //     // item.aResoureType = '核心网网元';
      //     item.zBelongSpec = '核心网数通专业';
      //     // item.zResoureType = '核心网网元';
      //   })
      //   this.changePage()
      // }else if (val=='核心网路由新增') {
      //   this.tableDatas.map(item=>{
      //     item.aBelongSpec = '核心网专业';
      //     // item.aResoureType = '核心网网元';
      //     item.zBelongSpec = '核心网专业';
      //     // item.zResoureType = '核心网网元';
      //   })
      //   this.changePage()
      // } else {
      //   this.tableDatas.map(item=>{
      //     item.aBelongSpec = '';
      //     // item.aResoureType = '';
      //     item.zBelongSpec = '';
      //     // item.zResoureType = '';
      //   })
      //   this.changePage()
      // }
    },
    rowDrop() {
      if (this.sortable) {
        this.sortable.destroy(); // 销毁旧实例
      }
      const wrapper = this.$refs.elTable.$el.querySelector('.el-table__body-wrapper');
      const el = wrapper.querySelector('table tbody');
    
      if (!el) {
        console.error('无法找到 tbody 元素');
        return;
      }
      this.sortable = Sortable.create(el, {
        // 灵敏度核心参数 (桌面端/移动端通用)
        fallbackTolerance: 5,      // 移动5px触发拖拽（默认3）
        delay: 50,                 // 延迟50ms消除点击误触
        touchStartThreshold: 8,    // 移动端需移动8px
      
        // 性能优化参数
        forceFallback: true,       // 强制使用polyfill
        dragClass: "sortable-drag-force", // 强制样式
        
        // 事件过滤
        filter: ".disabled-drag",  // 过滤不可拖拽元素
        preventOnFilter: false,
        
        // 手柄配置
        handle: ".drag-handle",    // 精确指定拖拽手柄
        draggable: ".el-table__row", // 明确可拖拽元素
        dataIdAttr: 'data-id', 
        animation: 150,
        // onMove(e) {
        //   return e.related.className.indexOf('filtered') === -1;
        // },
        onMove: (e) => {
          const { dragged, related } = e;
          const draggedRow = e.dragged;
          const relatedRow = e.related;
            const prevRow = relatedRow.previousElementSibling;
            const nextRow = relatedRow.nextElementSibling;
    
            const prevOperator = prevRow?.querySelector('.operator-cell')?.textContent.trim();
            const nextOperator = nextRow?.querySelector('.operator-cell')?.textContent.trim();
            // 拖拽方向判断
            const rows = Array.from(e.from.children);
            const draggedIndex = rows.indexOf(dragged);
            const relatedIndex = rows.indexOf(related);
            const isDraggingForward = draggedIndex < relatedIndex;
                      
                    // 规则1：允许拖拽到列表两端
            if (prevOperator === undefined || nextOperator === undefined) {
              return true;
            }
          
            // 规则2：向前拖拽时，前一行必须匹配
            if (!isDraggingForward && prevOperator !== this.currentOperator) {
              return false;
            }
          
            // 规则3：向后拖拽时，后一行必须匹配
            if (isDraggingForward && nextOperator !== this.currentOperator) {
              return false;
            }
    
          // 其他情况允许
          return true;

        },
        onEnd: ({ newIndex, oldIndex }) => {
          this.handleDragEnd(oldIndex, newIndex);
        }
      })
    },
    // 拖拽校验
    validateDrag(evt) {
      
      const draggedRow = this.getRowData(evt.dragged);
      const relatedRow = this.getRowData(evt.related);
      console.log(draggedRow);
      
      // 1. 校验拖动行权限
      if (!draggedRow || draggedRow.operator !== this.currentOperator) {
        this.showDragHint('无权限拖动他人数据');
        return false;
      }

      // 2. 校验目标位置合法性
      const { prevRow, nextRow } = this.getAdjacentRows(relatedRow, evt.willInsertAfter);
      
      // 3. 检查相邻行权限
      const isValid = [prevRow, nextRow].every(row => 
        !row || row.operator === this.currentOperator
      );

      if (!isValid) {
        this.showDragHint('禁止插入到他人数据之间');
        return false;
      }

      return true;
    },
    // 获取相邻行数据
    getAdjacentRows(relatedRow, isInsertAfter) {
      const index = this.tableDatas.findIndex(r => r.id === relatedRow?.id);
      if (index === -1) return { prevRow: null, nextRow: null };

      return {
        prevRow: isInsertAfter ? relatedRow : this.tableDatas[index - 1],
        nextRow: isInsertAfter ? this.tableDatas[index + 1] : relatedRow
      };
    },

    // 获取行数据
    getRowData(el) {
      const tr = el?.closest('tr.el-table__row');
      return tr ? this.tableDatas.find(r => r.id == tr.dataset.id) : null;
    },
    handleDragEnd(oldIndex, newIndex) {
      // 防御式编程
      if (
        typeof oldIndex !== 'number' ||
        typeof newIndex !== 'number' ||
        oldIndex === newIndex
      ) return;

      // 创建新数组（保持响应性）
      const newData = JSON.parse(JSON.stringify(this.paramsSettingForm.tableData));
      
      // 索引校验
      const maxIndex = newData.length - 1;
      const safeOld = Math.max(0, Math.min(oldIndex, maxIndex));
      const safeNew = Math.max(0, Math.min(newIndex, maxIndex));
      const pageStart = (this.page - 1) * this.size;
      // 执行移动
      if (safeOld <= maxIndex && safeNew <= maxIndex) {
        const [item] = newData.splice(safeOld, 1);
        newData.splice(safeNew, 0, item);
        this.tableDatas.splice(pageStart, this.size, ...newData);
        // 如果需要同步更新原始数据源
        // this.tableDatas = newData; // 触发响应式更新
      }
      
      // 强制刷新表格
      this.$nextTick(() => {
        this.changePage()
        this.$refs.elTable?.doLayout?.();
      });
    },
    setDynamicRowClass({ row }) {
     // 返回要动态添加的类名字符串
      return row.operator !== this.currentOperator ? 'filtered' : '';
    },
    // 新增路由表格增加空行
    async addList() {
      const defaultSpecMap = {
        '传输路由新增': { a: '传输专业', z: '传输专业' },
        '核心网路由新增': { a: '核心网专业', z: '核心网专业' },
        '核心网数通路由新增': { a: '核心网数通专业', z: '核心网数通专业' },
        '跨专业路由新增': { a: '', z: '' }
      };
      const defaultTypeMap = {
        '传输路由新增': { a: '传输网元', z: '传输网元' },
        '核心网路由新增': { a: '核心网网元', z: '核心网网元' },
        '跨专业路由新增': { a: '', z: '' }
      };
      
      this.tableDatas.push({
          aBelongSpec: defaultSpecMap[this.specialised].a,
          aTrsNeName:'',
          aResoureType:'',
          aPortName:'',
          zBelongSpec: defaultSpecMap[this.specialised].z,
          zTrsNeName:'',
          zResoureType:'',
          zPortName:'',
          routeIsbak:'',
          operator:JSON.parse(localStorage.getItem('user')),
          id:this.tableDatas.length+1,
          "auditStatus": '待审核',
          "auditOp": '',
          "auditDate":  '',
          "auditReason": '',
      })
       this.changePage()
    },
    // 删除当前行
    deleteRow (index,row) {
      this.$confirm('此操作将删除该行数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // if (this.tableDatas.length !== 1) {
            let del = (this.page-1) * this.size + index
            this.tableDatas.splice(del, 1)
            this.delList.push(row)
            this.changePage();
        
          if (this.paramsSettingForm.tableData.length<1) {
            this.currentChange(this.page-1)
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    async getRouteTypeSelectData(BelongSpec){
      const res = await this.$api.transApi.getRouteTypeSelectData({
        specialty: BelongSpec
      });
      return res.data
    },
    changeBelongSpec(row,val){
      if (val=='a') {
        row.aTrsNeName = '';
        row.aTrsNeId='';
        row.aResoureType = '';
        row.aPortName = '';
        row.aNetworkElementTypes = [];
      } else {
        row.zTrsNeName = '';
        row.zTrsNeId='';
        row.zResoureType = '';
        row.zPortName = '';
        row.zNetworkElementTypes = [];
      }
    },
    // 更新资源类型
  async updateResourceType(row, end) {
  try {
    this.$set(row, 'loading', true);
    // this.$set(row, `${end}ResoureType`, ''); // 清空已选类型
    const res = await this.$api.transApi.getRouteTypeSelectData({
      specialty: row[`${end}BelongSpec`]
    });
    
    if (res.resultCode === 200) {
      this.$set(row, `${end}NetworkElementTypes`, res.data);
      this.$set(row, 'loading', false);
    } else {
      this.$message.error('获取资源类型失败');
      this.$set(row, `${end}NetworkElementTypes`, []);
    }
  } catch (error) {
    // this.$set(this.loadingStates, key, false);
    console.error('接口请求失败:', error);
    this.$message.error('获取资源类型失败');
    this.$set(row, `${end}NetworkElementTypes`, []);
  }
},
  // 获取资源类型选项
  getResourceTypeOptions(profession) {
    return profession === '核心网专业' 
      ? ['数通网元', '核心网网元'] 
      : ['传输网元'];
  },
  // 是否禁用资源类型选择
  shouldDisableResourceType(row, end) {
    if (row[`${end}BelongSpec`] === '传输专业') {
      return false
    } else if (row[`${end}BelongSpec`] === '核心网专业') {
      return false
    } else {
      return true
    }
  },
  // 传输路由电路状态下拉查询
  getPubEnumDefinition(){
    this.$api.coreApi.getTransCircuitStatusSelectData()
     .then(res=>{
       this.enumData = res.data
     })
  },
  // 传输路由查询电路列表
  eqpTab(){
    this.equipDialog = true
  },
  busFrom(msg){
    this.$set(this.pubCustForm,'busName',msg.busName)
    this.$nextTick(() => {
            this.$refs.pubCustForm.clearValidate();
          });
    this.busDialog = false;
    if (this.busNameBefore !== '' && this.busNameBefore !== msg.busName) {
      this.busNameDialog = true;
    }
  },
  groupForm(msg){
    this.$set(this.pubCustForm,'groupNumber',msg.groupNumber)
    this.$nextTick(() => {
            this.$refs.pubCustForm.clearValidate();
          });
    this.groupDialog = false;
  },
  portFrom(msg){
    if (!this.belongSpecRouterType) {
      if (this.maintainAreaId == 'A') {
        this.$set(this.pubCustForm,'aDevicePort',msg[0].portName)
      } else {
        this.$set(this.pubCustForm,'zDevicePort',msg[0].portName)
      }
    } else {
      if (this.maintainAreaId == 'A') {
        this.$set(this.currentRow,'aPortName',msg[0].portName)
        this.$set(this.currentRow,'aPortId',msg[0].portId)
      } else {
        this.$set(this.currentRow,'zPortName',msg[0].portName)
        this.$set(this.currentRow,'zPortId',msg[0].portId)
      }
    }
    this.$nextTick(() => {
      this.$refs.pubCustForm.clearValidate();
    });
    this.portDialog = false;
  },
  equipFrom(msg){
    if (this.belongSpecRouterType) {
      if (this.maintainAreaId == 'A') {
        this.$set(this.pubCustForm,'aDeviceName',msg[0].eqpName)
        this.$set(this.pubCustForm,'aDevicePort','')
        this.$set(this.pubCustForm,'aDeviceType',msg[0].routeTypeName)
        this.$set(this.pubCustForm,'aARreaName',msg[0].regionName)
        this.$set(this.pubCustForm,'aDeviceRoom',msg[0].roomName)
      } else if (this.maintainAreaId == 'Z'){
        this.$set(this.pubCustForm,'zDeviceName',msg[0].eqpName)
        this.$set(this.pubCustForm,'zDevicePort','')
        this.$set(this.pubCustForm,'zDeviceType',msg[0].routeTypeName)
        this.$set(this.pubCustForm,'zARreaName',msg[0].regionName)
        this.$set(this.pubCustForm,'zDeviceRoom',msg[0].roomName)
      } else {
        this.$set(this.pubCustForm,'coreNetDeviceName',[...new Set(msg.map(item => String(item.eqpName)).filter(id => id !== "undefined" && id !== "null"))].join(','))
      }
      this.$nextTick(() => {
            this.$refs.pubCustForm.clearValidate();
          });
    } else {
      if (this.maintainAreaId == 'A') {
      // [...new Set(msg.map(item => String(item.eqpId)).filter(id => id !== "undefined" && id !== "null"))].join(',')
        this.$set(this.currentRow,'aTrsNeId',[...new Set(msg.map(item => String(item.eqpId)).filter(id => id !== "undefined" && id !== "null"))].join(','))
        this.$set(this.currentRow,'aTrsNeName',[...new Set(msg.map(item => String(item.eqpName)).filter(id => id !== "undefined" && id !== "null"))].join(','))
        this.$set(this.currentRow,'aPortName','')
        this.$set(this.currentRow,'aPortId','')
        this.$set(this.currentRow,'aResoureType',[...new Set(msg.map(item => String(item.routeTypeName)).filter(id => id !== "undefined" && id !== "null"))].join(','))
        if (this.currentRow.aResoureType) {
          this.$set(this.currentRow,'isNetwork',true)
        } else {
          this.$set(this.currentRow,'isNetwork',false)
        }
      } else {
  
        this.$set(this.currentRow,'zTrsNeId',[...new Set(msg.map(item => String(item.eqpId)).filter(id => id !== "undefined" && id !== "null"))].join(','))
        this.$set(this.currentRow,'zTrsNeName',[...new Set(msg.map(item => String(item.eqpName)).filter(id => id !== "undefined" && id !== "null"))].join(','))
        this.$set(this.currentRow,'zPortName','')
        this.$set(this.currentRow,'zPortId','')
        this.$set(this.currentRow,'zResoureType',[...new Set(msg.map(item => String(item.routeTypeName)).filter(id => id !== "undefined" && id !== "null"))].join(','))
        if (this.currentRow.zResoureType) {
          this.$set(this.currentRow,'isNetwork',true)
        } else {
          this.$set(this.currentRow,'isNetwork',false)
        }
      }
    }
    
        this.equipDialog = false;
  },
  eqpFrom(msg){
    let target = JSON.stringify(this.pubCustForm);
    this.pubCustForm = msg;
    // this.pubCustForm.busName = JSON.parse(target).busName;
    this.pubCustForm.groupNumber = JSON.parse(target).groupNumber;
    this.pubCustForm.id = JSON.parse(target).id;
    this.$set(this.pubCustForm,'busName',JSON.parse(target).busName)
    this.$nextTick(() => {
            this.$refs.pubCustForm.clearValidate();
          });
    this.eqpDialog = false;
  },
  eqpCndFrom(msg) {
    let target = JSON.stringify(this.pubCustForm);
    this.pubCustForm = msg;
    // this.pubCustForm.busName = JSON.parse(target).busName;
    this.pubCustForm.groupNumber = JSON.parse(target).groupNumber;
    this.pubCustForm.id = JSON.parse(target).id;
    this.$set(this.pubCustForm,'busName',JSON.parse(target).busName)
    this.$nextTick(() => {
            this.$refs.pubCustForm.clearValidate();
          });
    this.eqpCndDialog = false;
  },
  getCustFrom(msg){},
  getRegionFrom(msg){
    // console.log(msg);
    if (this.maintainAreaId == 'A') {
      this.pubCustForm.aARreaName = msg.districtName;
    } else {
      this.pubCustForm.zARreaName = msg.districtName;
    }
    this.regionDialog = false;
  },
  getAllCounties(){
        this.$api.coreApi.getTransCircuitRateSelectData({circuitRate:''})
      .then(res=>{
          this.getCircuitRate = res.data
      }) 
    },
    regionTab(val){
        this.regionDialog = true;
        this.maintainAreaId = val
      },
    portTab(val,type,specType,row){
      this.portDialog = true;
      this.currentRow = row;
      this.maintainAreaId = val;
      this.deviceName = specType;
      this.belongSpecRouterType = type;
    },
    equipTab(val,row,type,specType){
      this.equipDialog = true;
      this.belongSpecRouterType = type;
      this.maintainAreaId = val;
      if (type) {
        this.belongSpecType = specType
        // if (val == 'A') {
        //   this.belongSpecType = specType;
        // } else if (val == 'Z') {
        //   this.belongSpecType = specType
        // } else {
        //   this.belongSpecType = specType
        // }
      } else {
        this.currentRow = row;
        if (val == 'A') {
          this.belongSpecType = row.aBelongSpec
        } else {
          this.belongSpecType = row.zBelongSpec
        }
      }
      
      
    },
    // 拼接路由查询
    splictRoutr(id,name){
      this.equipLoading = true;
        this.tableSplicingCircuits = [{
          displayName: '电路ID',
          columnName: 'circuitId'
        },{
            displayName: 'A端网元名称',
            columnName: 'atrsNeName'
        },{
            displayName: 'A端网元类型',
            columnName: 'aeqpType'
        },{
          displayName: 'A端端口',
          columnName: 'aportName'
        },{
          displayName: 'Z端网元名称',
          columnName: 'ztrsNeName'
        },{
            displayName: 'Z端网元类型',
            columnName: 'zeqpType'
        },{
            displayName: 'Z端端口',
            columnName: 'zportName'
        },{
            displayName: '主备',
            columnName: 'isbak'
        }]
        // if (name) {
          this.splictRoutrDialog = true;
          this.$api.coreApi.getTransRouteImport({circuitName:name})
            .then(res=>{
              this.splicingCircuits = res.data;
              this.equipLoading = false;
              
            })
        // }
        return this.splicingCircuits
      },
    showDragHint(message) {
      this.$message.warning({
        message,
        duration: 1500,
        showClose: true
      });
    },
    // 计算表格高度
    calcTableHeight() {
      const container = this.$refs.tableContainer;
      if (!container) return;

      // 1. 获取容器距离视口顶部的距离
      const containerTop = container.getBoundingClientRect().top;
      
      // 2. 计算可用高度（视口高度 - 容器顶部距离 - 其他元素高度）
      const windowHeight = window.innerHeight;
      const otherElementsHeight = 50; // 例如：分页组件高度 + 内边距
      
      this.tableHeight = windowHeight - containerTop - otherElementsHeight;

      // 3. 强制表格重新布局
      this.$nextTick(() => {
        if (this.$refs.table) {
          this.$refs.table.doLayout();
        }
      });
    },

    // 切换全屏
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;
      if (!this.isFullscreen) {
        this.routerHeight = 350
        this.activeNames = ['1','2']
      } else {
        this.routerHeight = 800
        this.activeNames = ['2']
        this.detailTopo = false
      }
    },

    // 处理全屏状态变化（例如用户按 ESC 退出）
    handleFullscreenChange() {
      this.isFullscreen = !!document.fullscreenElement;
      this.calcTableHeight();
    },
    noCustInfor(msg){
      this.noCust = msg
      this.eqpDialog = false;
    },
    noCustInfor1(msg) {
      this.noCust = msg
      this.eqpCndDialog = false;
    },
    handleNeNameChange(val,netGrade){
      console.log(val,netGrade);
      const params = {netGrade:netGrade,
        neId:val.neId, 
        neName:val.neName
        };
      this.$api.transApi.updateAceDevice(params)
        .then((result) => {
          if (result.code == 200) {
             this.$message({
               message: '修改成功',
               type: 'success'
             });
          } else {
            this.$message.error('修改失败');
          }
          this.getList()
        })
      
    },
    goTopo(msg){
      // this.$router.replace({ name: 'topoPage' ,params: {circuitName: msg[0].circuitName}});
      const routeData = this.$router.resolve({
        name: 'topoPage',
        query: { circuitName: msg[0].circuitName }
      });
      window.open(routeData.href, '_blank');
    },
    setOperationColumnClass({ row, column, rowIndex }) {
      
      if (column.fixed) {
        return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
      }
      return '';
    },
    notPassed(row,auditStatus){
      let uniqueIds = [] 
      row.routeDetailVOList.map(item=>{
        uniqueIds.push(item.uniqueId)
      })
      this.$prompt('请输入审核原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        }).then(({ value }) => {
          let params ={
              "auditStatus": auditStatus,
              "auditReason": value,
              "circuitName": row.circuitName,
              "circuitId": row.id,
              "routeIds": uniqueIds
          }
          this.$api.transApi.updateAuditInfo(params)
          .then((result) => {
            if (result.code == 200) {
              this.getList();
              this.$message({
                type: 'success',
                message: result.msg
              });
            }
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          });       
        });
    },

      openDetailTopo(){
        this.detailTopo = !this.detailTopo
        if (this.detailTopo) {
          this.activeNames = ['2','3']
        } else {
          this.activeNames = ['2','1']
        }
        
      }
  
  }
  
}
</script>

<style scoped lang="less">
::v-deep .nc-query .el-form-item__content {
  width: 220px;
}
/* 关键样式 */
.table-container {
  height: 100vh; /* 容器占满整个视口 */
  display: flex;
  flex-direction: column;
  padding: 20px;
  box-sizing: border-box;
}

.el-table {
  flex: 1; /* 表格占据剩余空间 */
  margin-bottom: 10px; /* 分页组件间距 */
}

.el-pagination {
  flex-shrink: 0; /* 分页组件不压缩 */
}
.custom-cascader ::v-deep(.el-cascader-menu__wrap) {
      background-color: black; /* 你可以根据需要修改颜色 */
    }
.alarm-monitor {
  width: 100%;
  overflow: hidden;
  position: relative;

  .container {
    background-color: transparent;
  }

  .muen-left {
    height: 85vh;
    margin: 20px 20px 0 20px;
    // background: url("../../assets/images/menu_left.png") center center no-repeat;
    // background-size: 100vw 100vh;
    background: url("../../assets/images/menu_left.png") center center no-repeat;
      background-size: 100% 100%;
    overflow: auto;
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 0Px #163479 !important;
      border-radius: 10Px;
      background-color: #040B1D !important;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 0px #4C72C9;
      background: linear-gradient(to top, #040B1D 0%, #4C72C9 20%,#4C72C9 80%, #040B1D 100%);
    }
    &::-webkit-scrollbar {
      width: 3px; // 横向滚动条
      height: 1px; // 纵向滚动条 必写
    }
  }

  .menu-bg {
    background: url("../../assets/images/menu-bg-yes.png") center center no-repeat;
    background-size: 100% 100%;
    height: 50px;
    margin: 25px;
    font-size: 20px;
    text-align: center;
    line-height: 50px;
  }

  ::v-deep .el-menu {
    border: none;
    background-color: transparent;
    margin: 0 20px;
  }

  ::v-deep .tabHeader .el-checkbox {
    width: 20%;
    overflow: hidden;
  }

  ::v-deep .tabSel .el-checkbox__inner {
    background-color: transparent;
  }

  ::v-deep .el-menu-item {
    background-color: rgba(9, 33, 86, .8);
    color: #fff;
    text-align: center;
    border-bottom: 1px rgb(7 13 26) solid;
    border-radius: 5px;
    width: 100%;
    min-width: 100px;

    &:hover {
      color: #fff;
      background: rgba(15, 52, 124, .5);

      .star {
        color: #333333;
      }
    }

    &.is-active {
      color: #fff;
      background: rgba(15, 52, 124, .5);

      & span {
        color: #ffffff;
      }

      & .star {
        color: #ffffff;
      }

      & :hover .star {
        color: #ffffff;
      }
    }
  }

  .content_right {
    height: 87vh;
    // margin: 20px 0px 0 0;
    padding: 0 30px;
    background: url("../../assets/images/content_right.png") center center no-repeat;
    background-size: 100% 100%;
    overflow: auto;
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 0Px #163479 !important;
      border-radius: 10Px;
      background-color: #040B1D !important;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 0px #4C72C9;
      background: linear-gradient(to top, #040B1D 0%, #4C72C9 20%,#4C72C9 80%, #040B1D 100%);
    }
    &::-webkit-scrollbar {
      width: 3px; // 横向滚动条
      height: 1px; // 纵向滚动条 必写
    }
    .nc-query {
      border-radius: 2px;
      background-color: rgba(23, 70, 137, 0.25);
      padding: 0px 16px 12px;
      // margin: 25px 0px;
      margin: 25px 0px 10px 0px;
      /deep/.el-form-item{
        margin-bottom: 5px;
        .el-form-item__label-wrap{
          margin: 0;
        }
      }
      ::v-deep .el-select {
        width: 100%;
      }
    }
    .table-class ::v-deep(.el-table__body-wrapper) {
     // height: 91% !important;
    }
    /deep/.el-form-item__label {
      color: #fff;
    }

    /deep/.el-input__inner {
      background-color: transparent;
      border: 1px solid rgba(37, 190, 247, 0.5);
      padding: 0 15px !important;
    }

    /deep/.el-button--primary {
      color: #FFF;
      background-color: #409EFF;
      border-color: #409EFF;
    }

    /deep/.el-button--default {
      color: #409EFF;
      background-color: transparent;
      border-color: #409EFF;
    }
  }

  ::v-deep {
    .el-table,
    .el-table__expanded-cell {
      background-color: transparent !important;
    }
    .el-collapse-item__header .el-collapse-item__arrow {
      display: none !important;
    }
    .el-table thead.is-group th.el-table__cell {
      background-color: transparent !important;
    }
    .el-table__header-wrapper{
      // height: 30% !important;
      .el-table__header{
        // height: 100% !important;
      }
    }
    .el-table__body-wrapper {
      height: 87% !important;
    }
    /*定义滚动条轨道 内阴影+圆角*/
    .el-table__body-wrapper::-webkit-scrollbar-track {
      box-shadow: inset 0 0 0Px #163479 !important;
      border-radius: 10Px;
      background-color: #163479 !important;
    }

    .el-table tr {
      color: #fff;
      background-color: transparent;
      cursor: pointer;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: #06529d !important;
      cursor: pointer;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped:hover>td.el-table__cell {
      background-color: #06529d !important;
      cursor: pointer;
    }

    .el-table--striped {
      background-color: rgba(12, 33, 87, 0) !important;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
      background: rgba(12, 21, 69, 1);
    }

    .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
      width: 10Px;
    }

    /*定义滑块 内阴影+圆角*/
    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      border-radius: 10Px;
      box-shadow: inset 0 0 0Px #4C72C9;
      background: linear-gradient(-55deg, #4C72C9 0%, #4C72C9 100%);
    }

    .el-table__body-wrapper::-webkit-scrollbar {
      width: 5Px; // 横向滚动条
      height: 5Px; // 纵向滚动条 必写
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: transparent;
    }

    .el-table--border th.el-table__cell.gutter {
      display: none;
    }

    /* 表格鼠标悬浮时的样式（高亮） */
    .el-table--enable-row-hover .el-table__body tr:hover {
      background-color: rgba(255, 255, 255, 0);
    }

    /*表格鼠标悬停的样式（背景颜色）*/
    .el-table tbody tr:hover>td {
      background-color: rgba(255, 255, 255, 0);
    }

    .el-table__body .el-table__row.hover-row td {
      background-color: #0f2444;
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      border: 1px solid transparent;
    }

    .el-table--border .el-table__cell,
    .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
      border: 1px solid transparent;
    }

    .el-table tr:nth-child(even) {
      background: rgba(40, 72, 121, 0.29);
    }
    .table-list {
      .el-table__fixed-right {
        right: 0px !important;
      }
      .el-table__fixed-right::before, .el-table__fixed::before {
        height: 0px ;
      }
      .even-row {
        background-color: #04152e;
      }
      .odd-row {
        background: #0f2444;
      }
      .el-table__body .el-table__row.hover-row td {
        // background-color: rgba(255, 255, 255, 0);
      }
    }
    

    .custDialog .el-dialog {
      width: 75%;
      // height: 77%;
      padding-bottom: 40px;
    }

    .equipDialog .el-dialog {
      width: 65%;
      // height: 75%;
      padding-bottom: 40px;
    }

    

    .trsCircuit .el-dialog {
      width: 75%;

      .el-form {
        min-height: 65px;
        width: 100%;

        .el-range-editor .el-range-input {
          background: transparent;
        }

        .el-date-editor .el-range-separator {
          color: #eee;
        }

        .el-date-editor .el-range-input {
          color: #fff;
        }

        
      }
    }
    .router .el-dialog {
      width: 95%;
      margin-top: 2vh !important;
      .el-table {
        .el-form-item {
          // float: left;
          margin-bottom: 0px;
        }
      }
      
      /* 提升拖拽元素层级 */
.drag-handle {
  // display: block !important; /* 确保块级显示 */
  // width: 24px;
  // height: 24px;
  margin: -6px; /* 扩大点击区域 */
  padding: 12px; /* 触摸友好 */
  position: relative;
  z-index: 9999;
  
  /* 防止内容选中 */
  user-select: none;
  -webkit-user-drag: none;
}
.disabled-drag {
  cursor: not-allowed !important;
  opacity: 0.6;
  background: #f5f7fa;
}


/* 消除元素间隔影响 */
.el-table__body-wrapper {
  padding: 0 !important;
  margin: 0 !important;
}

/* 拖拽时强制层级 */
.sortable-drag-force {
  transform: translate3d(0,0,100px) !important;
  box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}
      }

    .el-dialog {
      width: 55%;
      background: url("../../assets/images/dialogbg.png") center center no-repeat;
      background-size: 100% 100%;
      margin-top: 10vh !important;
    }

    .el-dialog__header {
      justify-content: left;
      background: none;
    }

    .el-dialog__title {
      justify-content: left;
      line-height: 20px;
      font-weight: 800;
      height: 20px;
      display: flex;
      color: rgba(255, 255, 255, 1);
      font-size: 22px;
    }

    .el-dialog__title::before,
    .el-dialog__title::after {
      content: none;
    }

    .el-dialog__headerbtn {
      width: 30px;
      height: 30px;
      float: right;
      top: 10px;
      right: 10px;
      background: url("../../assets/images/x.png") center center no-repeat;
      background-size: 100% 100%;
    }

    .el-dialog__body {
      height: 85%;
      padding: 5px 20px 0;
      .detail-info {
        max-height: 800px;
        height: 100%;
      }

      .el-form {
        min-height: 100px;
        width: 95%;
        // text-align: center;

        .el-form-item {
          // float: left;
        }

        .el-form-item__label {
          color: #fff;
          width: 130px;
          line-height: 20px;
        }

        .el-input__inner {
          color: #02dbff;
          background: none;
          border: 1px solid rgba(37, 190, 247, 0.5);
          line-height: 24px;
        }

        .el-switch__label,
        .el-checkbox {
          color: #fff;
        }

        .el-button--primary {
          color: #FFF;
          background-color: #409EFF;
          border-color: #409EFF;

          &:focus {
            background-color: #409EFF;
            border-color: #409EFF;
          }

          &:hover {
            background-color: #409EFF;
            border-color: #409EFF;
          }
        }

        .el-select-dropdown__item:hover {
          color: #409EFF;
          background: #40a0ff27;
        }

        .el-button--default {
          color: #409EFF;
          background: transparent;
          border-color: #409EFF;
        }

        .el-input.is-disabled .el-input__inner {
          background-color: rgba(238, 238, 238, 0.116);
        }
      }
    }

    .el-collapse {
      border: none;
      color: #fff;

      .el-collapse-item__header {
        position: relative;
        background-color: transparent;
        color: #fff;
        border: none;
      }

      .el-collapse-item__wrap {
        background-color: transparent;
        border: none;
      }

      .el-collapse-item__content {
        padding: 0;
      }
    }




  }
  
  /deep/.el-select-dropdown__item.hover {
    color: #fff;
    background: rgba(4, 56, 226, 0.29);
  }
  /deep/.el-scrollbar .el-scrollbar__view .el-select-dropdown__wrap {
    color: #fff;
    background: rgba(4, 56, 226, 0.29);
  }
  /deep/.el-pagination.is-background .btn-next,
  .el-pagination.is-background .btn-prev,
  .el-pagination.is-background .el-pager li {
    background-color: transparent;
    border: 1px solid rgba(4, 56, 226, 0.58);
  }

  /deep/.el-pagination__total {
    color: #fff;
  }

  /deep/.el-input__inner {
    color: #fff;
    background-color: transparent;
    border: 1px solid rgba(4, 56, 226, 0.58);
  }

  .search:hover {
    color: #409EFF;
  }

  /deep/.el-tabs__item {
    color: #FFF;
  }

  /deep/.el-tabs__item:hover {
    color: #409EFF;
  }

  /deep/.el-tabs__item.is-active {
    color: #409EFF;
  }

  /deep/.el-tabs--left .el-tabs__item.is-left {
    height: 100px;
    text-align: center;
  }

  /deep/ .formImport .el-form-item__content {
    width: 460px;
  }

  .tabTitle {
    color: #fff;
  }

  /deep/.el-pager li {
    background-color: transparent;
  }

  /deep/ .el-pagination button:disabled {
    background-color: transparent;
  }

  /deep/.el-pagination .btn-next,
  .el-pagination .btn-prev {
    background: center center no-repeat transparent;
  }
}

::v-deep .el-submenu__title {
  color: #fff;

  &:hover {
    background-color: #40a0ff27;
  }
}
::v-deep .el-menu-item{
  padding-left: 0 !important;
  padding-right: 0;
  text-align: center;
}
.topo{
	::v-deep .el-dialog {
			height: 60vh;
      width: 85%;
			background: url("../../assets/images/dialogbg.png") center center
        no-repeat;
      background-size: 100% 100%;
		}
}

</style>