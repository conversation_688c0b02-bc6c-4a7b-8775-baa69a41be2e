/**
 *  城市坐标修复工具类
 */
export function changePosition(center,name) {
    let zCewnter =[];
    if(name === "咸阳市"){
        zCewnter.push(center[0]-0.25);
        zCewnter.push(center[1]+0.45);
        return zCewnter;
    }else if(name === "铜川市"){
        zCewnter.push(center[0]+0.13);
        zCewnter.push(center[1]+0.30);
        return zCewnter;
    }else if(name === "榆林市"){
        zCewnter.push(center[0]+0.45);
        zCewnter.push(center[1]+0.85);
        return zCewnter;
    }else if(name === "渭南市"){
        zCewnter.push(center[0]+0.5);
        zCewnter.push(center[1]+0.45);
        return zCewnter;
    }else if(name === "西安市"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]-0.15);
        return zCewnter;
    }else if(name === "商洛市"){
        zCewnter.push(center[0]+0.1);
        zCewnter.push(center[1]-0.15);
        return zCewnter;
    }else if(name === "安康市"){
        zCewnter.push(center[0]+0.05);
        zCewnter.push(center[1]-0.20);
        return zCewnter;
    }else if(name === "渭南市"){
        zCewnter.push(center[0]-1.2);
        zCewnter.push(center[1]-0.25);
        return zCewnter;
    }else if(name === "府谷县"){  //榆林市
        zCewnter.push(center[0]-0.15);
        zCewnter.push(center[1]+0.30);
        return zCewnter;
    }else if(name === "榆阳区"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.25);
        return zCewnter;
    }else if(name === "横山区"){
        zCewnter.push(center[0]+0.15);
        zCewnter.push(center[1]-0.15);
        return zCewnter;
    }else if(name === "靖边县"){
        zCewnter.push(center[0]-0.1);
        zCewnter.push(center[1]-0.15);
        return zCewnter;
    }else if(name === "定边县"){
        zCewnter.push(center[0]-0.13);
        zCewnter.push(center[1]-0.18);
        return zCewnter;
    }else if(name === "佳县"){
        zCewnter.push(center[0]-0.13);
        zCewnter.push(center[1]);
        return zCewnter;
    }else if(name === "清涧县"){
        zCewnter.push(center[0]+0.22);
        zCewnter.push(center[1]+0.01);
        return zCewnter;
    }else if(name === "吴堡县"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]-0.03);
        return zCewnter;
    }else if(name === "子洲县"){
        zCewnter.push(center[0]-0.23);
        zCewnter.push(center[1]-0.23);
        return zCewnter;
    }else if(name === "安塞区"){  //延安市
        zCewnter.push(center[0]-0.13);
        zCewnter.push(center[1]);
        return zCewnter;
    }else if(name === "志丹县"){
        zCewnter.push(center[0]-0.23);
        zCewnter.push(center[1]-0.03);
        return zCewnter;
    }else if(name === "吴起县"){
        zCewnter.push(center[0]-0.23);
        zCewnter.push(center[1]+0.05);
        return zCewnter;
    }else if(name === "富县"){
        zCewnter.push(center[0]-0.28);
        zCewnter.push(center[1]-0.18);
        return zCewnter;
    }else if(name === "黄陵县"){
        zCewnter.push(center[0]-0.38);
        zCewnter.push(center[1]-0.08);
        return zCewnter;
    }else if(name === "洛川县"){
        zCewnter.push(center[0]+0.08);
        zCewnter.push(center[1]-0.20);
        return zCewnter;
    }else if(name === "黄龙县"){
        zCewnter.push(center[0]+0.12);
        zCewnter.push(center[1]);
        return zCewnter;
    }else if(name === "子长市"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.12);
        return zCewnter;
    }else if(name === "王益区"){  //铜川市
        zCewnter.push(center[0]);
        zCewnter.push(center[1]-0.02);
        return zCewnter;
    }else if(name === "耀州区"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.12);
        return zCewnter;
    }else if(name === "富平县"){ //渭南市
        zCewnter.push(center[0]-0.12);
        zCewnter.push(center[1]+0.05);
        return zCewnter;
    }else if(name === "白水县"){
        zCewnter.push(center[0]-0.05);
        zCewnter.push(center[1]+0.15);
        return zCewnter;
    }else if(name === "潼关县"){
        zCewnter.push(center[0]+0.10);
        zCewnter.push(center[1]-0.10);
        return zCewnter;
    }else if(name === "华阴市"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]-0.15);
        return zCewnter;
    }else if(name === "华州区"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]-0.15);
        return zCewnter;
    }else if(name === "韩城市"){
        zCewnter.push(center[0]-0.02);
        zCewnter.push(center[1]-0.01);
        return zCewnter;
    }else if(name === "镇安县"){ //商洛市
        zCewnter.push(center[0]-0.02);
        zCewnter.push(center[1]-0.11);
        return zCewnter;
    }else if(name === "洛南县"){
        zCewnter.push(center[0]+0.05);
        zCewnter.push(center[1]+0.11);
        return zCewnter;
    }else if(name === "商南县"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]-0.11);
        return zCewnter;
    }else if(name === "杨陵区"){ //咸阳市
        zCewnter.push(center[0]-0.06);
        zCewnter.push(center[1]-0.06);
        return zCewnter;
    }else if(name === "三原县"){
        zCewnter.push(center[0]+0.18);
        zCewnter.push(center[1]+0.05);
        return zCewnter;
    }else if(name === "渭城区"){
        zCewnter.push(center[0]-0.02);
        zCewnter.push(center[1]-0.11);
        return zCewnter;
    }else if(name === "秦都区"){
        zCewnter.push(center[0]+0.12);
        zCewnter.push(center[1]+0.01);
        return zCewnter;
    }else if(name === "千阳县"){ //宝鸡市
        zCewnter.push(center[0]+0.01);
        zCewnter.push(center[1]+0.14);
        return zCewnter;
    }else if(name === "凤县"){
        zCewnter.push(center[0]+0.12);
        zCewnter.push(center[1]+0.05);
        return zCewnter;
    }else if(name === "金台区"){
        zCewnter.push(center[0]+0.01);
        zCewnter.push(center[1]-0.08);
        return zCewnter;
    }else if(name === "渭滨区"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.08);
        return zCewnter;
    }else if(name === "麟游县"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.10);
        return zCewnter;
    }else if(name === "汉台区"){ //汉中市
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.10);
        return zCewnter;
    }else if(name === "宁陕县"){ //安康市
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.20);
        return zCewnter;
    }else if(name === "白河县"){
        zCewnter.push(center[0]-0.15);
        zCewnter.push(center[1]-0.05);
        return zCewnter;
    }else if(name === "阎良区"){ //西安市
        zCewnter.push(center[0]+0.05);
        zCewnter.push(center[1]+0.05);
        return zCewnter;
    }else if(name === "临潼区"){
        zCewnter.push(center[0]+0.15);
        zCewnter.push(center[1]+0.05);
        return zCewnter;
    }else if(name === "灞桥区"){
        zCewnter.push(center[0]+0.08);
        zCewnter.push(center[1]+0.05);
        return zCewnter;
    }else if(name === "未央区"){
        zCewnter.push(center[0]-0.02);
        zCewnter.push(center[1]+0.08);
        return zCewnter;
    }else if(name === "雁塔区"){
        zCewnter.push(center[0]-0.02);
        zCewnter.push(center[1]);
        return zCewnter;
    }else if(name === "碑林区"){
        zCewnter.push(center[0]+0.02);
        zCewnter.push(center[1]);
        return zCewnter;
    }else if(name === "莲湖区"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.01);
        return zCewnter;
    }else if(name === "新城区"){
        zCewnter.push(center[0]);
        zCewnter.push(center[1]+0.02);
        return zCewnter;
    }
    return center;
}

/**
 * 城市大小位置调整，名称位置调整*
 * @param curCityArr
 * @param mapCenter
 * @returns {{}}
 */
export function changeCity(curCityArr,mapCenter){
    let Obj = {};
    //下钻数据修正
    if (curCityArr[0].properties.name === "榆林市") {
        Obj.scaleBlowUp = 800;
        Obj.scaleBlowUp2 = 800;
        Obj.topMap = '45px';
        Obj.leftMap = '130px';
        Obj.mapCenter=[mapCenter[0]-0.25,mapCenter[1]];
    }else if(curCityArr[0].properties.name === "延安市") {
        Obj.scaleBlowUp = 1200;
        Obj.scaleBlowUp2 = 1200;
        Obj.topMap = '35px';
        Obj.leftMap = '80px';
        Obj.mapCenter=[mapCenter[0],mapCenter[1]];
    }else if(curCityArr[0].properties.name === "铜川市") {
        Obj.scaleBlowUp = 3000;
        Obj.scaleBlowUp2 = 3000;
        Obj.topMap = '35px';
        Obj.leftMap = '80px';
        Obj.mapCenter=[mapCenter[0]+0.10,mapCenter[1]+0.3];
    }else if(curCityArr[0].properties.name === "渭南市") {
        Obj.scaleBlowUp = 1800;
        Obj.scaleBlowUp2 = 1800;
        Obj.topMap = '25px';
        Obj.leftMap = '70px';
        Obj.mapCenter=[mapCenter[0]+0.45,mapCenter[1]+0.5];
    }else if(curCityArr[0].properties.name === "商洛市") {
        Obj.scaleBlowUp = 1400;
        Obj.scaleBlowUp2 = 1400;
        Obj.topMap = '60px';
        Obj.leftMap = '80px';
        Obj.mapCenter=[mapCenter[0]+0.1,mapCenter[1]];
    }else if(curCityArr[0].properties.name === "咸阳市") {
        Obj.scaleBlowUp = 2000;
        Obj.scaleBlowUp2 = 2000;
        Obj.topMap = '40px';
        Obj.leftMap = '50px';
        Obj.mapCenter=[mapCenter[0]-0.1,mapCenter[1]+0.5];
    }else if(curCityArr[0].properties.name === "宝鸡市") {
        Obj.scaleBlowUp = 1600;
        Obj.scaleBlowUp2 = 1800;
        Obj.topMap = '40px';
        Obj.leftMap = '90px';
        Obj.mapCenter=[mapCenter[0]+0.2,mapCenter[1]];
    }else if(curCityArr[0].properties.name === "汉中市") {
        Obj.scaleBlowUp = 1200;
        Obj.scaleBlowUp2 = 1400;
        Obj.topMap = '40px';
        Obj.leftMap = '90px';
        Obj.mapCenter=[mapCenter[0]+0.2,mapCenter[1]+0.10];
    }else if(curCityArr[0].properties.name === "安康市") {
        Obj.scaleBlowUp = 1400;
        Obj.scaleBlowUp2 = 1600;
        Obj.topMap = '40px';
        Obj.leftMap = '80px';
        Obj.mapCenter=[mapCenter[0]+0.2,mapCenter[1]];
    }else if(curCityArr[0].properties.name === "西安市") {
        Obj.scaleBlowUp = 1800;
        Obj.scaleBlowUp2 = 2000;
        Obj.topMap = '40px';
        Obj.leftMap = '80px';
        Obj.mapCenter=[mapCenter[0]-0.1,mapCenter[1]];
    }
    return Obj;
}

/**
 * 鼠标抓取修正
 * @param name
 * @param mouse
 */
export function cylinderCity(intersects,mouse,innerWidth,innerHeight,event){
    if(intersects[0].object.name === '西安市' ){
        let w = innerWidth-50;
        let h = innerHeight+45;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '铜川市') {
        let w = innerWidth-50;
        let h = innerHeight+55;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '咸阳市') {
        let w = innerWidth-30;
        let h = innerHeight+50;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '渭南市') {
        let w = innerWidth+80;
        let h = innerHeight+50;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '延安市') {
        let w = innerWidth-52;
        let h = innerHeight+150;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '榆林市') {
        let w = innerWidth-70;
        let h = innerHeight+320;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '汉中市') {
        let w = innerWidth-0;
        let h = innerHeight+160;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '安康市') {
        let w = innerWidth-50;
        let h = innerHeight-30;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '商洛市') {
        let w = innerWidth-85;
        let h = innerHeight-25;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }else if(intersects[0].object.name === '宝鸡市') {
        let w = innerWidth+80;
        let h = innerHeight+45;
        mouse.x = (event.clientX / w * 0.87) * 2 - 1;
        mouse.y = -(event.clientY / h * 0.905) * 2 + 1;
    }
    return mouse;
}
