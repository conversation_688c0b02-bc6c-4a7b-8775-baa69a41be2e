<!--登录页-->
<template>
<div class="login">
  <div class="bodybox">
    <div class="login-body">
      <div class="f_login">
        <div class="login-formHeader">核心网业务监控系统</div>
        <div class="inputArea">
          <div class="inputArea_header">账户登录</div>
          <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="0" class="formPos">
            <el-form-item ref="username" label="" prop="username">
              <el-input v-model="formData.username" placeholder="请输入用户名" clearable autocomplete="off">
                <i slot="prefix" class="i-slot-c">
                  <img src="../assets/images/kams_user.png" alt="">
                  <div style="margin-left: 10px">账户</div>
                  <div class="line-u"></div>
                </i>
              </el-input>
            </el-form-item>

            <el-form-item ref="password" label="" prop="password">
              <el-input placeholder="密码" clearable type="password" v-model="formData.password" autocomplete="off"
                :show-password="true" @keyup.enter.native="onSubmit">
                <i slot="prefix" class="i-slot-c">
                  <img src="../assets/images/kams_password.png" alt="">
                  <div style="margin-left: 10px">密码</div>
                  <div class="line-u"></div>
                </i>
              </el-input>
            </el-form-item>
            <el-button type="primary" class="login-btn" @click="onSubmit" :loading="loading">
              登录
            </el-button>


            <div class="btns">
              <el-button type="text" @click="forgotPw" style="color:#fff">
                忘记密码?
              </el-button>
            </div>
          </el-form>

        </div>
        <el-dialog title="修改密码" center :visible.sync="dialogFormVisible" @close="cancel">
          <el-form ref="ruleForm" :model="formData" label-width="0" class="formPos">
            <el-form-item ref="userAccount" label="" prop="userAccount">
              <el-input v-model="formUpdata.userAccount" placeholder="账户" clearable autocomplete="off">
                <i slot="prefix" class="i-slot-c">
                  <img src="../assets/images/kams_user.png" alt="">
                  <div style="margin-left: 10px">账户</div>
                  <div class="line-u"></div>
                </i>
              </el-input>
            </el-form-item>
            <el-form-item ref="phone" label="" prop="phone">
              <el-input v-model="formUpdata.phone" placeholder="手机号码" clearable autocomplete="off">
                <i slot="prefix" class="i-slot-c">
                  <img src="../assets/images/kams_phone.png" alt="" style="width: 12px;">
                  <div style="margin-left: 10px">电话</div>
                  <div class="line-u"></div>
                </i>
              </el-input>
            </el-form-item>
            <div v-if="isCode">
              <el-form-item ref="username" label="" prop="username">
                <el-input v-model="formUpdata.password" placeholder="密码" clearable autocomplete="off">
                  <i slot="prefix" class="i-slot-c">
                    <img src="../assets/images/kams_password.png" alt="">
                    <div style="margin-left: 10px">密码</div>
                    <div class="line-u"></div>
                  </i>
                </el-input>
              </el-form-item>
              <el-form-item ref="username" class="confirmPassword" label="" prop="username">
                <el-input v-model="formUpdata.newPassword" placeholder="确认密码" clearable autocomplete="off">
                  <i slot="prefix" class="i-slot-c">
                    <img src="../assets/images/kams_password.png" alt="">
                    <div style="margin-left: 10px">确认密码</div>
                    <div class="line-u"></div>
                  </i>
                </el-input>
              </el-form-item>
              <i style="color: #ef3f3f;"> <i class="el-icon-warning-outline" /> 请至少使用字母、数字、符号三种类型组合的密码，长度为8~20位。</i>

            </div>

            <el-form-item v-else ref="phoneCode" class="phoneCode" label="" prop="phoneCode">
              <el-input placeholder="请输入短信验证码" v-model="formUpdata.phoneCode" autocomplete="off">
                <i slot="suffix" class="i-slot-c">
                  <button @click.prevent="getCode()" class="code-btn" :disabled="!show">
                    <span v-show="show">获取验证码</span>
                    <span v-show="!show" class="count">{{ count }} s</span>
                  </button>
                </i>
              </el-input>
            </el-form-item>
          </el-form>
          <div v-if="isCode" slot="footer" class="dialog-footer" style="margin-top: 185px">
            <button class="code-btnqx" @click="cancel">取 消</button>
            <button class="code-btnUpdata" type="primary" @click="changePassword">修改密码</button>
          </div>
          <div v-else slot="footer" class="dialog-footer" style="margin-top: 85px">
            <button class="code-btnqx" @click="cancel">取 消</button>
            <button class="code-btnUpdata" type="primary" @click="codeIS">下一步</button>
          </div>

        </el-dialog>
      </div>
    </div>
  </div>
</div>
</template>

<script>
import axios from "axios";
import { requestUrl } from "@/utils/ajax";
import { encrypt } from "@/assets/js/jsencrypt.js";

export default {
  name: "login",
  data() {
    return {
      isCode: false,
      show: true,
      count: 60,
      dialogFormVisible: false,
      key: "1",
      loading: false,
      formData: {
        username: "",
        password: "",
      },
      formUpdata: {
        userAccount: "",
        phone: "",
        password: "",
        phoneCode: "",
        newPassword: ""
      },
      rules: {
        username: [
          {
            required: true,
            message: "请输入用户名！",
            trigger: "blur",
          },
        ],
        password: [
          {
            required: true,
            message: "请输入密码！",
            trigger: "blur",
          },
        ],
      },
      menuListM: ['重保大屏', '资源管理', '告警监控', '工单管理', '业务路由', '运营管理', '系统管理']
    };
  },
  methods: {
    /**
     * 外链
     */
    link(idx) {
      console.log("待开发 —— 跳转外链：", idx);
    },
    /**
     * 登录
     */
    onSubmit() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          try {
            let formData = new FormData();
            for (var key in this.formData) {
              formData.append(key, encrypt(this.formData[key]));
            }
            const res = await axios({
              method: "post",
              url: `${requestUrl}/core-network/otnUser/login`,
              data: formData,
              headers: {
                "content-type": "multipart/form-data",
              },
            });
            if (res.data.resultCode == 200) {
              
              localStorage.setItem("nmgjpwToken", res.data.data);
              localStorage.setItem("user", JSON.stringify(this.formData.username));
              
              this.$api.transApi.selectRoleInfo()
              .then((result) => {
                if (result.resultCode == 200) {
                  let roleList = []
                  result.data.map(item=>{
                    roleList.push(item.role_name)
                  })
                 localStorage.setItem("role", JSON.stringify(roleList));
                }
              })
              let router = '';
              this.$api.slaApi.getMenuByUser({
                type: '0',
                "pageNum": 1,
                "pageSize": 10,
              })
                .then(res => {
                  let _this = this;
                  res.data.sort((prev, next) => {
                    return _this.menuListM.indexOf(prev.title) - _this.menuListM.indexOf(next.title)
                  })
                  router = res.data[0].idx
                  // console.log(res.data[0].path);
                  // this.$router.push({ name: 'bigPictureCircuitName' });
                  this.$router.push({ path: res.data[0].path });
                })

              this.$store.dispatch("changeMenu", 22);

              this.$api.slaApi.getMenuPointByUser({
                type: '0',
                "pageNum": 1,
                "pageSize": 10,
              })
                .then(res => {
                  let all = []
                  res.data.map(item => {
                    all.push(item.id)
                  })
                  localStorage.setItem('permissions', all)
                })
            } else {
              if (res.data.resultCode === 500 || res.status === 502) {
                this.$alert(res.data.message, "提示", {
                  confirmButtonText: "关闭",
                  type: "error",
                  closeOnClickModal: false,
                  callback: () => { },
                });
                this.$router.push({ name: "login" });
              }
            }
          } catch (err) {
            console.error(err);
          }
        } else {
          return false;
        }
      });
    },
    /**
     * 注册
     */
    signIn() {
      console.log("待开发 —— 注册");
    },
    /**
     * 忘记密码
     */
    forgotPw() {
      this.dialogFormVisible = true;
      this.formUpdata.username = this.formData.username;
      console.log("待开发 —— 忘记密码");
    },
    // 获取短信验证码
    getCode() {
      //axios请求
      if (this.formUpdata.userAccount) {
        let params = {
          userAccount: this.formUpdata.userAccount,
          phone: this.formUpdata.phone,
        }
        this.$api.slaApi.sendMessage(params)
          .then((result) => {
            if (result.resultCode == 200) {
              this.$message({
                showClose: true,
                duration: 2000,
                type: "success",
                message: result.data,
              });
              // 验证码倒计时
              if (!this.timer) {
                this.count = 60;
                this.show = false;
                this.timer = setInterval(() => {
                  if (this.count > 0 && this.count <= 60) {
                    this.count--;
                  } else {
                    this.show = true;
                    clearInterval(this.timer);
                    this.timer = null;
                  }
                }, 1000);
              }
            } else {
              this.$message({
                showClose: true,
                duration: 2000,
                type: "error",
                message: result.message,
              });
            }
          });

      } else {
        this.$message({
          showClose: true,
          duration: 2000,
          type: "error",
          message: "账号不能为空",
        });
      }

    },
    // 验证验证码
    codeIS() {
      if (this.formUpdata.phoneCode) {
        let params = {
          userName: this.formUpdata.phone,
          auth: this.formUpdata.phoneCode
        }
        this.$api.slaApi.checkAuth(params)
          .then((result) => {
            // console.log(result);
            // this.isCode = true
            if (result.resultCode == 200) {
              this.isCode = true
            } else {
              this.$message({
                showClose: true,
                duration: 2000,
                type: "error",
                message: "验证码错误！",
              });
            }
          });
      } else {
        this.$message({
          showClose: true,
          duration: 2000,
          type: "error",
          message: "验证码不能为空",
        });
      }
    },
    cancel() {
      this.show = true;
      this.isCode = false;
      this.dialogFormVisible = false;
      this.formUpdata.username = "";
      this.formUpdata.password = "";
      this.formUpdata.phoneCode = "";
      this.formUpdata.newPassword = "";
    },
    changePassword() {
      // this.dialogFormVisible = false
      //密码为八位及以上并且字母数字特殊字符三项都包括
      var strongRegex = new RegExp("^(?=.{8,})(((?=.*[A-Z])(?=.*[a-z]))|((?=.*[A-Z])(?=.*[0-9]))|((?=.*[a-z])(?=.*[0-9]))|((?=.*[a-z])(?=.*\\W))|((?=.*[0-9])(?=.*\\W))|((?=.*[A-Z])(?=.*\\W))).*$", "g");
      if (this.formUpdata.newPassword) {
        if (this.formUpdata.password == this.formUpdata.newPassword) {
          // console.log(strongRegex.test(this.formUpdata.password));
          if (strongRegex.test(this.formUpdata.password) !== false) {
            let params = {
              "userName": this.formUpdata.userAccount,
              "password": this.formUpdata.password
            }
            this.$api.slaApi.changePassword(params)
              .then((result) => {
                if (result.resultCode == 200) {
                  this.$message({
                    showClose: true,
                    duration: 2000,
                    type: "success",
                    message: result.data,
                  });
                  this.dialogFormVisible = false;
                } else {
                  this.$message({
                    showClose: true,
                    duration: 2000,
                    type: "warning",
                    message: result.data,
                  });
                }
              });
          } else {
            this.$message({
              showClose: true,
              duration: 2000,
              type: "error",
              message: "密码不符合要求",
            });
          }
        } else {
          this.$message({
            showClose: true,
            duration: 2000,
            type: "error",
            message: "两次密码不一致",
          });
        }
      } else {
        this.$message({
          showClose: true,
          duration: 2000,
          type: "error",
          message: "密码不能为空",
        });
      }
    }
  },
  mounted() { },
};
</script>

<style lang="less" scoped>
.login {
  background: url("../assets/img/login/denglu-bgs.png") no-repeat center center;
  width: 100%;
  height: 100%;
  min-height: 700px;
  min-width: 1200px;
  background-size: 100% 100%;
}

.login-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  min-width: 1200px;
  height: 117px;
  background: #ffffff;
  padding: 0 137px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 10;

  .header-logo {
    width: 407px;
    height: 61px;
    background: url("../assets/img/login/logo_new.png") no-repeat center center;
    background-size: 100%;
  }

  .header-links {
    width: 350px;
    height: auto;
    text-align: center;
    font-size: 28px;
    color: #41465a;

    .link {
      cursor: pointer;
    }
  }
}

.bodybox {
  width: 100%;
  height: 100vh;
  min-width: 1200px;
  box-sizing: border-box;
  position: relative;
}

.login-body {
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  height: 520px;
  width: 960px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -220px;
  margin-left: -480px;
  // background: #fff;
  // box-shadow: 2px 4px 4px 0px rgba(0, 0, 0, 0.19);
}

.login-formHeader {
  color: rgb(255, 255, 255);
  // font-size: 4.2vh;
  font-size: 42px;
  text-align: center;
  font-family: PangMenZhengDao;
  font-weight: 800;
  margin-bottom: 50px;
  background-image: -webkit-linear-gradient(bottom, #398ce4, #abd4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.inputArea_header {
  font-size: 32px;
  font-weight: 800;
  position: absolute;
  top: 35px;
  left: 39%;
  background-image: -webkit-linear-gradient(bottom, #398ce4, #abd4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.login-body {
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex-direction: row;
  height: 520px;
  width: 730px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -310px;
  margin-left: -355px;
}

.box_g {
  width: 50%;
  height: 100%;
}

.inputArea {
  // width: 412px;
  width: 80%;
  margin: auto;
  height: 450px;
  // margin: 100px auto 0;
  position: relative;
  background: url("../assets/img/login/zhdl_bg.png") no-repeat center center;
  background-size: 100% 100%;
}

.formPos {
  // width: 55%;
  // position: absolute;
  // top: 30%;
  // left: 23%;
  width: 400px;
  position: absolute;
  top: 30%;
  left: 50%;
  margin-left: -200px;
}

.f_login {
  width: 100%;
}

.f_login_bg {
  background: url("../assets/img/login/chahua.png") no-repeat center center;
  background-size: 100%;
}

::v-deep .el-tabs__item,
.el-tabs__item.is-active {
  color: rgba(18, 93, 172, 1);
  cursor: pointer;
  font-size: 18px;
}

::v-deep .el-tabs__active-bar {
  width: 71px;
  background: rgba(18, 93, 172, 1);
  height: 3px;
}

::v-deep .el-tabs__item:hover {
  color: rgba(18, 93, 172, 1);
  cursor: pointer;
  font-size: 18px;
}

::v-deep .el-tabs__nav-wrap::after {
  height: 1px;
  background-color: #e1e1e1;
  z-index: 1;
}

::v-deep .el-tabs__header {
  margin: 0 0 35px;
}

::v-deep .el-form-item {
  height: 47px;
  background: url("../assets/img/login/input_bg.png") no-repeat center center;
  background-size: 100% 100%;
}

.ipt {
  width: 100%;
  height: 47px;
  line-height: 47px;
  border: none !important;
  outline: none !important;
}

::v-deep .el-input.is-active .el-input__inner {
  border-color: rgba(18, 93, 172, 1);
}

::v-deep .el-input__inner:focus {
  border-color: #d9d9d9;
}

::v-deep .el-input__inner::-webkit-input-placeholder {
  font-size: 16px;
  color: #ccc;
}

::v-deep .el-input--suffix .el-input__inner {
  padding-right: 10px;
  width: 100%;
  height: 100%;
}

::v-deep .el-form-item__error {
  color: red;
  font-size: 14px;
}

.btns {
  width: 270px;
  height: 22px;
  margin: 20px auto;
  text-align: center;
  font-size: 16px;
  // color: #979797;
  color: #fff;
}

.login-btn {
  width: 100%;
  height: 54px;
  font-size: 16px;
  text-align: center;
  border: none;
  color: #ffffff;
  background: rgba(18, 93, 172, 0);
  // border-radius: 28px;
  font-family: PingFangSC-Medium;
  // background-color: #02dbff !important;
  background: url("../assets/img/login/btn_bg.png") no-repeat center center;
  background-size: 100% 100%;
}

::v-deep .el-button--text {
  color: #6f747e;
  margin: 0 22px;
}

::v-deep .el-button--text:focus,
::v-deep .el-button--text:hover {
  color: rgba(18, 93, 172, 1);
}

::v-deep .el-button--primary:focus,
.el-button--primary:hover {
  // background: rgba(18, 93, 172, 0.5);
  border-color: rgba(18, 93, 172, 1);
  color: #ffffff;
  opacity: 0.9;
  background: rgba(18, 93, 172, 0.5);
  background:
    linear-gradient(135deg, transparent 30px, rgba(18, 93, 172, 0.5) 0) top left,
    linear-gradient(-45deg, transparent 10px, rgba(18, 93, 172, 0.5) 0) top right,
    linear-gradient(-45deg, transparent 30px, rgba(18, 93, 172, 0.5) 0) bottom right,
    linear-gradient(135deg, transparent 10px, rgba(18, 93, 172, 0.5) 0) bottom left;
  background-size: 50% 50%;
  background-repeat: no-repeat;
}

::v-deep .el-input .el-input__inner {
  line-height: 44px;
  color: #455a74;
  padding-left: 100px;
  height: 44px;
  border: 1px solid rgba(18, 93, 172, 0);
  color: rgba(225, 233, 249, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
}

::v-deep .el-input .el-input__inner:focus {
  border: 1px solid rgba(18, 93, 172, 0);
}

.i-slot-c {
  height: 32px;
  margin-left: -5px;
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.line-u {
  margin-left: 12px;
  width: 1px;
  height: 15px;
  border-left: 1px solid rgba(37, 190, 247, 1);
}

/deep/.el-input__prefix {
  left: 35px;
}

.btnLeft {
  width: 100%;
  height: 54px;
  background: url("../assets/img/login/btnLeft_bg.png") no-repeat center center;
  background-size: 100% 100%;
}

.inputArea {
  ::v-deep {

    input:-webkit-autofill,
    textarea:-webkit-autofill,
    select:-webkit-autofill {
      -webkit-text-fill-color: #ededed !important;
      -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
      transition: background-color 50000s ease-in-out 0s; //背景色透明  生效时长  过渡效果  启用时延迟的时间
      // background: url("../assets/img/login/input_bg.png") no-repeat center center;
      // background-size: 100% 100%;
    }

    input {
      // background: url("../assets/img/login/input_bg.png") no-repeat center center;
      // background-size: 100% 100%;
      background: transparent
    }
  }
}

::v-deep {
  .el-dialog {
    // height: 400px;
    width: 520px;
    background: url("../assets/img/login/zhdl_bg.png") center center no-repeat;
    background-size: 100% 100%;
    margin-top: 30vh !important;

    .el-input__inner {
      border: 1px solid rgba(18, 93, 172, 0);
      background-color: rgba(1, 48, 122, 0);
    }

    .phoneCode {
      .el-input__inner {
        color: #fff;
        padding-left: 35px;
      }
    }

    .confirmPassword {
      .el-input__inner {
        color: #fff;
        padding-left: 135px;
      }

      margin-bottom: 5px;
    }

    // .el-input .el-input__inner {
    //     line-height: 44px;
    //     color: #455a74;
    //     padding-left: 100px;
    //     // width: 360px;
    //     height: 44px;
    //     border: 1px solid rgba(18, 93, 172, 0.8);
    //     color: rgba(225, 233, 249, 1);
    //     font-size: 18px;
    //     font-family: PingFangSC-Regular;
    //     background-color: rgba(1, 48, 122, .5);
    //     border-radius: 14px;
    //     // background: url("../assets/img/login/input_bg.png") no-repeat center center;
    //     // background-size: 100% 100%;
    // }
  }

  .el-dialog__header {
    justify-content: center;
    background: none;
  }

  .el-dialog__title {
    justify-content: center;
    line-height: 62px;
    font-weight: 800;
    height: 62px;
    display: flex;
    color: rgba(255, 255, 255, 1);
    font-size: 22px;
    background-image: -webkit-linear-gradient(bottom, #398ce4, #abd4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .el-dialog__title::before,
  .el-dialog__title::after {
    content: none;
  }

  // .el-dialog__headerbtn {
  // 	    width: 30px;
  //         height: 30px;
  //         float: right;
  //         top: 10px;
  //         right: 10px;
  // 	background: url("../assets/images/x.png") center center
  //           no-repeat;
  //         background-size: 100% 100%;
  // }
  .el-dialog__body {
    align-items: center;
    justify-content: space-between;
    display: flex;
    flex-direction: row;
    height: 155px;
  }

  .code-btn {
    width: 100px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 222;
    //#ef8466橘色
    color: #409EFF;
    font-size: 14px;
    border: none;
    border: 1px solid rgba(18, 93, 172, 0.8);
    // border-left: 1px solid #bababa;
    padding-left: 10px;
    border-radius: 5px;
    background-color: rgba(1, 48, 122, .5);
    //鼠标指针的样式:伸出食指的手,default 箭头，crosshair 十字，progress 箭头和沙漏
    cursor: pointer;
  }

  .code-btnqx {
    width: 80px;
    height: 40px;
    color: #409EFF;
    border: 1px solid rgba(18, 93, 172, 0.8);
    border-radius: 5px;
    background-color: rgba(1, 48, 122, .5);
    //鼠标指针的样式:伸出食指的手,default 箭头，crosshair 十字，progress 箭头和沙漏
    cursor: pointer;
  }

  .code-btnUpdata {
    width: 80px;
    height: 40px;
    color: #fff;
    border: 1px solid rgba(18, 93, 172, 0.8);
    border-radius: 5px;
    margin-left: 10px;
    background-color: rgba(1, 48, 122, 1);
    //鼠标指针的样式:伸出食指的手,default 箭头，crosshair 十字，progress 箭头和沙漏
    cursor: pointer;
  }


}
</style>
