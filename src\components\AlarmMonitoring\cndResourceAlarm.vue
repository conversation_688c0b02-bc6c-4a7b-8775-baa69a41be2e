<template> <!-- 数通资源告警页面 -->
  <div class="page">
    <div class="alarm_statistics">
      <div class="alarm_title"><span>数通资源告警统计</span></div>
      <div class="alarm_content">
        <div class="alarm_left_content">
          <!-- <div class="base_data">基础数据</div> -->
          <div class="alarm_left_top_content">
            <div class="alarm_part">
              <div class="alarm_img_size">
                <img src="../../assets/alarmImg/dataIcon4.png" alt="">
              </div>
              <div class="alarm_label"> 
                <div class="alarm_part_content_title">影响的核心网网元数量</div>
                <div class="alarm_part_content_num" style="color: #62E3FF ;">{{  core_net_device_cnt }}</div>
              </div>
            </div>
            <div class="alarm_part">
              <div class="alarm_img_size">
                <img src="../../assets/alarmImg/dataIcon.png" alt="">
              </div>
              <div class="alarm_label"> 
                <div class="alarm_part_content_title">告警涉及的电路数量</div>
                <div class="alarm_part_content_num" style="color: #62E3FF ;">{{  alarm_circuit_cnt }}</div>
              </div>
            </div>
            <div class="alarm_part">
              <div class="alarm_img_size">
                <img src="../../assets/alarmImg/dataIcon4.png" alt="">
              </div>
              <div class="alarm_label"> 
                <div class="alarm_part_content_title">核心网数通网元总量</div>
                <div class="alarm_part_content_num" style="color: #62E3FF ;">{{  device_name_cnt }}</div>
              </div>
            </div>
          </div>
          <div class="alarm_left_top_content">
            <div class="alarm_part">
              <div class="alarm_img_size">
                <img src="../../assets/alarmImg/dataIcon4.png" alt="">
              </div>
              <div class="alarm_label"> 
                <div class="alarm_part_content_title">核心网数通告警总量</div>
                <div class="alarm_part_content_num" style="color: #62E3FF ;">{{  alarm_cnt_sum }}</div>
              </div>
            </div>
            <div class="alarm_part">
              <div class="alarm_img_size">
                <img src="../../assets/alarmImg/dataIcon4.png" alt="">
              </div>
              <div class="alarm_label"> 
                <div class="alarm_part_content_title">核心网数通网元告警总量</div>
                <div class="alarm_part_content_num" style="color: #62E3FF ;">{{  alarm_cnt }}</div>
              </div>
            </div>
            <div class="alarm_part">
              <div class="alarm_img_size">
                <img src="../../assets/alarmImg/dataIcon1.png" alt="">
              </div>
              <div class="alarm_label"> 
                <div class="alarm_part_content_title">核心网数通网元告警率</div>
                <div class="alarm_part_content_num" style="color: #62E3FF;">{{  core_alarm_rate }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="alarm_right_content">
          <div class="alarm_right_top_content">各地市告警数量排行</div>
          <div class="alarm_right_bottom_content">
            <div class="alarm_right_bottom">
              <div class="num_item" v-for="(item, index) in cityAlarmRank" :key="index">
                <div class="num">{{ index + 1 }}</div>
                <div class="num_city">{{ item.city_name }}</div>
                <div class="num_process" >
                  <myProgress :value="item.cnt" :max="maxValue<10 ? maxValue + 1 : maxValue+10 " />
                </div>
                <div class="num_data">{{ item.cnt }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="alarm_detail">
      <div class="alarm_title"><span>数通资源告警详情</span></div>
      <div class="alarm_content">
        <div class="table_query_content">
          <el-collapse
            v-model="activeNames"
            ref="collapse"
          >
            <el-collapse-item name="1" :hide-arrow="false">
              <template slot="title">
                <el-form ref="form" :model="form" label-width="80px" inline>
                  <el-form-item label="告警流水单号" label-width="100px">
                    <el-input 
                      v-model="form.serialId"
                      clearable
                      @keyup.enter.stop.native
                      @keyup.space.stop.native
                      @click.stop.native 
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="设备唯一ID" label-width="100px">
                    <el-input 
                      v-model="form.neId"
                      clearable
                      @keyup.enter.stop.native
                      @keyup.space.stop.native
                      @click.stop.native 
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="设备类型" label-width="100px">
                    <el-input 
                      v-model="form.neType"
                      clearable
                      @keyup.enter.stop.native
                      @keyup.space.stop.native
                      @click.stop.native 
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="设备名称" label-width="100px">
                    <el-input 
                      v-model="form.neName"
                      clearable
                      @keyup.enter.stop.native
                      @keyup.space.stop.native
                      @click.stop.native 
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button 
                      size="small" 
                      type="primary" 
                      @keyup.enter.stop.native
                      @keyup.space.stop.native
                      @click.stop.native  
                      @click="moreQuery"
                    >更多查询</el-button>
                  </el-form-item>
                  <el-form-item>
                    <el-button 
                      size="small" 
                      type="primary" 
                      @keyup.enter.stop.native
                      @keyup.space.stop.native
                      @click.stop.native  
                      @click="getList">查询</el-button>
                  </el-form-item>
                </el-form>
              </template>
              <el-form ref="form" :model="form" label-width="80px" inline>
                <el-form-item label="地市" label-width="100px">
                    <el-select v-model="form.neCity" placeholder="请选择">
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="告警类型" label-width="100px">
                    <el-input 
                      v-model="form.alarmCat"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="告警名称" label-width="100px">
                    <el-input 
                      v-model="form.alarmName"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="告警级别" label-width="100px">
                    <el-input 
                      v-model="form.alarmLevel"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="告警状态" label-width="100px">
                    <el-input 
                      v-model="form.alarmStatus"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="端口名称" label-width="100px">
                    <el-input 
                      v-model="form.portName"
                      clearable
                    ></el-input>
                  </el-form-item>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="alarm_table">
          <el-table 
            v-loading="loading"
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            @cell-click="handleRowClick"
            height="34vh"
            :header-row-style="{
              color:'#9ED8FF',
              'background-color':'rgba(15, 52, 124, 1)',
            }"
            :header-cell-style="{
              color: '#9ED8FF',
              'background-color': 'rgba(15, 52, 124, 1)',
              'text-align': 'center',
              'font-size': '14px',
              'border-right': '1px rgba(15, 0, 137, .8) solid'
            }"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            border
          >
            <!-- <el-table-column 
              width="55" 
              align="center"
              type="selection"
            >
            </el-table-column> -->
            <el-table-column 
              v-for="(i, idx) in tableColumns" :key="idx"
              :label="i.displayName"
              :prop="i.columnName"
              show-overflow-tooltip
              :width="i.displayName.length > 4 ? 220 : 100"
              align="center"
            >
            </el-table-column>
          </el-table>
          <el-pagination 
            class="page-list"
            @size-change="handleSizeChange" 
            @current-change="handleCurrentChange"
            :current-page.sync="pageNumber" 
            :page-sizes="[10, 20, 30, 40, 50]" 
            :page-size="pageSize" 
            background
            layout="total, prev, pager, next, sizes" 
            :total.sync="total" 
            style="float: right">
          </el-pagination>
        </div>
        <el-dialog :visible.sync="dialogTableVisible">
          <el-descriptions class="margin-top" :column="2" :size="size" border>
            <el-descriptions-item v-for=" (item, index) in tableColumns" :key="index">
              <template slot="label">
                {{ item.displayName || '' }}
              </template>
              <span @dblclick="handleRowDblclick(dialogTable[item.columnName])">{{ dialogTable[item.columnName] }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import myProgress from './progress.vue';
export default {
  data() {
    return {
      dialogTableVisible:false,
      statisticsTitle: '',  
      detailTitle: '',
      options: [
        {label:'西安', value:'西安市'},
        {label:'宝鸡', value:'宝鸡市'},
        {label:'咸阳', value:'咸阳市'},
        {label:'渭南', value:'渭南市'},
        {label:'延安', value:'延安市'},
        {label:'榆林', value:'榆林市'},
        {label:'铜川', value:'铜川市'},
        {label:'汉中', value:'汉中市'},
        {label:'安康', value:'安康市'},
        {label:'商洛', value:'商洛市'}, 
      ],
      value: '',
      tableData: [
        {
          id:'1',
          city:'西安',
          customerName:'陕西联通核心网监控中心',
          criticalName:'西开省分南院OFO12DWM',
          criticalCode:'西开省分南院OFO12DWM',
          deviceName:'0010-浐灞',
          alarmPosition:'3037-西开5楼',
          alarmTime:'2025/02/05 10:23:56',
          alarmDetail:'传输系统故障',
          orderNumber:'0124924881024029119',
        },
      ],
      multipleSelection: [],
      // 影响的核心网网元数量
      core_net_device_cnt: '',
      // 告警涉及的电路数量
      alarm_circuit_cnt: '',
      // 核心网数通网元告警率
      core_alarm_rate: '',
      // 核心网数通告警总量
      alarm_cnt_sum: '',
      // 核心网数通网元告警总量
      alarm_cnt: '',
      // 核心网数通网元总量
      device_name_cnt: '',
      // 各地市告警数量排行
      cityAlarmRank: [
        
      ],
      loading: false,
      header: [],
      tableColumns: [],
      total: 0,
      pageNumber: 1,
      pageSize: 10,
      form:{
        serialId: '',
        neId: '',
        neType: '',
        neName: '',
        neCity: '',
        alarmCat: '',
        alarmName: '',
        alarmLevel: '',
        alarmStatus: '',
        portName: '',
      },
      activeNames: [],
      maxValue: 0,
      flage: true,
      dialogTable:{}
    }
  },
  components: {
    myProgress,
  },
  mounted() {
    const jsonData = require('../../assets/json/alarmMonitoring/alarm.json');
    this.tableColumns = jsonData.tableConfig.columns;
    this.getCoreAlarmByCity();
    this.getCoreAlarmStatistics();
    this.getList();
  },
  watch: {
    form: {
      handler(newVal, oldVal) {
        console.log('form changed:', newVal, oldVal);
        this.flage = false
      },
      deep: true // 深度监听
    }
  },
  methods: {
    async copyToClipboard(text) {
      try {
        // 方案1: 使用现代剪贴板API
        if (navigator.clipboard && window.isSecureContext) {
          await navigator.clipboard.writeText(text);
          return true;
        }
        // 方案2: 使用传统execCommand作为降级方案
        else {
          const textArea = document.createElement('textarea');
          textArea.value = text;
          textArea.style.position = 'fixed';  // 避免滚动
          document.body.appendChild(textArea);
          textArea.select();
          
          const success = document.execCommand('copy');
          document.body.removeChild(textArea);
          
          if (!success) throw new Error('复制失败');
          return success;
        }
      } catch (err) {
        console.error('复制操作失败:', err);
        
        // 方案3: 显示备用复制界面
        // showFallbackCopyUI(text); // 自定义的备用复制方法
        return false;
      }
    },
    async handleRowDblclick(val){
      const cellData = val;
      if (cellData) {
          try {
        await this.copyToClipboard(cellData);
        this.$message({
          message: '复制成功',
          type: 'success',
        });
      } catch (error) {
        this.$message({
          message: `复制失败-${error}`,
          type: 'error',
        });
      }
      }
      
      
    },
    handleRowClick(row, column){
      this.dialogTable = row
      this.dialogTableVisible = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    getCoreAlarmByCity(){
      this.$api.resAlarm.getCoreAlarmByCity().then(res=>{
        this.maxValue =Math.max(...res.data.map(i=>i.cnt))
        this.cityAlarmRank = res.data.sort((a, b) => b.value - a.value)
      })
    },
    getCoreAlarmStatistics(){
      this.$api.resAlarm.getCoreAlarmStatistics().then(res=>{
        this.core_net_device_cnt = res.data.core_net_device_cnt
        this.alarm_circuit_cnt = res.data.alarm_circuit_cnt
        this.core_alarm_rate = (res.data.core_alarm_rate * 100).toFixed(2) + '%'
        this.alarm_cnt_sum = res.data.alarm_cnt_sum
        this.alarm_cnt = res.data.alarm_cnt
        this.device_name_cnt = res.data.device_name_cnt
      })
    },
    handleCurrentChange(val){
      this.pageNumber = val;
      this.getList();
    },
    handleSizeChange(val){
      this.pageNumber = 1;
      this.pageSize = val;
      this.getList();
    },
    getList(){
      if (!this.flage) {
        this.pageNumber = 1;
        this.flage = true
      }
      const params = {
        ...this.form,
        pageSize: this.pageSize,
        pageNum: this.pageNumber
      };

      this.$api.resAlarm.getCoreAlarmDetail(params).then(res=>{
        this.tableData = res.data.records;
        this.total = res.data.total;
      })
    },
    moreQuery(){
      this.activeNames = this.activeNames[0] === '1' ? [] : ['1'];
    }
  },
  
}
</script>

<style lang="less" scoped>
.page{
  color: white;
  height: calc(100vh - 120px);
  margin: 20px 20px 0;
  .alarm_statistics{
    height: 35vh;
    .alarm_title{
      padding-left: 55px;
      font-size: 24px;
      line-height: 45px;
      height: 45px;
      font-weight: 800;
      background: url(../../assets/alarmImg/propertyAlarm/title_bg.png) no-repeat 0px 0px;
      background-size: cover;
      span {
        background: linear-gradient(to bottom, #D9F7FF, #FFFFFF); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
        -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
        -webkit-text-fill-color: transparent;/*给文字设置成透明*/
      }
    }
    .alarm_content{
      display: flex;
      height: calc(100% - 37px);
      .alarm_left_content{
        width: 50%;
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        .base_data{
          text-align: center;
          font-size: 16px;
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 20px;
        }
        .alarm_left_top_content{
          margin-top: 20px;
          width: 100%;
          height: 40%;
          background: url(../../assets/alarmImg/bBackground.png) no-repeat 0px 0px;
          background-size: contain;
          display: flex;
          justify-content: space-evenly;
          margin-bottom: 5px;
          .alarm_part{
            display: flex;
            font-size: 1.5vh;
            width: 10vw;
            min-width: 105px;

            .alarm_img_size{
              width: 30%;
              height: 55%;
            }
            .alarm_label{
              margin-left: 5px;
              width: 60%;
              .alarm_part_content_title{
                font-family: SourceHanSansCN, SourceHanSansCN;
                font-size: .2rem;
                font-weight: 400;
                color: #FFFFFF;
                text-align: left;
              }
              .alarm_part_content_num{
                font-family: D-DIN-PRO, D-DIN-PRO;
                font-weight: 500;
                font-size: 20px;
                text-align: left;
                font-style: normal;
                color: linear-gradient(90deg, #8BEAFF 0%, #41DDFF 100%);
              }
            }
          }
        }
        .alarm_left_bottom_content{
          margin-top: 15px;
          width: 100%;
          height: 37%;
          background: url(../../assets/alarmImg/bBackground.png) no-repeat 0px 0px;
          background-size: contain;
          display: flex;
          justify-content: space-around;
          .alarm_part{
            display: flex;
            font-size: 1.5vh;
            .alarm_img_size{
              width: 50px;
              height: 50px;
            }
            .alarm_label{
              margin-left: 5px;
            }
          }
        }
      }
      .alarm_right_content{
        width: 50%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        margin-top: 10px;
        .alarm_right_top_content{
          text-align: center;
          font-size: 16px;
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          font-size: 20px;
        }
        .alarm_right_bottom_content{
          margin-top: 5px;
          display: flex;
          height: 100%;
          margin: 5px 5px 5px;
          .alarm_right_bottom{
            width: 100%;
            display: grid; 
            grid-template: "a b" 19%
                    "c d" 19%
                    "e f" 19%
                    "g h" 19%
                    "i j" 20%;
            grid-auto-flow: column;
            .num_item{
              position: relative;
              width: 95%;
              height: 100%;
              background: url(../../assets/alarmImg/sort.png) no-repeat 0px 0px;
              background-size: cover;
              margin-bottom: 10px;
              .num{
                position: absolute;
                top: 30%;
                left: 5%;
                font-size: 1.5vh;
              }
              .num_city{
                position: absolute;
                // top: 17px;
                top: 25%;
                left: 12%;
                font-size: 1.5vh;
              }
              .num_process{
                background-color: #051D35;
                width: 60%;
                height: 35%;
                position: absolute;
                // top: 18px;
                top: 30%;
                left: 25%;
              }
              .num_data{
                position: absolute;
                top: 15px;
                right: 4%;
                font-size: 1.5vh;
                color: #FF9852 ;
              }
            }
          }
        }
      }
    }
  }
  .alarm_detail{
    // height: calc(65vh - 130px);
    // background-color: #0D2343;
    overflow: auto;
    .alarm_title{
      padding-left: 55px;
      font-size: 24px;
      line-height: 45px;
      height: 45px;
      font-weight: 800;
      background: url(../../assets/alarmImg/propertyAlarm/title_bg.png) no-repeat 0px 0px;
      background-size: cover;
      span {
        background: linear-gradient(to bottom, #D9F7FF, #FFFFFF); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
        -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
        -webkit-text-fill-color: transparent;/*给文字设置成透明*/
      }
    }
    .alarm_content{
      margin-top: 5px;
      display: flex;
      flex-direction: column;
      height: calc(65vh - 167px);
      overflow: auto;
      .table_query_content{
        // display: flex;
        margin: 20px 0;
        /deep/.el-form{
          .el-form-item{
            margin-bottom: 5px;
            .el-form-item__label{
              color: white !important;
            }
            .el-form-item__content{
              color:#81BBFF;
              .el-select{
                line-height: 30px !important;
                  .el-input{
                  line-height: 30px !important;
                  .el-input__inner{
                    color: #81BBFF ;
                    background-color: transparent !important;
                    border: 1px solid #2A6DBD;
                    line-height: 30px !important;
                  }
                }
              }
              .el-input{
                width: 200px;
                .el-input__inner{
                  color: #81BBFF ;
                  background-color: transparent !important;
                  border: 1px solid #2A6DBD;
                  line-height: 30px !important;
                  height: 30px !important;
                }
              }
            }
          }
        }
        /deep/.el-collapse{
          border:none;
          .el-collapse-item{
            .el-collapse-item__header{
              background-color: transparent;
              border: none;
            }
            .el-collapse-item__wrap{
              background-color: transparent;

            }
          }
        }
      }
      .alarm_table{
        height: 50%;
        /deep/.el-table{
          color: white;
          background-color: transparent;
          border: none;
          &::before {
              content: '';
              position: absolute;
              background-color: transparent;
              z-index: 1;
          }
          .el-table__header-wrapper{
            .el-table__header{
              thead{
                tr{
                  .el-table__cell{
                    border: none;
                    .cell{
                      .el-checkbox{
                        .el-checkbox__input{
                          .el-checkbox__inner{
                            background-color: transparent;
                          }
                        }
                      }
                    }
                    &:last-child{
                      width: 0 !important;
                      display: none;
                    }
                  }
                }
              }
            }
          }
          .el-table__body-wrapper{
            .el-table__body{
              tbody{
                .el-table__row{
                  .el-table__cell{
                    border: none;
                    .cell{
                      .el-checkbox{
                        .el-checkbox__input{
                          .el-checkbox__inner{
                            background-color: transparent;
                          }
                        }
                      }
                    }
                  }
                  &:nth-child(odd) {
                    background: #04152E;
                  }
                  &:nth-child(even) {
                    background: #0D2343;
                  }
                }
              }
            }
            &::-webkit-scrollbar-track{
              background-color: #04152E;
            }
            &::-webkit-scrollbar-thumb{
              background-color: #06529D;
            }
          }
        }
      }
    }
  }
  ::v-deep{
    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: #06529d !important;
      cursor: pointer;
    }
  }
}
img{
  width: 100%;
  height: 100%;
}
/deep/ .el-dialog {
      width: 65%;
      background: url("../../assets/images/dialogbg.png") center center no-repeat;
      background-size: 100% 100%;
      margin-top: 20vh !important;
      .el-dialog__header {
      justify-content: left;
      background: none;
    }

    .el-dialog__title {
      justify-content: left;
      line-height: 20px;
      font-weight: 800;
      height: 20px;
      display: flex;
      color: rgba(255, 255, 255, 1);
      font-size: 22px;
    }

    .el-dialog__title::before,
    .el-dialog__title::after {
      content: none;
    }

    .el-dialog__headerbtn {
      width: 30px;
      height: 30px;
      float: right;
      top: 10px;
      right: 10px;
      background: url("../../assets/images/x.png") center center no-repeat;
      background-size: 100% 100%;
    }

    .el-dialog__body {
      height: 85%;
      // padding: 5px 20px 0;
      .detail-info {
        max-height: 800px;
        height: 100%;
      }
    }
    .el-table,
    .el-table__expanded-cell {
      background-color: transparent !important;
    }
    .el-collapse-item__header .el-collapse-item__arrow {
      display: none !important;
    }
    .el-table thead.is-group th.el-table__cell {
      background-color: transparent !important;
    }
    .el-table__header-wrapper{
      // height: 30% !important;
      .el-table__header{
        // height: 100% !important;
      }
    }
    .el-table__body-wrapper {
      height: 87% !important;
    }
    /*定义滚动条轨道 内阴影+圆角*/
    .el-table__body-wrapper::-webkit-scrollbar-track {
      box-shadow: inset 0 0 0Px #163479 !important;
      border-radius: 10Px;
      background-color: #163479 !important;
    }

    .el-table tr {
      color: #fff;
      background-color: transparent;
      cursor: pointer;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
      background-color: #06529d !important;
      cursor: pointer;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped:hover>td.el-table__cell {
      background-color: #06529d !important;
      cursor: pointer;
    }

    .el-table--striped {
      background-color: rgba(12, 33, 87, 0) !important;
    }

    .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
      background: rgba(12, 21, 69, 1);
    }

    .el-table__body-wrapper:hover::-webkit-scrollbar-corner {
      width: 10Px;
    }

    /*定义滑块 内阴影+圆角*/
    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      border-radius: 10Px;
      box-shadow: inset 0 0 0Px #4C72C9;
      background: linear-gradient(-55deg, #4C72C9 0%, #4C72C9 100%);
    }

    .el-table__body-wrapper::-webkit-scrollbar {
      width: 5Px; // 横向滚动条
      height: 5Px; // 纵向滚动条 必写
    }

    .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: transparent;
    }

    .el-table--border th.el-table__cell.gutter {
      display: none;
    }

    /* 表格鼠标悬浮时的样式（高亮） */
    .el-table--enable-row-hover .el-table__body tr:hover {
      background-color: rgba(255, 255, 255, 0);
    }

    /*表格鼠标悬停的样式（背景颜色）*/
    .el-table tbody tr:hover>td {
      background-color: rgba(255, 255, 255, 0);
    }

    .el-table__body .el-table__row.hover-row td {
      background-color: #0f2444;
    }

    .el-table td.el-table__cell,
    .el-table th.el-table__cell.is-leaf {
      background-color: transparent;
      border: 1px solid transparent;
    }

    .el-table--border .el-table__cell,
    .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
      border: 1px solid transparent;
    }

    .el-table tr:nth-child(even) {
      background: rgba(40, 72, 121, 0.29);
    }
    .el-descriptions__body {
      color: #606266;
      background: transparent;
    }
    .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
        width: 200px;
        padding-bottom: 12px;
        text-align: center;
        color: #fff;
        line-height: 20px;
        // padding: .42rem;
        font-size: 16px;
        // background: transparent;
        border: 1px solid #2e5f9d;
    }
    .el-descriptions-item__label.is-bordered-label {
        width: 100px !important;
        background: rgba(15, 52, 124,0.6);
        color: #a5c8ff;
        line-height: 20px;
        font-weight: 400;
        padding-bottom: 12px;
        font-size: 16px;
        text-align: center;
        border-bottom: 1px solid #2e5f9d;
    }
    }
</style>
