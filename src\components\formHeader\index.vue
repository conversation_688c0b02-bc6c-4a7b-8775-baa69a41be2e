<template>
  <div
    class="form-header bg-header dif jcsb wf"
    
  >
    <div class="header-l" ref="header">
      核心网业务监控系统
    </div>
    <div style="z-index: 992;height:60%;margin-top: 15px;position: absolute;right: 15px;">
       
      <el-dropdown class="fr" style="color: #fff" @command="logOut">
        <span class="el-dropdown-link ml20 cp fs16">
          {{ userName }}<i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="user fr ml10">
        <img class="user-img wf" :src="userImg" />
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "formHeader",
    data() {
      return {
        // alarmSendData: [],
        datetime: "",
        showinform: false,
        informData: [
          {
            name: "1041-临河乌海方向",
            date: "2019-09-07 14:33:14",
          },
          {
            name: "呼和浩特市ONE0017PN线路",
            date: "2019-09-07 14:33:14",
          },
          {
            name: "呼和浩特市ONE0017PN线路",
            date: "2019-09-07 14:33:14",
          },
          {
            name: "呼和浩特市ONE0017PN线路，1041-临河乌海方向",
            date: "2019-09-07 14:33:14",
          },
        ],
        showMenu: true,
        userImg: require("../../assets/img/header/user_img.jpg"),
        userName: "客户",
        menuIcon: "ico el-icon-s-fold cp",
        showCustList: false,
        noticeList: [], // 处理后的消息列表
        audio: new Audio(),
        warnUrl: require("../../assets/header/alarmMusic.mp3"),
        timer: {}
      };
    },
    created(){
      this.userName = JSON.parse(localStorage.getItem('user'))
    },
    mounted(){
    },
    destroyed(){
      window.clearInterval(this.timer);
    },
    props: {
      companyName: {
        type: String,
        default: () => "",
      },
    },
    watch: {
      datetime(n) {
        this.$nextTick(() => {
          if (n) {
            this.datetime = n;
            setTimeout(() => {
              let timer = setInterval(() => {
                // this.alarmSend();
              }, n * 1000);
              if (n == "") {
                clearInterval(timer);
              }
            }, 2000);
          }
        });
      },
      companyName() {},
      custName(n) {
        if (n) {
          this.userName = `客户 ${n}`;
        }
      },
    },
    methods: {
      logOut(){
        localStorage.clear();
        this.$router.push({ name: "login" });
      },
      cllearinform() {
        this.showinform = false;
        this.$api.slaApi.updateAlarmNotice().then((req) => {
          if (req.code == 200) {
            this.$refs.audio.pause()
          }
        })
      },
      informclick() {
        this.showinform = !this.showinform;
      },
      /**
       * <AUTHOR>
       * 切换列表菜单展开收起
       */
      toggleMenu() {
        this.showMenu = !this.showMenu;
      },
      /**
       * <AUTHOR>
       * 显示客户列表
       */
      showUserList() {
        this.showCustList = !this.showCustList;
      },
      handleCommand(command) {
        this.$message('click on item ' + command);
      }
    },
  };
</script>

<style scoped lang="less">
  .form-header {
    height: 8%;
    line-height: 62px;
    padding: 0 20px 0 20px;
    z-index: 1;
    position: fixed;
    top: 0;
    right: 0;
    background: url("../../assets/img/homeImg/top_3.png") center center
              no-repeat;
            background-size: 100% 100%;
    .ico {
      display: inline-block;
      font-size: 16px;
      line-height: 62px;
    }
    .user {
      width: 25px;
      height: 25px;
      margin-top: 20px;
      position: relative;
      .user-img {
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        border-radius: 50%;
        z-index: 0;
      }
    }
    .cell-size {
      font-size: 14px;
    }
    .item {
      width: 30px;
      height: 30px;
      text-align: center;
      line-height: 30px;
      border: 1px solid #217fa9;
      border-radius: 20px;
    }
    .item /deep/.el-badge__content {
      height: 20px;
      width: 20px;
      line-height: 16px;
      padding: 0;
    }
    .inform-mains {
      all: initial;
      width: 100px;
      // height: 375px;
      position: absolute;
      overflow: auto;
      top: 62px;
      right: 20px;
      background: #fff;
      box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05), 0 1px 10px rgba(0, 0, 0, 0.1);
      border-radius: 3px;
      // ::-webkit-scrollbar {
      //   width: 4px !important;
      // }
    }
    .inform-main {
      all: initial;
      width: 300px;
      // height: 375px;
      position: absolute;
      overflow: auto;
      top: 62px;
      right: 100px;
      background: #fff;
      box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05), 0 1px 10px rgba(0, 0, 0, 0.1);
      border-radius: 3px;
      ::-webkit-scrollbar {
        width: 4px !important;
      }
      .inform-main-cont {
        display: block;
        border-bottom: 1px solid rgba(220, 222, 226, 1);
        padding: 10px 20px;
        cursor: pointer;
      }
      .inform-title {
        color: #fa6400;
        font-size: 14px;
        border-bottom: 1px solid rgba(220, 222, 226, 1);
        height: 50px;
        line-height: 50px;
        padding-left: 20px;
        position: relative;
        .el-icon-close {
          cursor: pointer;
        }
        .inform-line {
          position: absolute;
          display: inline-block;
          width: 85px;
          height: 2px;
          border-radius: 10px;
          background: #fa6400;
          bottom: 0;
          left: 20px;
        }
      }
      .inform-main-title {
        padding-left: 5px;
        color: rgba(81, 90, 110, 1);
        font-size: 14px;
        display: block;
        line-height: 22px;
      }
      .inform-main-date {
        font-size: 12px;
      }
      .inform-main-clear {
        text-align: center;
        color: rgba(81, 90, 110, 1);
        font-size: 14px;
      }
    }
    .inform-color {
      width: 32px;
      height: 32px;
      display: block;
      border-radius: 50%;
      text-align: center;
      line-height: 32px;
      margin: 4px auto;
      .inform-f {
        color: #fff !important;
        font-weight: 700;
        i {
          font-size: 18px;
          line-height: 32px;
        }
        &.style2 {
          font-size: 20px;
          display: block;
          transform: rotate(180deg);
        }
      }
    }
    .menuTitle {
      background-color: transparent;
      border: none;
    }
    .menu {
      background: url("../../assets/header/menuBg.png") center center
        no-repeat;
      background-size: 100% 100%;
      width: 150px;
      height: 45px;
      margin-top: 50px;
      border: none;
    .menuHeader {
      display: block;
      width: 100%;
      height: 100%;
      line-height: 45px;
      color: #fff;
      font-size: 18px;
      padding-left: 15px;
      }
      /deep/.el-submenu__title {
        height: 45px !important;
        line-height: 45px !important;
        color: #fff !important;
        border: none;
      }
    }
    .header-l {
        width: 80%;
        font-size: 2.8vh;
        text-align: center;
        margin-left: 10%;
        line-height: 7.5vh;
    }
  }
  @keyframes flashing {
  from {
    background: red;
  }
  50% {
    background: transparent;
  }
  from {
    background: red;
  }
}
.alarmOn {
  // width: 120%;
  // height: 120%;
  background: #ff000094;
  border-radius: 50%;
  animation: flashing 2000ms infinite ease-in-out;
}

</style>
