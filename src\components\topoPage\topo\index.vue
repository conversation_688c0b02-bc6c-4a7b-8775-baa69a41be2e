<template>
  <div class="topoContainer" v-loading="loading">
    <div class="imgTuli">
      <div class="imgCls">
        <img :src="defaultNetElement" />
        <span>网元</span>
      </div>
      <div class="imgCls">
        <img :src="alarmNetElement" />
        <span>告警网元</span>
      </div>
      <div class="lujing">
        <div class="workcls" :style="'background:' + defaultLineColor"></div>
        <span>线路</span>
      </div>
      <div class="lujing">
        <div class="protectCls" :style="'background:' + alarmLineColor"></div>
        <span>告警线路</span>
      </div>
      <div>
        <!--  class="enableCls" -->
        <el-tooltip class="item" effect="dark" content="开启按钮可以保存拓扑的位置信息，关闭按钮则不能保存。" placement="top">
          <el-switch v-model="enableSave" active-color="#409FFF" active-text="保存使能" inactive-text="保存禁用"
            @change="handleChange" v-permission="'100030'">
          </el-switch>
        </el-tooltip>
      </div>
      <div class="enableCls">
        <!-- <el-tooltip class="item" effect="dark" content="开启按钮可以保存拓扑的位置信息，关闭按钮则不能保存。" placement="top">
          <el-switch v-model="enableSave" active-color="#409FFF" active-text="保存使能" inactive-text="保存禁用"
            @change="handleChange">
          </el-switch>
        </el-tooltip> -->
        <el-button v-if="topoPublish" type="primary" size="mini" @click="topoRelease">拓扑发布</el-button>
      </div>
    </div>
    <div id="container" ref="container"></div>
    <el-dialog
			title="告警列表"
			:visible.sync="detailModal"
			:modal="false"
			:close-on-click-modal="false"
      class="alarmList"
		>
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="网元" name="ne">
          <cust-act-table
		        ref="custActTable"
		        :list="alarmListData"
		        :colConfigs="alarmColumns"
            @getDetail="getDetail"
		        @changePage="getPage"
		        :pagin="true"
		        :showIdx="false"
            :istopo='false'
		        tableType="alarmMonitor"
		        rKey="index"
						style="margin: 10px 10px 0 10px;height: 80%;"
						:pageNum="pageNum"
						:pageSize="pageSize"
						:total="total"
		    ></cust-act-table>
        </el-tab-pane>
        <el-tab-pane label="板卡" name="card">
          <cust-act-table
		        ref="custActTable"
		        :list="alarmListData"
		        :colConfigs="alarmColumns"
            @getDetail="getDetail"
		        @changePage="getPage"
		        :pagin="true"
		        :showIdx="false"
		        tableType="alarmMonitor"
		        rKey="index"
						style="margin: 10px 10px 0 10px;height: 80%;"
						:pageNum="pageNum"
						:pageSize="pageSize"
						:total="total"
		    ></cust-act-table>
        </el-tab-pane>
        <el-tab-pane label="端口" name="port">
          <cust-act-table
		        ref="custActTable"
		        :list="alarmListData"
		        :colConfigs="alarmColumns"
            @getDetail="getDetail"
		        @changePage="getPage"
		        :pagin="true"
		        :showIdx="false"
		        tableType="alarmMonitor"
		        rKey="index"
						style="margin: 10px 10px 0 10px;height: 80%;"
						:pageNum="pageNum"
						:pageSize="pageSize"
						:total="total"
		    ></cust-act-table>
        </el-tab-pane>
      </el-tabs>
		</el-dialog>
    <el-dialog
			title="告警工单详情"
			:visible.sync="detailModals"
			:modal="false"
			:close-on-click-modal="false"
		>
      <detail-modal
      ref="detailInfo"
				class="detail-info"
				:mainFormId="mainFormId"></detail-modal>
		</el-dialog>
  </div>
</template>

<script>
import G6, { Legend } from "@antv/g6";
import green from "@/assets/img/topo/green.png";
import yellow from "@/assets/img/topo/yellow.png";
import red from "@/assets/img/topo/red.png";
// import resultJson from "./data.json";
import { getHiddenNodes, isCashed } from './util.js';
import insertCss from 'insert-css';
import green1 from "@/assets/img/topo/defaultData/defaultData1.png";
import yellow1 from "@/assets/img/topo/selectData/selectData1.png";
import red1 from "@/assets/img/topo/alarmData/red1.png";
import custActTable from "@/components/common/Table/custActTable.vue";
import DetailModal from '../../common/Modal/detailModal.vue';

let leavelMap = {
  'combo1': '骨干层',
  'combo2': '核心层',
  'combo3': '汇聚层',
  'combo4': '接入层',
  'combo5': '未知'
}

insertCss(`
  .g6-component-toolbar{
    top: -40px;
    left: 85% !important;
    background: #041736 !important;
    border: 1px solid #03396A;
    svg {
      fill: #fff;
    }
  }

  .g6-component-tooltip {
      background-color: #062856 !important;
      color: #fff;
      border: 0;
      padding: 16px;
      .node-cls {
        display: flex;
        span {
          display: flex;
          align-items: center;
          height: 30px;
        }

        :first-child {
          width: 90px;
        }
      }
    }

`);

import permission from '../../common/permission.js';
export default {
directives: {
  permission
},
  name: "Topo",
  props: {
    sendData: {
      type: Object,
      default: () => {
        return {
          nodes: [],
          edges: []
        }
      }
    },
    custId: {
      type: String
    },
  },
  components:{
    custActTable,
    DetailModal
  },
  data() {
    return {
      topoPublish: false,
      topoLoading: false,
      detailModals: false,
      detailModal: false,
      enableSave: false,
      isCache: false,
      loading: false,
      activeName: 'ne',
      combosStyle: {},
      canvansStyle: {},
      // sendData: {},
      jsonData: {
        // id: "01",
        // label: "业务类型",
        // img: img1,
        // children: [],
        nodes: [],
        edges: [],
        combos: [],
      },
      imgMap: {
        green,
        yellow,
        red,
      },
      defaultNetElement: green1,
      selectNetElement: yellow1,
      alarmNetElement: red1,
      defaultLineColor: '#0AEFC5',
      selectLineColor: 'yellow',
      alarmLineColor: 'red',
      defaultHierarchyColor: '#0AEFC5',
      alarmHierarchyColor: 'red',
      alarmListData: [],
			alarmColumns: [
				{
					prop: "alarmRegion",
					label: "地市",
					align: "center",
					width: 130
				},
				{
					prop: "custName",
					label: "客户名称",
					align: "center",
					width: 220
				},
				{
					prop: "circuitName",
					label: "电路名称",
					align: "center",
					width: 200
				},
				{
					prop: "workOrderNumber",
					label: "工单号",
					align: "center",
					width: 200
				},
				{
					prop: "eqpName",
					label: "网元名称",
					align: "center",
					width: 220
				},
				{
					prop: "locateInfo",
					label: "告警位置",
					align: "center",
					width: 230
				},
				{
					prop: "eventTime",
					label: "告警发生时间",
					align: "center",
					width: 220
				},
				{
					prop: "vendorAlarmText",
					label: "告警正文",
					align: "center",
					width: 220
				},
				
				// {
				// 	prop: "serviceNo",
				// 	label: "业务代码",
				// 	align: "center",
				// 	width: 200
				// },
				
				// {
				// 	prop: "bandwidthServiceMode",
				// 	label: "带宽业务模式",
				// 	align: "center",
				// 	width: 200
				// },
				// {
				// 	prop: "alarmSite",
				// 	label: "告警位置",
				// 	align: "center",
				// },
			],
      eqpId: '',
      pageNum:1,
      pageSize:10,
      total:0,
      nodeData: [],
      selRow:{},
      mainFormId: '', // 工单号
    };
  },
  watch: {
    sendData: {
      async handler(newData) {
        // let topoData = resultJson?.data;
        if (this.graph) {
          this.graph.destroy();
          this.jsonData = {
            nodes: [],
            edges: [],
            combos: [],
          }
        }
        let combos = this.jsonData.combos || [];
        if (!combos.length && newData) {
          this.dealTopoData(newData);
          console.log('获取原始数据')
        }
        // console.log(this.jsonData)
        this.initTopo();
        this.highlightTopo();
      },
    },

  },
  created(){
    if (JSON.parse(localStorage.getItem('user')) == 'fengkang') {
      this.topoPublish = true;
    }
    // topoPublish
  },
  mounted() {
  },
  methods: {
    topoRelease(){
      
       this.$confirm('请确认发布', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
           this.$api.slaApi.topoRelease()
           .then((response) => {
             console.log(response);
             if (response.code == 200) {
               this.$message({
                 type: 'success',
                 message: '发布成功!'
               });
             }
           })
          
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消发布'
          });          
        });
    },
    handleChange(val) {
      console.log(val, this.enableSave)
    },
    dealTopoData(data) {
      this.defaultNetElement = data.defaultNetElement
        this.selectNetElement = data.selectNetElement
        this.alarmNetElement = data.alarmNetElement
        this.defaultLineColor = data.defaultLineColor
        this.selectLineColor = data.selectLineColor
        this.alarmLineColor = data.alarmLineColor
        this.defaultHierarchyColor = data.defaultHierarchyColor
        this.alarmHierarchyColor = data.alarmHierarchyColor
      let nodeList = data?.nodes || [];
      let edgeList = data?.edges || [];
      this.isCache = isCashed(nodeList)

      let { nodes: allNodes } = this.getNodesList(nodeList);
      let edges = [];
      let combos = this.getCombos(allNodes);
      let hiddenNodes = getHiddenNodes(combos, this.combosStyle);
      let nodes = [];

      if (this.isCache) {
        nodes = [...allNodes,];
      } else {
        nodes = [...allNodes, ...hiddenNodes];
      }
      edges = this.getEdges(edgeList);

      let jsonData = {
        nodes,
        edges,
        combos,
      };
      // console.log(jsonData)
      this.jsonData = jsonData;
      // this.drawLink();
    },

    highlightTopo() {
      let { nodes, edges, combos } = { ...this.jsonData };
      // let statusType = {
      //   0: this.imgMap.green,
      //   1: this.imgMap.yellow,  //高亮
      //   '-1': this.imgMap.red   //告警
      // }
      nodes.forEach(item => {
        const node = this.graph.findById(item.id);
        if (item.colourType === '1') { //高亮
          node.update({
            // img: this.imgMap.yellow
            img: this.selectNetElement
          })
        } else if (item.colourType === '-1') { //告警
          node.update({
            // img: this.imgMap.red
            img: this.alarmNetElement
          })
        }
        node.refresh();
      })
      edges.forEach(item => {
        const edge = this.graph.findById(item.id);
        console.log(item.colourType);
        if (item.colourType === '1') { //高亮
          this.graph.setItemState(edge, 'active', true)
        } else if (item.colourType === '-1') { //告警
          this.graph.setItemState(edge, 'warn', true)
        }
      })

      combos.forEach(item => {
        let children = item.children || [];
        let curCombs = this.graph.findById(item.id);
        if (this.isWaning(children)) {
          curCombs.update({
            style: {
              // stroke: 'red'
              stroke: this.alarmHierarchyColor     // 层级告警变色
            }
          })
        }
      })
      // this.graph.render();
    },
    //判断是否为告警
    isWaning(list) {
      return list.some(item => {
        const node = this.graph.findById(item.id);
        return node.getModel().colourType == '-1'
      })
    },
    //数组去重
    deduplication(arr) {
      let newArr = [];
      let idArr = [];
      for (let item of arr) {
        if (!idArr.includes(item.label)) {
          newArr.push(item);
          idArr.push(item.label);
        }
      }
      return newArr;
    },
    // 线段数组
    getEdges(edgesList) {
      let links = [];
      for (let item of edgesList) {
        let { source, target } = item;
        links.push({
          ...item,
          source,
          target,
          style: { endArrow: true, },
          colourType: item.colourType || '0'
        });
      }
      G6.Util.processParallelEdges(links);
      return links;
    },
    dealEdges(linkList) {
      let links = [];
      let tmpArr = [];
      let repeatArr = [];
      //   sourceAnchor: 4,
      // targetAnchor: 2,

      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (!tmpArr.includes(idStr)) {
          tmpArr.push(idStr);
        } else {
          repeatArr.push(idStr);
        }
      }
      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (repeatArr.includes(idStr)) {
          if (item.type === "line-arrow") {
            //工作路径
            links.push({
              ...item,
              sourceAnchor: 4,
              targetAnchor: 2,
            });
          } else {
            //保护路径
            links.push({
              ...item,
              sourceAnchor: 5,
              targetAnchor: 3,
            });
          }
        } else {
          links.push(item);
        }
      }

      return links;
    },
    getCombos() {
      if (this.isCache) {
        return [
          { id: "combo1", label: leavelMap.combo1, type: 'backbone', },
          { id: "combo2", label: leavelMap.combo2, type: 'region', },
          { id: "combo3", label: leavelMap.combo3, type: "local", },
          { id: "combo4", label: leavelMap.combo4, type: "access", },
          { id: "combo5", label: leavelMap.combo5, type: "unknown", },
        ]
      } else {
        return [
          { id: "combo1", label: leavelMap.combo1, type: 'backbone', x: this.combosStyle.width, y: this.combosStyle.height * 0.5, },
          { id: "combo2", label: leavelMap.combo2, type: 'region', x: this.combosStyle.width, y: this.combosStyle.height * 1.8, },
          { id: "combo3", label: leavelMap.combo3, type: "local", x: this.combosStyle.width, y: this.combosStyle.height * 3.1, },
          { id: "combo4", label: leavelMap.combo4, type: "access", x: this.combosStyle.width, y: this.combosStyle.height * 4.4, },
          { id: "combo5", label: leavelMap.combo5, type: "unknown", x: this.combosStyle.width, y: this.combosStyle.height * 5.7, },
        ]
      }

    },
    getNodesList(allNodes) {
      let comboMap = {
        [leavelMap.combo1]: 'combo1',
        [leavelMap.combo2]: 'combo2',
        [leavelMap.combo3]: 'combo3',
        [leavelMap.combo4]: 'combo4',
        [leavelMap.combo5]: 'combo5'
      }
      const nodesMap = {
        [leavelMap.combo1]: 0,
        [leavelMap.combo2]: 0,
        [leavelMap.combo3]: 0,
        [leavelMap.combo4]: 0,
        [leavelMap.combo5]: 0
      }
      const nodesMap2 = {
        [leavelMap.combo1]: 0,
        [leavelMap.combo2]: 0,
        [leavelMap.combo3]: 0,
        [leavelMap.combo4]: 0,
        [leavelMap.combo5]: 0
      }

      let nodes = [];

      for (let item of allNodes) {
        let nodeType = item.netLevelName;
        if (nodeType) {
          nodesMap[nodeType]++
        }
      }
      //获取最多的节点
      let lenArr = Object.values(nodesMap).map(item => {
        return item
      })
      let maxNodeLen = Math.max(...lenArr);
      let canvasWidth = (maxNodeLen > 80 ? 80 : maxNodeLen) * 150;
      let containerW = this.$refs.container.offsetWidth;
      let containerH = this.$refs.container.offsetHeight;
      // this.canvansStyle.width = canvasWidth >= containerW ? containerW : canvasWidth;
      this.canvansStyle.width = containerW;
      this.canvansStyle.height = containerH;
      this.combosStyle.width = this.canvansStyle.width * 0.5;
      this.combosStyle.height = this.canvansStyle.height * 0.15;
      for (let item of allNodes) {
        let nodeType = item.netLevelName;
        nodesMap2[nodeType]++
        let nodeMode = {
          id: item.id,
          label: item?.trsNetName ? `${item?.trsNetName?.trim()}` : '',
          // img: green,
          img: this.defaultNetElement,
          comboId: comboMap[nodeType] || 'combo5',
          data: item,
          x: (this.isCache ? item.x : nodesMap2[nodeType] * 150 - this.canvansStyle.width / 2),
          y: (this.isCache ? item.y : 0),
          colourType: item.colourType || '0',
          size: !item.id.includes('combo') ? [40, 40,] : [1, 1]
        }

        nodes.push(nodeMode);
      }

      return {
        nodes,
      };
    },
    initTopo() {

      const tooltip = this.drawTootip();

      this.registerCombo("backbone", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("region", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("local", { width: this.combosStyle.width, height: this.combosStyle.height });
      // this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height, refY: 90 });
      this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("unknown", { width: this.combosStyle.width, height: this.combosStyle.height });

      this.registerEdge("line-arrow");
      const toolbar = new G6.ToolBar({
        position: { x: 10, y: -50 },
      });
      // this.registerCustomNode("myNode");
      // console.log(this.jsonData)
      let g6Conf = [

        { type: 'drag-canvas', enableOptimize: false },
        {
          type: 'drag-node',
          enableOptimize: false,
          onlyChangeComboSize: true,
          // enableDelegate: true,
          multiple: true,
        },
        {
          type: 'drag-combo',
          enableOptimize: true,
          onlyChangeComboSize: true,
          enableDelegate: false,
        },
        { type: 'zoom-canvas', sensitivity: 1, enableOptimize: false, optimizeZoom: 0.5 },
        { type: 'brush-select', enableOptimize: true },
      ]


      const graph = new G6.Graph({
        container: "container",
        width: this.canvansStyle.width,
        height: this.canvansStyle.height,
        groupByTypes: false,
        // linkCenter: true,
        maxZoom: 1,
        modes: {
          default: g6Conf,
        },
        defaultNode: {
          size: [40, 40],
          type: "image",
          img: green,
          style: {
            // lineWidth: 20,
            stroke: "#000",
            // fill: "#C6E5FF",
            // radius: 5
          },
        },
        defaultEdge: {
          type: "quadratic",
          style: {
            lineWidth: 2,
            stroke: this.defaultLineColor,         //  默认线路颜色
          },
          labelCfg: {
            autoRotate: true,
          },
        },
        nodeStateStyles: {
          active: {
            stroke: "#01E2B5",
            // shadowColor: "#01E2B5",
            shadowColor: "yellow",
            lineWidth: 100,
            strokeOpacity: 0.5,
            shadowBlur: 10,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            "text-shape": {
              fill: "#01E2B5",
            },
            opacity: 1,
          },
          inactive: {
            opacity: 0.6,
          },
        },
        comboStateStyles: {
          active: {
            fill: "rgba(255,255,255,0)",
            opacity: 1,
            stroke: "#0AEFC5",
            "text-shape": {
              fill: "#01E2B5",
            },
            lineWidth: 5,
          },
          inactive: {
            lineDash: [5, 5],
            opacity: 1,
            fill: "rgba(255,255,255,0)",
            lineWidth: 2,
          },
        },

        edgeStateStyles: {
          active: {
            lineWidth: 5,
            // stroke: "yellow",
            stroke: this.selectLineColor,   // 选中颜色
          },
          inactive: {
            lineWidth: 2,
            stroke: "#fff",
          },
          hover: {
            lineWidth: 5,
            stroke: "#0AEFC5",
          },
          warn: {
            lineWidth: 5,
            // stroke: "red",
            stroke: this.alarmLineColor,       //  告警颜色
          }
        },
        // plugins: [tooltip],
        plugins: [tooltip, toolbar,],
        enabledStack: true, //启用redo && undo栈功能
      });

      graph.node(function (node) {
        let position = "bottom";
        let rotate = 0;
        if (!node.children) {
          position = "bottom";
          rotate = -Math.PI / 8;
        }
        return {
          label: node.label,
          labelCfg: {
            position,
            // offset: 50,
            style: {
              textAlign: "center",
              rotate,
              fill: "#fff",
            },
          },
        };
      });

      graph.on('node:dblclick', e => {
        if (e.item.getModel().colourType == '-1') {
          console.log(e.item.getModel());
          this.eqpId = e.item.getModel().id;
          this.getAlarmByEqpId()
          this.detailModal = true;
        }
        
      })
      graph.on('node:mouseenter', e => {
        // console.log(e);
        graph.setItemState(e.item, 'active', true)
        let edgeItem = e.item
        edgeItem.getEdges().forEach(edge => {
          graph.setItemState(edge.getTarget(), 'active', true)
          graph.setItemState(edge.getSource(), 'active', true)
          graph.setItemState(edge, 'hover', true)
        })
      })

      graph.on('node:mouseleave', e => {
        graph.setItemState(e.item, 'active', false)
        graph.getNodes().forEach(node => {
          if (node.getModel().colourType == '0') {
            graph.clearItemStates(node)
          }
        })
        graph.getEdges().forEach(edge => {
          let colourType = edge.getModel().colourType
          graph.clearItemStates(edge)
          if (colourType === '1') {
            this.graph.setItemState(edge, 'active', true)
          } else if (colourType === '-1') { //告警
            this.graph.setItemState(edge, 'warn', true)
          }
        })
        
        // graph.paint()
        // graph.setAutoPaint(true)
      })

      graph.on('dragend', async (evt) => {
        const { item } = evt;
        if (!item) return;
        // const model = item.getModel();
        // const { x, y, width, height } = item.getBBox();
        // const edges = this.graph.save().edges;
        const { nodes, } = this.graph.save();
        let sendNodes = [];
        nodes.forEach(item => {
          let curMode = {
            id: item.id,
            x: item.x,
            y: item.y,
            custId: this.custId,
            trsNetId: item.data?.trsNetId || item.id,
            netLevelName: item.data?.netLevelName || item.netLevelName
          }
          // if (!item.id.includes('combo')) {
          sendNodes.push(curMode)
          // }
        })

        // console.log(sendNodes)
        if (this.enableSave) {
          await this.$api.topo.updateTrsNode(sendNodes);
        }
      });


      if (typeof window !== "undefined")
        window.onresize = () => {
          if (!graph || graph.get("destroyed")) return;
          const container = document.getElementById('container');
          const width = container.scrollWidth;
          const height = container.scrollHeight || window.innerHeight;
          if (!container || !container.scrollWidth || !container.scrollHeight)
            return;
          graph.changeSize(width, height);
        };
      graph.data(this.jsonData);

      graph.render();
      graph.fitView();
      this.graph = graph;
    },

    registerEdge(linkName) {
      G6.registerEdge(linkName, {
        itemType: "edge",
        draw: function draw(cfg, group) {
          var startPoint = cfg.startPoint,
            endPoint = cfg.endPoint;

          var keyShape = group.addShape("path", {
            attrs: {
              path: [
                ["M", startPoint.x, startPoint.y],
                ["L", endPoint.x, endPoint.y],
              ],
              stroke: "#0CC",
              lineWidth: 1,
              // startArrow: {
              //   path: "M 10,0 L -10,-10 L -10,10 Z",
              //   path: G6.Arrow.vee(15, 20, 15),
              //   d: 15,
              // },
              endArrow: {
                path: G6.Arrow.vee(5, 5, 5),
                d: 5,
              },
            },
          });
          return keyShape;
        },
      });
    },

    registerCombo(comboName, cusConf) {
      let _this = this;
      G6.registerCombo(
        comboName,
        {
          drawShape: function drawShape(cfg, group) {
            const self = this;
            // 获取配置中的 Combo 内边距
            cfg.padding = cfg?.padding || [0, 0, 0, 0];
            cfg.labelCfg = {
              refY: cusConf?.refY || 40,
              position: "top",
              style: {
                fontSize: 18,
                fill: "#fff",
              },
            }
            cfg.size = [cusConf?.width || cfg.width, cusConf?.height || cfg.height];
            // 获取样式配置，style.width 与 style.height 对应 rect Combo 位置说明图中的 width 与 height
            const style = self.getShapeStyle(cfg);
            // 绘制一个矩形作为 keyShape，与 'rect' Combo 的 keyShape 一致
            const rect = group.addShape("rect", {
              attrs: {
                allowZoom: true,
                allowDrag: true,
                ...style,
                x: -style.height / 2 - cfg.padding[0],
                y: -style.width / 2 - cfg.padding[3],
                lineWidth: 2,
                fill: "rgba(255,255,255,0)",
                opacity: 1,
                // stroke: "#fff",
                stroke: _this.defaultHierarchyColor,      // 默认图层颜色
                // stroke: cusConf?.stroke || '#fff',
                lineDash: [5, 5],
              },
              draggable: false,
              name: "combo-keyShape" + comboName, // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
            });

            return rect;
          },
        },
        "rect"
      );
    },

    // 画画线
    drawLink() {
      let len = this.jsonData.nodes.length || 0;
      let nodeList = this.jsonData.nodes;

      for (let i = 0; i < len; i++) {
        if (nodeList[i + 1]) {
          let item1 = nodeList[i];
          let item2 = nodeList[i + 1];
          let source = item1.id;
          let target = item2.id;

          this.graph.addItem("edge", {
            source,
            target,
          });
        }
      }
    },
    // 提示框
    drawTootip() {
      let tooltip = new G6.Tooltip({
        offsetX: 20,
        offsetY: 0,
        fixToNode: [1, 1],
        // trigger: 'click',
        itemTypes: ['node'],
        getContent(e) {
          const outDiv = document.createElement("div");
          outDiv.style.minWidth = "300px";
          // console.log(e.item.getModel());
          let curData = e.item.getModel();

          if (curData?.data) {
            let obj = curData?.data;
            outDiv.innerHTML = `
              <ul>
                <li class="node-cls">
                  <span>网元ID : </span> <span>${obj.id}</span>
                </li>
                <li class="node-cls">
                  <span>网元名称 : </span> <span>${obj.label}</span>
                </li>
                <li class="node-cls">
                  <span>所属层级 : </span><span>${obj.netLevelName}</span>
                </li>
              </ul>
            `;
          } else if (curData.label) {
            outDiv.innerHTML = `
              <ul>
                <li>业务名称：${curData.label || ""}</li>
              </ul>
            `;
          } else {
            return "";
          }

          return outDiv;
        },
      });

      return tooltip;
    },

    //告警弹窗
    handleClick(tab, event) {
      this.activeName = tab.name;
      this.getAlarmByEqpId()
    },
    getAlarmByEqpId(){
      this.$api.slaApi.getAlarmByEqpId({
        locateNeType: this.activeName,
        eqpId: this.eqpId,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      })
			.then(res =>{
        this.alarmListData = res.records;
        this.total = res.total;
      })
    },
    getDetailBasic(row) {
			// this.diaLoading = true;
			this.mainFormId = row.workOrderNumber;
			// this.mainFormId = 'QDKH-20240115-59338479';
			// console.log(this.mainFormId);
			// this.detailData.basic.title = '基础业务信息';
		},
    getDetail(row) {
			this.$nextTick(() => {
				this.getDetailBasic(row);
				this.detailModals = true;
				this.selRow = row;
			});
		},
    getPage(page) {
			this.pageNum = page.pageNum; // 当前页
			this.pageSize = page.pageSize; // 当前页
			this.total = page.total; // 当前页
			this.getAlarmByEqpId();
		},
  },
};
</script>


<style lang="less" scoped>
.topoContainer {
  position: relative;

  #container {
    display: flex;
    flex: 1;
    height: 750px;
    overflow: auto;


  }

  #container::-webkit-scrollbar-thumb {
    background-color: #158C8D;
  }

  #container::-webkit-scrollbar-track {
    background-color: #041440;
  }


  .imgTuli {
    position: absolute;
    top: -60px;
    left: 0px;
    padding: 16px;
    display: flex;
    color: #fff;
    max-height: 50px;
    width: 100%;

    .enableCls {
      // margin-left: 510px;
      position: absolute;
    right: 16%;
    }

    .lujing {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;

      .workcls {
        width: 50px;
        height: 2px;
        background-color: #fff;
        margin-right: 12px;
      }

      .protectCls {
        width: 50px;
        height: 2px;
        background: red;
        margin-right: 12px;

      }

    }

    .imgCls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;

      img {
        display: block;
        width: 30px;
        margin-right: 12px;
      }
    }
  }

  .el-loading-mask {
    background: rgba(5, 99, 153, 0.3);
  }

  :deep(.el-switch__label) {
    color: #5e5959;
  }

  :deep(.el-switch__label.is-active) {
    color: #409EFF;
  }
  ::v-deep {
    .el-button--primary {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;
    }
		.el-dialog {
			height: 80vh;
            width: 60%;
			background: url("../../../assets/images/dialogbg.png") center center
              no-repeat;
            background-size: 100% 100%;
			margin-top: 10vh !important;
		}
    .alarmList .el-dialog {
      height: 50vh;
            width: 80%;
            // .el-table {
            //   height: 30vh !important;
            // }
    }
		.el-dialog__header {
			justify-content: left;
			background: none;
		}
		.el-dialog__title {
			justify-content: left;
            line-height: 20px;
            font-weight: 800;
            height: 20px;
            display: flex;
            color: rgba(255, 255, 255, 1);
            font-size: 22px;
		}
		.el-dialog__title::before ,
		.el-dialog__title::after{
            content:none;
        }
		.el-dialog__headerbtn {
			    width: 30px;
            height: 30px;
            float: right;
            top: 10px;
            right: 10px;
			background: url("../../../assets/images/x.png") center center
              no-repeat;
            background-size: 100% 100%;
		}
		.el-dialog__body {
			height: 95%;
			
			.detail-info {
				max-height: 800px;
				height: 100%;
			}
			.el-form {
				min-height: 100px;
				width: 80%;
				margin: 30px auto 20px;
				text-align: center;
			}
		}
	}
  ::v-deep {
    .el-tabs__item {
      color: #fff;
    }
    .el-tabs__content {
      height: 40vh;
    }
    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
