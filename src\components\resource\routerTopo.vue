<template>
  <div class="topoContainer">
    <div id="container"></div>
  </div>
</template>
<script>
  import G6 from "@antv/g6";
  import green from "@/assets/images/topo/img2.png";
  import yellow from "@/assets/images/topo/img1.png";
  import red from "@/assets/images/topo/img3.png";
  import result from "@/assets/images/topo/result.png";

  let status = {
    0: "正常",
    1: "中断",
    2: "未知",
  };
  let dashStyle = {
    lineDash: [10, 5],
    endArrow: true,
  };


  export default {
    name: "topoPage",
    props: {
      sendData: {
        type: Array,
      },
      selRow: {
        type: Object,
      },
    },
    data() {
      return {
        jsonData: {
          // id: "01",
          // label: "业务类型",
          // img: img1,
          // children: [],
          nodes: [],
        },
        imgMap: {
          green,
          yellow,
          red,
        },
        sendDatas: [],
      };
    },
    watch: {
      sendDatas: {
        handler(n, o) {
          let primary = n;
          let secondary = n?.secondary || [];
          let imgMap = {
            0: green,
            1: red,
            2: yellow,
          };
          let primaryList = primary.map((item, index) => {
            return {
              id: item.id,
              label: item.label,
              img: result,
              data: item,
            };
          });
          let secondaryList = secondary.map((item, index) => {
            return {
              // id: item.nodeUuid + "index=" + index,
              // label: `${item?.name?.trim()}(${item.linkTpId}) `,
              id: item.aportId + '',
              label: `${item?.aportName?.trim()}`,
              img: imgMap[item?.communicationState],
              data: item,
            };
          });
          
           let allNodes = primaryList;

          let nodes = this.deduplication(allNodes);
          let pEdgeList = this.getEdges(primaryList, "working"); //公祖路径

          let edges = this.dealEdges([...pEdgeList]);

          let jsonData = {
            nodes,
            edges,
          };

          this.jsonData = jsonData;
          // this.jsonData = dataJson;
          this.updateTopo();
          this.drawLink();
        },
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      initData() {
        // 1. 提取所有唯一的节点（去重）
        const nodeNames = new Set();
        this.sendData.forEach(item => {
          nodeNames.add(item.aTrsNeName);
          nodeNames.add(item.zTrsNeName);
        });
        
        // 2. 构建节点数据
        const nodes = Array.from(nodeNames).map(name => ({
          id: name,
          label: this.superLongTextHandle(name,180,12),
          img: result, // 默认图片
          data: {
            aPortName: this.sendData.find(item => item.aTrsNeName === name)?.aPortName || '',
            zPortName: this.sendData.find(item => item.zTrsNeName === name)?.zPortName || ''
          }
        }));
      
        // 3. 构建连线数据
        const edges = this.sendData.map(item => ({
          source: item.aTrsNeName,
          target: item.zTrsNeName,
          label: this.superLongTextHandle(`${item.aPortName || ''}->\n${item.zPortName || ''}`,150,12),
          type: item.linkStatus === "断链" ? "dashed-line" : "line-arrow", // 断链用虚线，正常用实线
          style: {
            stroke: item.linkStatus === "断链" ? "#ff4d4f" : "#1890ff", // 断链红色，正常蓝色
            lineWidth: 2,
            endArrow: true
          },
          labelCfg: {
            position: 'center',  // 关键配置：标签位置在连线中间
            refY: -55,          // 关键配置：垂直偏移量（负值表示向上移动）
            style: {
              fill: "#409EFF",
              fontSize: 12,
              background: {
                // fill: "rgba(0,0,0,0.7)",  // 半透明背景更美观
                padding: [4, 8, 4, 8],    // 增加内边距
                radius: 4
              }
            }
          },
          data: item
        }));
      
      
        // 4. 更新拓扑图数据
        this.jsonData = { nodes, edges };
        this.initTopo();
      },
      //数组去重
      deduplication(arr) {
        let newArr = [];
        let idArr = [];
        for (let item of arr) {
          if (!idArr.includes(item.label)) {
            newArr.push(item);
            idArr.push(item.label);
          }
        }
        return newArr;
      },
      // 线段数组
      getEdges(nodeList, flag) {
        let links = [];
        let typeCls = {};

        if (flag === "protection") {
          typeCls = {
            style: dashStyle,
          };
        } else {
          typeCls = {
            type: "line-arrow",
          };
        }
        for (let i = 0; i < nodeList.length; i++) {
          if (nodeList[i + 1]) {
            let item1 = nodeList[i];
            let item2 = nodeList[i + 1];
            let source = item1.id;
            let target = item2.id;
            links.push({
              source,
              target,
              ...typeCls,
            });
          }
        }
        return links;
      },
      dealEdges(linkList) {
        let links = [];
        let tmpArr = [];
        let repeatArr = [];
        //   sourceAnchor: 4,
        // targetAnchor: 2,

        for (let i = 0; i < linkList.length; i++) {
          let item = linkList[i];
          let idStr = item.source + item.target;
          if (!tmpArr.includes(idStr)) {
            tmpArr.push(idStr);
          } else {
            repeatArr.push(idStr);
          }
        }
        for (let i = 0; i < linkList.length; i++) {
          let item = linkList[i];
          let idStr = item.source + item.target;
          if (repeatArr.includes(idStr)) {
            if (item.type === "line-arrow") {
              //工作路径
              links.push({
                ...item,
                sourceAnchor: 4,
                targetAnchor: 2,
              });
            } else {
              //保护路径
              links.push({
                ...item,
                sourceAnchor: 5,
                targetAnchor: 3,
              });
            }
          } else {
            links.push(item);
          }
        }

        return links;
      },
      initTopo() {
        const container = document.getElementById("container");
        const width = container.scrollWidth;
        const height = container.scrollHeight || 350;
      
        // 注册自定义连线类型
        G6.registerEdge("dashed-line", {
          draw(cfg, group) {
            const { startPoint, endPoint } = cfg;
            return group.addShape("path", {
              attrs: {
                path: [
                  ["M", startPoint.x, startPoint.y],
                  ["L", endPoint.x, endPoint.y]
                ],
                stroke: cfg.style?.stroke || "#ff4d4f", // 默认红色
                lineDash: [5, 5],  // 虚线样式
                lineWidth: 2,
                endArrow: cfg.style?.endArrow || false
              }
            });
          }
        });
        const graph = new G6.Graph({
          container: "container",
          width,
          height,
          modes: {
            default: ["drag-canvas", "zoom-canvas", "drag-node","activate-relations",]
          },
          defaultNode: {
            type: "image",
            size: [40, 40],
            img: green,
            labelCfg: {
              position: "bottom",
              style: {
                fill: "#fff",
                fontSize: 12
              }
            },
            
          },
          maxZoom: 1.5,
          defaultEdge: {
            type: "line",
            style: {
              stroke: "#ccc",
              lineWidth: 2,
              endArrow: true
            }
          },
          edgeStateStyles: {
            hover: {
              stroke: "#ff8800",
              lineWidth: 3
            }
          },
          plugins: [this.drawTootip()], // 添加悬停提示
          layout: {
            type: "dagre",
            rankdir: "LR",
            align: 'UL',
            nodesep: 30,
            ranksep: 100
          }
        });
      
        graph.data(this.jsonData);
        graph.render();
        // graph.fitView();
        if (typeof window !== "undefined")
          window.onresize = () => {
            if (!graph || graph.get("destroyed")) return;
            if (!container || !container.scrollWidth || !container.scrollHeight)
              return;
            graph.changeSize(container.scrollWidth, container.scrollHeight);
          };
        this.graph = graph;
      },
      updateTopo() {
        this.graph.data(this.jsonData);
        this.graph.render();
        setTimeout(() => {
          this.graph.zoomTo(1.5);
          // this.graph.fitView();
        });
      },

      // 画画线
      drawLink() {
        console.log(this.jsonData.nodes);
        let len = this.jsonData.nodes.length || 0;
        let nodeList = this.jsonData.nodes;

        for (let i = 0; i < len; i++) {
          if (nodeList[i + 1]) {
            let item1 = nodeList[i];
            let item2 = nodeList[i + 1];
            let source = item1.data.zportId;
            let target = item2.data.aportId;

            this.graph.addItem("edge", {
              source,
              target,
              // style: {
              //   endArrow: true,
              // },
              type: "line-arrow",
            });
          }
        }
      },
      // 提示框
      drawTootip() {
        return new G6.Tooltip({
          offsetX: 10,
          offsetY: 20,
          getContent(e) {
            if (e.item.getType() !== 'edge') return null;
      
            const model = e.item.getModel();
            const data = model.data || {};
      
            const outDiv = document.createElement('div');
            outDiv.style.width = '300px';
            outDiv.style.background = '#fff';
            outDiv.style.padding = '10px';
            outDiv.style.borderRadius = '4px';
            outDiv.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
      
            outDiv.innerHTML = `
              <ul>
                <li class="node-cls">
                  <span>A端设备：</span><span>${data.aTrsNeName}</span>
                </li>
                <li class="node-cls">
                  <span>A端端口：</span><span>${data.aPortName}</span>
                </li>
                <li class="node-cls">
                  <span>Z端设备：</span><span>${data.zTrsNeName}</span>
                </li>
                <li class="node-cls">
                  <span>Z端端口：</span><span>${data.zPortName}</span>
                </li>
                <li class="node-cls">
                  <span>路由类型：</span><span>${data.routeIsbak}</span>
                </li>
              </ul>
            `;
            return outDiv;
          },
          itemTypes: ['edge']
        });
        
                // <li class="node-cls">
                //   <span>链路状态：</span><span style="color: ${data.linkStatus === '断链' ? '#ff4d4f' : '#52c41a'}">${data.linkStatus || '正常'}</span>
                // </li>
      },
      
      // drawTootip() {
      //   return new G6.Tooltip({
      //     offsetX: 10,
      //     offsetY: 20,
      //     getContent(e) {
      //       const model = e.item.getModel();
      //       const data = model.data || {};
      //       const isEdge = e.item.getType() === "edge";
      
      //       const outDiv = document.createElement("div");
      //       outDiv.style.width = "300px";
      //       outDiv.style.background = "#fff";
      //       outDiv.style.padding = "10px";
      //       outDiv.style.borderRadius = "4px";
      //       outDiv.style.boxShadow = "0 0 10px rgba(0,0,0,0.2)";
      
      //       if (isEdge) {
      //         // 连线提示
      //         outDiv.innerHTML = `
      //           <h4>链路详情</h4>
      //           <p><b>A端设备：</b>${data.aTrsNeName}</p>
      //           <p><b>A端端口：</b>${data.aPortName}</p>
      //           <p><b>Z端设备：</b>${data.zTrsNeName}</p>
      //           <p><b>Z端端口：</b>${data.zPortName}</p>
      //           <p><b>路由类型：</b>${data.routeIsbak}</p>
      //         `;
      //       } else {
      //         // 节点提示
      //         outDiv.innerHTML = `
      //           <h4>节点详情</h4>
      //           <p><b>名称：</b>${model.label}</p>
      //           <p><b>A端端口：</b>${data.aPortName || '-'}</p>
      //           <p><b>Z端端口：</b>${data.zPortName || '-'}</p>
      //         `;
      //       }
      //       return outDiv;
      //     },
      //     itemTypes: ["node", "edge"] // 同时支持节点和连线的提示
      //   });
      // },
      // G6换行符处理超长文本
    superLongTextHandle(str, maxWidth, fontSize) {
      let currentWidth = 0;
      let res = str;
      // 区分汉字和字母
      const pattern = new RegExp("[\u4E00-\u9FA5]+");
      str.split("").forEach((letter, i) => {
        if (currentWidth > maxWidth) return;
        if (pattern.test(letter)) {
          // 中文字符
          currentWidth += fontSize;
        } else {
          // 根据字体大小获取单个字母的宽度
          currentWidth += G6.Util.getLetterWidth(letter, fontSize);
        }
        if (currentWidth > maxWidth) {
          res = `${str.substr(0, i)}\n${this.superLongTextHandle(
            str.substr(i),
            maxWidth,
            fontSize
          )}`;
        }
      });
      return res;
    },
    },
  };
</script>

<style lang="less" scoped></style>

<style lang="less">
  .g6-component-tooltip {
    background-color: #062856 !important;
    border: none;
  div {
    background-color: #062856 !important
  }
  .node-cls {
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px dashed #eee;
    
    &:last-child {
      // border-bottom: none;
    }
    
    span:first-child {
      color: #fff;
      font-weight: bold;
    }
    
    span:last-child {
      color: #fff;
    }
  }
}

/* 电路连接线高亮样式 */
.g6-edge:hover {
  stroke: #ff7d4f !important;
  line-width: 3 !important;
}
  .topoContainer {
    position: relative;
    .tuli {
      position: absolute;
      bottom: 20px;
      left: 30px;
      background-color: #fff;
      padding: 16px;
      .lujing {
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .workcls {
          width: 80px;
          height: 2px;
          background-color: #2ecccc;
        }
        .protectCls {
          width: 80px;
          height: 2px;
          background: repeating-linear-gradient(
            to right,
            #fff 0px,
            #fff 0px,
            #ccc 4px,
            #ccc 8px
          );
        }
      }
    }
    .imgTuli2 {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background-color: transparent;
      padding: 16px;
      display: flex;
      .imgCls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 24px;
        img {
          width: 30px;
          margin-left: 8px;
        }
      }
    }
  }
</style>
