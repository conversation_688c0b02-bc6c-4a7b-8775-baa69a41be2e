
export default {
    bind(el, binding) {
        //拿到当前用户的所有的权限  假设权限列表['add','view','update']
        const permissions = localStorage.getItem('permissions')
        // const permissions = ['add','view','custupdate','100023','circuitadd'];
        //当前页面需要的权限
        const needPermissions = binding.value
        console.log(permissions);
        //判读是否有权限
        const hasPermission = permissions.includes(needPermissions)
        //如果没有权限 就将该按钮隐藏 这里需要做一个延时处理，因为我们需要等待dom加载结束再进行处理Node节点
        if (!hasPermission) {
            setTimeout(() => {
                el.parentNode.removeChild(el)
            }, 0)
        }
    }
}
