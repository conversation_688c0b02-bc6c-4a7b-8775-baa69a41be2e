apiVersion: v1
kind: Service
metadata:
  name: bigscreen
  namespace: default
spec:
  selector:
    app: bigscreen
  ports:
    - port: 18082
      targetPort: 18082
---
apiVersion: apps/v1
kind: Deployment
metadata: 
  name: bigscreen
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bigscreen
  template:
    metadata: 
      labels: 
        app: bigscreen
    spec:
      hostAliases:
        - ip: ************
          hostnames:
            - uac.sso.chinaunicom.cn
        - ip: ************
          hostnames:
            - uac.ssolf.chinaunicom.cn
      containers:
      - name: bigscreen
        image: harbor.dcos.ncmp.unicom.local/sxwlyydd/bigscreen-#{MODE}#:v1
        imagePullPolicy: Always
        env:
          - name: realVersion
            value: "#{Build.BuildNumber}#"
        ports:
        - name: http
          containerPort: 18082