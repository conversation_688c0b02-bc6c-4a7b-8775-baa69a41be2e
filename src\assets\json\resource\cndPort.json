{"queryConfig": {"columns": [{"displayName": "端口名称", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "portname", "tableColumnName": "portname"}, {"displayName": "端口ID", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "id", "tableColumnName": "id"}, {"displayName": "所属设备ID", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "deviceid", "tableColumnName": "deviceid"}], "classId": "cndPort", "className": "端口"}, "tableConfig": {"columns": [{"displayName": "端口ID", "type": "string", "columnName": "id"}, {"displayName": "所属设备ID", "type": "string", "columnName": "deviceid"}, {"displayName": "端口名称", "type": "string", "columnName": "portname"}, {"displayName": "端口描述", "type": "string", "columnName": "portdesc"}, {"displayName": "端口物理类型", "type": "string", "columnName": "physicalintftype"}, {"displayName": "端口速率", "type": "string", "columnName": "portspeed"}, {"displayName": "设备IP", "type": "string", "columnName": "deviceip"}, {"displayName": "槽位号", "type": "string", "columnName": "slotNo"}, {"displayName": "子卡号", "type": "string", "columnName": "subslotNo"}], "selectColumns": ["id", "deviceid", "portname", "portdesc", "physicalintftype", "portspeed", "deviceip", "slotNo", "subslotNo"]}}