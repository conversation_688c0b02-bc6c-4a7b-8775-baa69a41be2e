<template>
<div style="height: 70%;">
  <div class="nc-query-eqp">
    <el-form :inline="true" :model="form" size="small" label-width="100px">
      <el-form-item class="formItem" label="电路名称">
        <el-input v-model="form.circuitName" placeholder="电路名称" clearable></el-input>
      </el-form-item>
      <!-- <el-form-item class="formItem" label="客户经理">
        <el-input v-model="form.managerMan" placeholder="客户经理" clearable></el-input>
      </el-form-item>
      <el-form-item class="formItem" label="A端归属地市">
        <el-input v-model="form.amaintainArea" placeholder="A端归属地市" clearable></el-input>
      </el-form-item>
      <el-form-item class="formItem" label="Z端归属地市">
        <el-input v-model="form.zmaintainArea" placeholder="Z端归属地市" clearable></el-input>
      </el-form-item>
      <el-form-item class="formItem" label="电路带宽">
        <el-select v-model="form.circuitRate" filterable placeholder="电路带宽" clearable>
          <el-option class="infinite-list-item" v-for="(option, index) in getCircuitRate"
            :key="option.circuitRate + index" :label="option.circuitRate" :value="option.circuitRate"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="formItem" label="客户名称">
        <el-input v-model="form.custName" placeholder="客户名称" clearable></el-input>
      </el-form-item> -->

      <el-form-item style="float:right;width:5%">
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button type="primary" @click="selectAllUserInfo">查询</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div style="margin:0 0 15px 0">
    <el-button size="mini" :disabled='multipleSelection.length !== 1' @click="custInfor">确认</el-button>
    <el-button size="mini" v-if="className == 'cndCircuit'" @click="noCustInfor">没有可用电路</el-button>
  </div>
  <el-table v-loading="loading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)" border :data="tableBody" :header-cell-style="{
      color: '#9ED8FF',
      'background-color': 'rgba(15, 52, 124, 1)',
      'text-align': 'center',
      'font-size': '14px',
      'border-right': '1px rgba(15, 0, 137, .8) solid'
    }" height="450" style="border-radius: 4px 4px 0 0;
      border:none;margin-top: 5px;" @selection-change="handleSelectionChange">
    <el-table-column class-name="tabSel" type="selection" width="55" align="center">
    </el-table-column>
    <el-table-column align='center' v-for="(i, idx) in tableColumns" :key="idx" :label="i.displayName"
      :prop="snakeToCamel(i.columnName)" show-overflow-tooltip>
    </el-table-column>
  </el-table>
  <el-pagination class="page-list" @size-change="handleSizeChange" @current-change="handleCurrentChange"
    :current-page.sync="pageNumber" :page-sizes="[1, 10, 20, 30, 40, 50]" :page-size="pageSize" background
    layout="total, prev, pager, next, sizes" :total.sync="total" style="float: right;">
  </el-pagination>
</div>
</template>

<script>
export default {
  props: {
    eqpDialog: {
      type: Boolean, //要求传递的类型,大写开头
      required: true, //强制传递,否则报错
    },
    className:{
      type: String,
      required: true
    },
  },
  watch: {
    eqpDialog(val) {
      this.multipleSelection=[];
      if (!val) {
        this.resetQuery()
      }
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      detailModal: false,
      form: {},
      formData: {},
      total: 10,
      pageSize: 10,
      pageNumber: 1,
      allCityData: [],
      getCircuitRate: [],
      allCustUserRoles: [],
      roleList: [],
      custIdList: [],
      tableColumns: [{
        displayName: '电路名称',
        columnName: 'CIRCUIT_NAME'
      }, {
        displayName: '电路编号',
        columnName: 'CIRCUIT_CODE'
      }, {
        displayName: '电路ID',
        columnName: 'CIRCUIT_ID'
      }, {
        displayName: '调单编号',
        columnName: 'ADJUST_NUM'
      }, {
        displayName: '核心网设备名称',
        columnName: 'CORE_NET_DEVICE_NAME'
      }, {
        displayName: '分组编号',
        columnName: 'GROUP_NUMBER'
      }, {
        displayName: 'A端归属区域',
        columnName: 'A_AREA_NAME'
      }, {
        displayName: 'A端设备名称',
        columnName: 'A_DEVICE_NAME'
      }, {
        displayName: 'A端设备端口',
        columnName: 'A_DEVICE_PORT'
      }, {
        displayName: 'A端设备类型',
        columnName: 'A_DEVICE_TYPE'
      }, {
        displayName: 'A端设备所在机房',
        columnName: 'A_DEVICE_ROOM'
      }, {
        displayName: 'Z端归属区域',
        columnName: 'Z_AREA_NAME'
      },
      {
        displayName: 'Z端设备名称',
        columnName: 'Z_DEVICE_NAME'
      },
      {
        displayName: 'Z端设备端口',
        columnName: 'Z_DEVICE_PORT'
      },
      {
        displayName: 'Z端设备类型',
        columnName: 'Z_DEVICE_TYPE'
      },
      {
        displayName: 'Z端设备所在机房',
        columnName: 'Z_DEVICE_ROOM'
      },
      {
        displayName: '带宽',
        columnName: 'BANDWIDTH'
      },
      {
        displayName: '电路承载类型',
        columnName: 'CIRCUIT_CARRIER_TYPE'
      },
      {
        displayName: 'AZ端设备是否同局',
        columnName: 'IS_AZ_DEVICE_SAME'
      },
      {
        displayName: 'VPN',
        columnName: 'VPN'
      },
      {
        displayName: '是否横联链路',
        columnName: 'IS_HORIZONTAL_LINK'
      },
      {
        displayName: '路径',
        columnName: 'ROUTE'
      },{
        displayName: '全程路由信息',
        columnName: 'FULL_ROUT INFORMATION'
      },],
      tableBody: [],
      userInformation: [],
      customerAdded: [],
      noCustomerAdded: [],
      customerName: '',
      deleteCustomer: [],
      multipleSelection: []
    }
  },
  created() {
    
    // this.getAllCounties()
    this.selectAllUserInfo()
  },
  methods: {
    snakeToCamel(str) {
  return str
    .toLowerCase() // 转为全小写
    .split('_')    // 按 _ 分割成数组
    .map((word, index) => 
      index === 0 
        ? word 
        : word[0].toUpperCase() + word.slice(1)
    )
    .join('');      // 合并成字符串
},
    // 表格复选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    custInfor() {
      this.$emit('eqpFrom', this.multipleSelection[0])
    },
    noCustInfor(){
      this.$emit('noCustInfor', false)
    },
    getAllCounties() {
      console.log(this.className);
      
        if (this.className == 'cndCircuit') {
          this.$api.transApi.getCircuitNameSelectData({circuitName: this.form.circuitName})
          .then(res => {
            this.getCircuitRate = res.data
          })
        } else {
          this.$api.transApi.getTransCircuitNameSelectData({circuitName: this.form.circuitName})
          .then(res => {
            this.getCircuitRate = res.data
          })
        }
    },
    selectAllUserInfo() {
      this.loading = true;
      let scope = this;
      let params = {
        circuitName: this.form.circuitName || '',
        "pageNum": this.pageNumber,
        "pageSize": this.pageSize
      }
      this.$api.transApi.getTransCircuitNameSelectData(params)
        .then(res => {
          this.tableBody = res.data.records;
          this.total = res.data.total;
          this.loading = false;
        })
    },

    transformData(data) {
      let transformedData = { where: {} };
      data.forEach(item => {
        for (let key in item) {
          let value = item[key]; // 假设统一使用$like作为条件 
          transformedData.where[key] = value;
        }
      });
      return transformedData;
    },
    // 重置表单
    resetQuery() {
      this.form = {};
      this.pageSize = 10;
      this.pageNumber = 1;
      this.selectAllUserInfo()
    },
    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.selectAllUserInfo();
    },
    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.selectAllUserInfo();
    },
  }
}
</script>

<style lang="less" scoped>
.nc-query-eqp {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
  //   height: 10vh;
  height: 85px;
}

/deep/ .el-checkbox__inner {
  background-color: transparent;
  border: 1px solid #02dbff;
}

::v-deep {
  .el-table__fixed-right-patch {
    background-color: rgba(23, 70, 137, 1);
  }

  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch {
    border: none;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }

  .el-dialog {
    // height: 30vh;
    // width: 60%;
    background: url("../../assets/images/dialogbg.png") center center no-repeat;
    background-size: 100% 100%;
    margin-top: 10vh !important;
  }

  .el-dialog__header {
    justify-content: left;
    background: none;
  }

  .el-dialog__title {
    justify-content: left;
    line-height: 20px;
    font-weight: 800;
    height: 20px;
    display: flex;
    color: rgba(255, 255, 255, 1);
    font-size: 22px;
  }

  .el-dialog__title::before,
  .el-dialog__title::after {
    content: none;
  }

  .el-dialog__headerbtn {
    width: 30px;
    height: 30px;
    float: right;
    top: 10px;
    right: 10px;
    background: url("../../assets/images/x.png") center center no-repeat;
    background-size: 100% 100%;
  }

  .el-dialog__body {
    height: 85%;

    .detail-info {
      max-height: 800px;
      height: 100%;
    }

    .el-form {
      min-height: 100px;
      width: 95%;
      // margin: 30px auto 20px;
      margin-top: 30px;
      text-align: center;

      .el-form-item__label {
        color: #fff;
      }

      .el-input__inner {
        color: #02dbff;
        background: none;
        border: 1px solid rgba(37, 190, 247, 0.5);
        // height: 24px !important;
        line-height: 24px;
      }

      .el-switch__label,
      .el-checkbox {
        color: #fff;
      }

      .el-button--primary {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;

        &:focus {
          background-color: #409EFF;
          border-color: #409EFF;
        }

        &:hover {
          background-color: #409EFF;
          border-color: #409EFF;
        }
      }

      .el-select-dropdown__item:hover {
        color: #409EFF;
        background: #40a0ff27;
      }

      .el-button--default {
        color: #409EFF;
        background: transparent;
        border-color: #409EFF;
      }
    }
  }
}
</style>