import G6 from "@antv/g6";

const getHiddenNodes = (combos, cobStyle) => {
  // let hiddenNodes = [{
  //     id: 'combo4id1',
  //     comboId: 'combo4',
  //     size: [1, 1],
  //     x: -600,
  //     y: -100,
  //   }, {
  //     id: 'combo4id2',
  //     comboId: 'combo4',
  //     size: [1, 1],
  //     x: 600,
  //     y: 100,
  //   }]
  //   { id: "combo1", label: "骨干网", type: 'backbone', x: 600, y: 50, },
  let height = cobStyle.height;

  let hiddenNodes = [];
  for (let item of combos) {
    hiddenNodes.push({
      id: item.id + "leftTop",
      comboId: item.id,
      size: [1, 1],
      x: -item.x,
      y: -height / 2,
      netLevelName: item.label,
    });
    hiddenNodes.push({
      id: item.id + "rightBtm",
      comboId: item.id,
      size: [1, 1],
      x: item.x,
      y: height / 2,
      netLevelName: item.label,
    });
  }
  return hiddenNodes;
};

const isCashed = (list) => {
  return list.some((item) => item.id.includes("combo"));
};

export { getHiddenNodes, isCashed };
