<template>
	<!-- 自定义列表 -->
	<div class="cust-act-table hf" 
          @click="circuitDetails($event)">
        <table id="tableH">
    		<!-- 设置列宽 -->
	    	<colgroup>
	    		<col width="30%">
	    		<col width="50%">
	    		<col/>
	    	</colgroup>
	    	<thead>
	    	<th>业务号码</th>
	    	<th>业务名称</th>
	    	<th>操作</th>
	    	</thead>
    	</table>
        <div class="warp">
          <ul class="item item-list" id="scroll-div">
            <li 
			        v-for="(item, index) in listData" 
			        :key="index" 
			        :ref="'oddRow'+index"
              class=""
			      >
              <span class="title t-ellipsis" :data-dept="JSON.stringify(item)" v-text="item.circuitNo"></span>
              <span class="date t-ellipsis" :data-dept="JSON.stringify(item)" v-text="item.circuitName"></span>
              <span class="date t-ellipsis tooltip" style="color: #19D6FF" :data-dept="JSON.stringify(item)" v-text="item.circuitName"></span>
            </li>
          </ul>
        </div>


		<el-dialog class="circuitDialog" :title="'电路详情 ['+circuitName+']'" :visible.sync="circuitDialog" :close-on-click-modal="false">
          <circuit-table-vue :tableBody='tableBody'/>
        </el-dialog>
	</div>
</template>

<script>
  import vueSeamlessScroll from 'vue-seamless-scroll'
import circuitTableVue from './circuitTable.vue';
import marquee from '@/components/common/marquee/index.vue';
import bus from "@/eventBus/bus.js";
	export default {
		name: "custActTable",
        components: {vueSeamlessScroll,circuitTableVue},
    props: {
    scorllBoxListName: {
      default: () => {
        return ["类别", "时间", "区域", "状态"];
      }
    },
    scorllHeight: {
      default: 180
    },
    scorllBoxList: {
      default: () => {
        return [];
      }
    }
  },
  //计算属性，获得可以操作的swiper对象
  computed: {
    mySwiper() {
      // mySwiper 是要绑定到标签中的ref属性
      return this.$refs.mySwiper.swiper
    }
  },
		data() {
			return {
				circuitDialog: false,
				listData: [],
                classOption: {
                  singleHeight: 60,
                  waitTime: 3000,
                },
                tableBody: [],
                circuitName: '',
                topoBody: [],
                timer1:"",
                artIndex: 0,
                timeA:null,
                timeB:null,
			};
		},
		watch: {
		},
    created(){
      
    },
		mounted() {
      this.getLineList()
      // if (this.timer1) {
      //   clearInterval(this.timer1); // 如果有先清除计数器
      // }
      // this.timer1 = setInterval(this.artIndexJ, 4000); // 开始计数
		},
    destroyed(){
      clearInterval(this.timeB);
        this.timeB = null;
        clearInterval(this.timeA);
        this.timeA = null;
        bus.$emit("circuitName", '');
    },
		methods: {
      artIndexJ(){
        let self = this;
        if (self.artIndex == self.listData.length+1) {
          // clearInterval(this.timer1);
          self.artIndex = 0
        } else {
          self.artIndex = Number(self.artIndex)+1
        }
        
      },
      getRouteDetailNe(circuitId){
        this.$api.slaApi.getRouteDetailNe(circuitId)
          .then((result) => {
            let res = result;
            if (res) {
              this.topoBody = res.data
              bus.$emit("topoBody", this.topoBody);
              // console.log(this.topoBody);
				      this.circuitDialog = true;
            }
          })
      },
      async getLineList(){
        const _this = this;
            let params = {"options":"{\"current\":1,\"limit\":50,\"isQueryCount\":true,\"where\":{}}"}
            this.$api.slaApi.query('trsCircuit',params)
              .then((result) => {
                let res = result;
                if (res) {
                  this.listData = [];
                  clearInterval(_this.timeA);
                  _this.timeA = null;
                  this.listData = res.data
                  bus.$emit("circuitName", this.listData[0].circuitNo);
                  var itemList = document.querySelector('.item-list');
                  var scrollDis = 0.75;
                  var length = this.listData.length;
                  var index = 1;
                  _this.timeA = setInterval(() => {
                    if (index < length + 1) {
                      itemList.style.top = -(scrollDis * index) + 'rem';
                      itemList.style.transitionDuration = '1s';
                      bus.$emit("circuitName", this.listData[index].circuitNo);
                        // console.log(index,length);
                      if (index === length-1) {
                        clearInterval(this.timeB);
                        this.timeB = null;
                        _this.timeB = setTimeout(() => {
                          itemList.style.top = 0;
                          itemList.style.transitionDuration = '0s';
                        }, 10);
                        index = 0;
                        bus.$emit("circuitName", this.listData[0].circuitNo);
                      }
                    }
                    ++index;
                  }, 10000);
                }
              })
        },
            ScrollEnd: function(){
              // console.log("ScrollEnd")
            },
            dangqian(){
                // console.log(11111111111);
            },
			// 鼠标移入
            mEnter(type,index) {
				this.$refs[type][0].className = 'evenRow'
            },
            // 鼠标移出
            mLeave(type,index) {
				this.$refs[type].map(item =>{
					item.className = "oddRow"
				})
            },
			circuitDetails(e){
        if (e.target.dataset.dept) {
          const item = JSON.parse(e.target.dataset.dept)
          // console.log(item )
          //去打开弹框
          this.circuitName = item.circuitName;
          let options = {
            current: 1,
            limit: 10,
            isQueryCount: true,
            where: {
              circuitNo:{$like: item.circuitNo}
            }
          }
          let params = {"options":JSON.stringify(options),}
              this.$api.slaApi.query('trsCircuit',params)
                .then((result) => {
                  let res = result;
                  // console.log(res.data);
                  if (res) {
                    this.tableBody = res.data
                  }
                })
                this.getRouteDetailNe(item.circuitId)
        }
        
			},
		},
	};
</script>

<style scoped lang="less">
table {
      width: 100%;
      text-align: center;
      color: #19D6FF;
}

th {
	height: 40px;
	line-height: 40px;
	font-size: 16px;
	// background-color: rgba(180, 181, 198, 0.1);
}
	.warp {
        color: #fff;
    height: calc(100% - 50px);
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
    ul {
      position: relative;
      list-style: none;
      padding: 0;
      margin: 0 auto;
      li,
      a {
        display: block;
        height: 0.75rem;
        line-height: 60px;
        display: flex;
        justify-content: space-between;
        font-size: 18px;
      }
    }
    .evenRow {
        padding: 0 10px;
        background: #19d5ff31;
    }
    .oddRow {
        padding: 0 10px;
        background: transparent;
    }
    .t-ellipsis {
    	overflow: hidden;
    	text-overflow: ellipsis;
    	white-space: nowrap;
      // margin: 0 20px;
    }
  }
  ::v-deep {
	  
	  .el-dialog {
			// height: 40vh;
            width: 70%;
			background: url("../../../assets/images/dialogbg.png") center center
              no-repeat;
            background-size: 100% 100%;
			margin-top: 10vh !important;
		}
		.el-dialog__header {
			justify-content: left;
			background: none;
		}
		.el-dialog__title {
			justify-content: left;
            line-height: 20px;
            font-weight: 800;
            height: 20px;
            display: flex;
            color: rgba(255, 255, 255, 1);
            font-size: 22px;
		}
		.el-dialog__title::before ,
		.el-dialog__title::after{
            content:none;
        }
		.el-dialog__headerbtn {
			    width: 30px;
            height: 30px;
            float: right;
            top: 10px;
            right: 10px;
			background: url("../../../assets/images/x.png") center center
              no-repeat;
            background-size: 100% 100%;
		}
		.el-dialog__body {
			height: 65%;
			.detail-info {
				max-height: 800px;
				height: 100%;
			}
  }
  
      
			.el-form {
				min-height: 100px;
				width: 95%;
				// margin: 30px auto 20px;
        // margin-top: 30px;
				text-align: center;
        .el-form-item {
          float: left;
        }
        .el-form-item__label {
          color: #fff;
          width: 107px;
        }
        .el-input__inner {
					color: #02dbff;
					background: none;
					border: 1px solid rgba(37, 190, 247, 0.5);
					// height: 24px !important;
					line-height: 24px;
				}
        .el-switch__label, .el-checkbox {
          color: #fff;
        }
        
        .el-button--primary {
					color: #FFF;
          background-color: #409EFF;
          border-color: #409EFF;
					&:focus {
						background-color: #409EFF;
          border-color: #409EFF;
					}
					&:hover {
						background-color: #409EFF;
          border-color: #409EFF;
					}
				}
        .el-select-dropdown__item:hover {
            color: #409EFF;
            background: #40a0ff27;
        }
        .el-button--default {
            color: #409EFF;
            background: transparent;
            border-color: #409EFF;
        }
        .el-input.is-disabled .el-input__inner {
          background-color: rgba(238, 238, 238, 0.116);
        }
			}
		}
    .tooltip:hover {
      border-bottom: #02dbff solid 1px;
    }
</style>