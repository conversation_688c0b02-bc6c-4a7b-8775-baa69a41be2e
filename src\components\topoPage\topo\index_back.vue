<template>
  <div class="topoContainer" v-loading="loading">
    <!-- <div class="tuli">
      <div class="lujing">
        <span>工作路径</span>
        <div class="workcls"></div>
      </div>
      <div class="lujing">
        <span>保护路径</span>
        <div class="protectCls"></div>
      </div>
    </div>
    <div class="imgTuli">
      <div class="imgCls">
        <span>正常</span>
        <img :src="imgMap.green" />
      </div>
      <div class="imgCls">
        <span>中断</span>
        <img :src="imgMap.red" />
      </div>
      <div class="imgCls">
        <span>未知</span>
        <img :src="imgMap.yellow" />
      </div>
    </div> -->
    <div id="container"></div>
  </div>
</template>
<script>
import G6 from "@antv/g6";
import green from "@/assets/img/topo/green.png";
import yellow from "@/assets/img/topo/yellow.png";
import red from "@/assets/img/topo/red.png";
// import resultJson from "./data.json";
import { getHiddenNodes } from './util.js';


let status = {
  0: "正常",
  1: "中断",
  2: "未知",
};
// active：对象已激活，表示对象处于管理状态；inactive：对象未激活，表示对象处于未管理状态；test：对象处于测试状态；
// maintenance：对象处于维护状态；unknown：表示对象的管理状态未知；--：表示该对象里这个属性没意义
let manageStatus = {
  active: "已激活状态",
  inactive: "未管理状态",
  test: "测试状态",
  maintenance: "维护状态",
  unknown: "未知状态",
};
// "enableAson": "", //仅ASON设备支持此参数。网元使能智能。0=网元智能关闭；1=网元智能开启（光层和电层智能关闭）；2=光层智能开启；3=电层智能开启；4=光层和电层智能都开启
let enableAsonStatus = {
  0: "网元智能关闭",
  1: "网元智能开启（光层和电层智能关闭）",
  2: "光层智能开启",
  3: "电层智能开启",
  4: "光层和电层智能都开启",
};
let dashStyle = {
  lineDash: [10, 5],
  endArrow: true,
};

let leavelMap = {
  'combo1': '骨干层',
  'combo2': '核心层',
  'combo3': '汇聚层',
  'combo4': '接入层',
  'combo5': '未知'
}


export default {
  name: "Topo",
  props: {
    sendData: {
      type: Object,
      default: () => {
        return {
          nodes: [],
          edges: []
        }
      }
    },
    custId: {
      type: String
    },
    highlightData: {
      type: Object,
      default: () => {
        return {
          nodes: [],
          edges: []
        }
      }
    }
  },
  data() {
    return {
      loading: false,
      combosStyle: {},
      canvansStyle: {},
      // sendData: {},
      jsonData: {
        // id: "01",
        // label: "业务类型",
        // img: img1,
        // children: [],
        nodes: [],
        edges: [],
        combos: [],
      },
      imgMap: {
        green,
        yellow,
        red,
      },
    };
  },
  watch: {
    sendData: {
      async handler(newData) {
        // let topoData = resultJson?.data;
        if (this.graph) {
          this.graph.destroy();
          this.jsonData = {
            nodes: [],
            edges: [],
            combos: [],
          }
        }
        await this.getDefaultNode();

        let combos = this.jsonData.combos || [];
        if (!combos.length && newData) {
          this.dealTopoData(newData);
          console.log('获取原始数据')
        }
        // console.log(this.jsonData)
        this.initTopo();
      },
    },
    highlightData: {
      handler(newData) {
        console.log(newData)
        let { nodes, edges } = newData;
        nodes.forEach(item => {
          const node = this.graph.findById(item.id);
          // node.setState('active', true);
          this.graph.setItemState(node, 'active', true)
        })
      }
    }
  },
  mounted() {
  },
  methods: {
    async getDefaultNode() {
      if (this.custId) {
        this.loading = true;
        let res = await this.$api.topo.getTopo({
          type: "1",
          typeId: this.custId,
        });
        this.loading = false;
        console.log('请求保存的数据')
        if (res && res.data) {
          let result = res.data;
          let { nodes, edges, combos } = result;
          let curNodes = nodes || [];
          let curJsonData = {
            nodes: curNodes,
            edges: edges || [],
            combos: JSON.parse(combos || '[]'),
          }
          this.getStyles(curNodes)
          this.jsonData = { ...curJsonData };
        }
      } else {
        this.jsonData = {
          nodes: [],
          edges: [],
          combos: [],
        }
      }

    },
    getStyles(nodes) {
      let comboMap = {
        combo1: 0,
        combo2: 0,
        combo3: 0,
        combo4: 0,
        combo5: 0,
      }
      for (let item of nodes) {
        comboMap[item.comboId]++
      }
      //获取最多的节点
      let lenArr = Object.values(comboMap).map(item => {
        return item
      })
      let maxNodeLen = Math.max(...lenArr) - 2;

      let canvasWidth = (maxNodeLen > 80 ? 80 : maxNodeLen) * 150;
      this.canvansStyle.width = canvasWidth >= 1300 ? 1300 : canvasWidth;
      this.canvansStyle.height = 750;
      this.combosStyle.width = this.canvansStyle.width * 0.5;
      this.combosStyle.height = this.canvansStyle.height * 0.15;
    },
    savePosition() {
    },

    dealTopoData(data) {
      let nodeList = data?.nodes || [];
      let edgeList = data?.edges || [];
      let { nodes: allNodes } = this.getNodes(nodeList);
      let edges = [];
      let combos = this.getCombos();
      let hiddenNodes = getHiddenNodes(combos, this.combosStyle);

      let nodes = [...allNodes, ...hiddenNodes];
      edges = this.getEdges(edgeList);

      let jsonData = {
        nodes,
        edges,
        combos,
      };
      // console.log(jsonData)
      this.jsonData = jsonData;
      // this.drawLink();
    },
    //数组去重
    deduplication(arr) {
      let newArr = [];
      let idArr = [];
      for (let item of arr) {
        if (!idArr.includes(item.label)) {
          newArr.push(item);
          idArr.push(item.label);
        }
      }
      return newArr;
    },
    // 线段数组
    getEdges(edgesList) {
      let links = [];
      // let source_targetArr = [];
      // let typeCls = {};
      // endArrow: true,
      // if (flag === "protection") {
      //   typeCls = {
      //     style: dashStyle,
      //   };
      // } else {
      //   typeCls = {};
      // }
      // for (let nodeList of nodeArray) {
      //   for (let i = 0; i < nodeList.length; i++) {
      //     let item = nodeList[i];
      //     let source = item.aportId;
      //     let target = item.zportId;
      //     if (!source_targetArr.includes(source + '_' + target)) {
      //       links.push({
      //         source,
      //         target,
      //         style: { endArrow: true, }
      //       });
      //       source_targetArr.push(source + '_' + target)
      //     }
      //   }
      // }

      for (let item of edgesList) {
        let { source, target } = item;
        links.push({
          ...item,
          source,
          target,
          style: { endArrow: true, }
        });
      }
      // console.log(source_targetArr)
      // console.log(links)
      G6.Util.processParallelEdges(links);
      return links;
    },
    dealEdges(linkList) {
      let links = [];
      let tmpArr = [];
      let repeatArr = [];
      //   sourceAnchor: 4,
      // targetAnchor: 2,

      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (!tmpArr.includes(idStr)) {
          tmpArr.push(idStr);
        } else {
          repeatArr.push(idStr);
        }
      }
      for (let i = 0; i < linkList.length; i++) {
        let item = linkList[i];
        let idStr = item.source + item.target;
        if (repeatArr.includes(idStr)) {
          if (item.type === "line-arrow") {
            //工作路径
            links.push({
              ...item,
              sourceAnchor: 4,
              targetAnchor: 2,
            });
          } else {
            //保护路径
            links.push({
              ...item,
              sourceAnchor: 5,
              targetAnchor: 3,
            });
          }
        } else {
          links.push(item);
        }
      }

      return links;
    },
    getCombos() {
      return [
        { id: "combo1", label: leavelMap.combo1, type: 'backbone', x: this.combosStyle.width, y: this.combosStyle.height * 0.5, },
        { id: "combo2", label: leavelMap.combo2, type: 'region', x: this.combosStyle.width, y: this.combosStyle.height * 1.8, },
        { id: "combo3", label: leavelMap.combo3, type: "local", x: this.combosStyle.width, y: this.combosStyle.height * 3.1, },
        { id: "combo4", label: leavelMap.combo4, type: "access", x: this.combosStyle.width, y: this.combosStyle.height * 4.4, },
        { id: "combo5", label: leavelMap.combo5, type: "unknown", x: this.combosStyle.width, y: this.combosStyle.height * 5.7, },
      ]
    },
    getNodes(allNodes) {
      let comboMap = {
        [leavelMap.combo1]: 'combo1',
        [leavelMap.combo2]: 'combo2',
        [leavelMap.combo3]: 'combo3',
        [leavelMap.combo4]: 'combo4',
        [leavelMap.combo5]: 'combo5'
      }
      const nodesMap = {
        [leavelMap.combo1]: 0,
        [leavelMap.combo2]: 0,
        [leavelMap.combo3]: 0,
        [leavelMap.combo4]: 0,
        [leavelMap.combo5]: 0
      }
      const nodesMap2 = {
        [leavelMap.combo1]: 0,
        [leavelMap.combo2]: 0,
        [leavelMap.combo3]: 0,
        [leavelMap.combo4]: 0,
        [leavelMap.combo5]: 0
      }
      // for (let item of datas) {
      //   for (let item2 of item) {
      //     if (!allNodeNames.includes(item2.atrsNeName)) {
      //       allNodes.push(item2);
      //       allNodeNames.push(item2.atrsNeName);
      //     }
      //   }
      // }
      let nodes = [];

      for (let item of allNodes) {
        let nodeType = item.netLevelName;
        nodesMap[nodeType]++
      }
      //获取最多的节点
      let lenArr = Object.values(nodesMap).map(item => {
        return item
      })
      let maxNodeLen = Math.max(...lenArr);
      this.canvansStyle.width = (maxNodeLen > 80 ? 80 : maxNodeLen) * 150;
      this.canvansStyle.height = 800;
      this.combosStyle.width = this.canvansStyle.width * 0.5;
      this.combosStyle.height = this.canvansStyle.height * 0.15;
      for (let item of allNodes) {
        let nodeType = item.netLevelName;
        nodesMap2[nodeType]++
        nodes.push({
          id: item.id,
          label: item?.trsNetName ? `${item?.trsNetName?.trim()}` : '',
          img: green,
          comboId: comboMap[nodeType] || 'combo5',
          data: item,
          x: nodesMap2[nodeType] * 150 - this.canvansStyle.width / 2,
        });
      }

      return {
        nodes,
      };
    },
    initTopo() {

      const tooltip = this.drawTootip();

      this.registerCombo("backbone", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("region", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("local", { width: this.combosStyle.width, height: this.combosStyle.height });
      // this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height, refY: 90 });
      this.registerCombo("access", { width: this.combosStyle.width, height: this.combosStyle.height });
      this.registerCombo("unknown", { width: this.combosStyle.width, height: this.combosStyle.height });

      this.registerEdge("line-arrow");
      const toolbar = new G6.ToolBar({
        position: { x: 10, y: -50 },
      });
      // this.registerCustomNode("myNode");

      const graph = new G6.Graph({
        container: "container",
        width: this.canvansStyle.width,
        height: this.canvansStyle.height,
        groupByTypes: false,
        // linkCenter: true,
        maxZoom: 1,
        modes: {
          default: [
            // {
            //   type: "collapse-expand",
            //   onChange: function onChange(item, collapsed) {
            //     const data = item.getModel(); 
            //     data.collapsed = collapsed;
            //     return true;
            //   },
            // },

            { type: 'zoom-canvas', sensitivity: 1, enableOptimize: false, optimizeZoom: 0.5 },
            { type: 'drag-canvas', enableOptimize: false },
            {
              type: 'drag-node',
              enableOptimize: false,
              onlyChangeComboSize: true,
              // enableDelegate: true,
              multiple: true,
            },
            // {
            //   type: 'click-select',
            //   selectCombo: false,
            //   selectedState: 'selected'
            // },
            {
              type: 'drag-combo',
              enableOptimize: true,
              onlyChangeComboSize: true,
              // enableDelegate: true,
            },
            { type: 'brush-select', enableOptimize: true },
            { type: "activate-relations", trigger: 'mouseenter' },

            // "click-select",
          ],
        },
        defaultNode: {
          size: [40, 40],
          type: "image",
          img: green,
          color: "#ffffff",
          style: {
            // lineWidth: 20,
            stroke: "#000",
            // fill: "#C6E5FF",
            // radius: 5
          },
        },
        defaultEdge: {
          type: "quadratic",
          style: {
            lineWidth: 2,
          },
          labelCfg: {
            autoRotate: true,
          },
        },
        nodeStateStyles: {
          active: {
            stroke: "#01E2B5",
            shadowColor: "#01E2B5",
            lineWidth: 10,
            strokeOpacity: 0.5,
            shadowBlur: 10,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            "text-shape": {
              fill: "#01E2B5",
            },
            opacity: 1,
          },
          inactive: {
            opacity: 0.6,
          },
        },
        comboStateStyles: {
          active: {
            fill: "rgba(255,255,255,0)",
            opacity: 1,
            stroke: "#0AEFC5",
            "text-shape": {
              fill: "#01E2B5",
            },
            lineWidth: 5,
          },
          inactive: {
            lineDash: [5, 5],
            opacity: 1,
            fill: "rgba(255,255,255,0)",
            lineWidth: 2,
          },
        },

        edgeStateStyles: {
          active: {
            lineWidth: 5,
            stroke: "#0AEFC5",
          },
          inactive: {
            lineWidth: 2,
            stroke: "#fff",
          }
        },
        // plugins: [tooltip],
        plugins: [tooltip, toolbar],
      });

      graph.node(function (node) {
        let position = "bottom";
        let rotate = 0;
        if (!node.children) {
          position = "bottom";
          rotate = -Math.PI / 8;
        }
        return {
          label: node.label,
          labelCfg: {
            position,
            // offset: 50,
            style: {
              textAlign: "center",
              rotate,
              fill: "#fff",
            },
          },
        };
      });

      // graph.on('edge:mouseenter', e => {
      //   graph.setItemState(e.item, 'active', true)
      // })

      // graph.on('edge:mouseleave', e => {
      //   graph.setItemState(e.item, 'active', false)
      // })

      // graph.on("combo:mouseenter", (e) => {
      //   let edgeItem = e.item
      //   graph.setItemState(edgeItem, 'highlight', true)
      //   edgeItem.getEdges().forEach(edge => {
      //     graph.setItemState(edge.getTarget(), 'highlight', true)
      //     graph.setItemState(edge.getSource(), 'highlight', true)
      //     graph.setItemState(edge, 'highlight', true)
      //   })
      //   graph.paint()
      //   graph.setAutoPaint(true)
      // });

      // graph.on('combo:mouseleave', (e) => {
      //   graph.setAutoPaint(false)
      //   graph.getNodes().forEach(node => {
      //     graph.clearItemStates(node)
      //   })
      //   graph.getEdges().forEach(edge => {
      //     graph.clearItemStates(edge)
      //   })
      //   graph.getCombos().forEach(combo => {
      //     graph.clearItemStates(combo)
      //   })
      //   graph.paint()
      //   graph.setAutoPaint(true)
      // })
      // graph.on('node')

      // graph.on('node:dragend', async (evt) => {

      // });

      graph.on('dragend', async (evt) => {
        // const { item } = evt;
        // const model = item.getModel();
        // const { x, y, width, height } = item.getBBox();
        // const edges = this.graph.save().edges;
        const { nodes, edges, combos } = this.graph.save();
        let param = {
          type: "1",
          typeId: this.custId,
          viewJson: {
            nodes: JSON.stringify(nodes),
            edges: JSON.stringify(edges),
            combos: JSON.stringify(combos),
          }
        }
        console.log(param)

        await this.$api.topo.topoSave(param);
      });


      if (typeof window !== "undefined")
        window.onresize = () => {
          if (!graph || graph.get("destroyed")) return;
          const container = document.getElementById('container');
          const width = container.scrollWidth;
          const height = container.scrollHeight || window.innerHeight;
          if (!container || !container.scrollWidth || !container.scrollHeight)
            return;
          graph.changeSize(width, height);
        };
      graph.data(this.jsonData);

      graph.render();
      graph.fitView();
      this.graph = graph;
    },
    updateTopo() {
      this.graph.data(this.jsonData);
      this.graph.render();
    },
    registerEdge(linkName) {
      G6.registerEdge(linkName, {
        itemType: "edge",
        draw: function draw(cfg, group) {
          var startPoint = cfg.startPoint,
            endPoint = cfg.endPoint;

          var keyShape = group.addShape("path", {
            attrs: {
              path: [
                ["M", startPoint.x, startPoint.y],
                ["L", endPoint.x, endPoint.y],
              ],
              stroke: "#0CC",
              lineWidth: 1,
              // startArrow: {
              //   path: "M 10,0 L -10,-10 L -10,10 Z",
              //   path: G6.Arrow.vee(15, 20, 15),
              //   d: 15,
              // },
              endArrow: {
                path: G6.Arrow.vee(5, 5, 5),
                d: 5,
              },
            },
          });
          return keyShape;
        },
      });
    },

    registerCombo(comboName, cusConf) {
      G6.registerCombo(
        comboName,
        {
          drawShape: function drawShape(cfg, group) {
            const self = this;
            // 获取配置中的 Combo 内边距
            cfg.padding = cfg?.padding || [0, 0, 0, 0];
            cfg.labelCfg = {
              refY: cusConf?.refY || 40,
              position: "top",
              style: {
                fontSize: 18,
                fill: "#fff",
              },
            }
            cfg.size = [cusConf?.width || cfg.width, cusConf?.height || cfg.height];
            // 获取样式配置，style.width 与 style.height 对应 rect Combo 位置说明图中的 width 与 height
            const style = self.getShapeStyle(cfg);
            // 绘制一个矩形作为 keyShape，与 'rect' Combo 的 keyShape 一致
            const rect = group.addShape("rect", {
              attrs: {
                allowZoom: true,
                allowDrag: true,
                ...style,
                x: -style.height / 2 - cfg.padding[0],
                y: -style.width / 2 - cfg.padding[3],
                lineWidth: 2,
                fill: "rgba(255,255,255,0)",
                opacity: 1,
                stroke: "#fff",
                lineDash: [5, 5],
              },
              draggable: false,
              name: "combo-keyShape" + comboName, // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
            });

            return rect;
          },
        },
        "rect"
      );
    },

    // 画画线
    drawLink() {
      let len = this.jsonData.nodes.length || 0;
      let nodeList = this.jsonData.nodes;

      for (let i = 0; i < len; i++) {
        if (nodeList[i + 1]) {
          let item1 = nodeList[i];
          let item2 = nodeList[i + 1];
          let source = item1.id;
          let target = item2.id;

          this.graph.addItem("edge", {
            source,
            target,
          });
        }
      }
    },
    // 提示框
    drawTootip() {
      let tooltip = new G6.Tooltip({
        offsetX: 20,
        offsetY: 0,
        fixToNode: [1, 1],
        // trigger: 'click',
        itemTypes: ['node'],
        getContent(e) {
          const outDiv = document.createElement("div");
          outDiv.style.width = "300px";
          // console.log(e.item.getModel());
          let curData = e.item.getModel();

          if (curData?.data) {
            let obj = curData?.data;
            outDiv.innerHTML = `
              <ul>
                <li class="node-cls">
                  <span>网元ID : </span> <span>${obj.id}</span>
                </li>
                <li class="node-cls">
                  <span>网元名称 : </span> <span>${obj.label}</span>
                </li>
                <li class="node-cls">
                  <span>所属层级 : </span><span>${obj.netLevelName}</span>
                </li>
              </ul>
            `;
          } else if (curData.label) {
            outDiv.innerHTML = `
              <ul>
                <li>业务名称：${curData.label || ""}</li>
              </ul>
            `;
          } else {
            return "";
          }

          return outDiv;
        },
      });

      return tooltip;
    },
  },
};
</script>

<style lang="less" scoped></style>

<style lang="less">
.g6-component-toolbar {
  top: -50px;
  left: 81% !important;
  background: #041736;
  border: 1px solid #03396A;

  svg {
    fill: #fff;
  }
}

.g6-component-tooltip {
  background-color: #062856;
  color: #fff;
  border: 0;
  padding: 16px;

  .node-cls {
    display: flex;

    span {
      display: flex;
      align-items: center;
      height: 30px;
    }

    :first-child {
      width: 90px;
    }
  }
}

.topoContainer {
  position: relative;

  #container {
    display: flex;
    width: 1300px;
    height: 750px;
    overflow: auto;
  }

  #container::-webkit-scrollbar-thumb {
    background-color: #158C8D;
  }

  #container::-webkit-scrollbar-track {
    background-color: #041440;
  }

  .tuli {
    position: absolute;
    bottom: 20px;
    left: 30px;
    padding: 16px;

    .lujing {
      width: 150px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .workcls {
        width: 80px;
        height: 2px;
        background-color: #2ecccc;
      }

      .protectCls {
        width: 80px;
        height: 2px;
        background: repeating-linear-gradient(to right,
            #fff 0px,
            #fff 0px,
            #ccc 4px,
            #ccc 8px);
      }
    }
  }

  .imgTuli {
    position: absolute;
    bottom: 20px;
    left: 250px;
    padding: 16px;
    display: flex;

    .imgCls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 24px;

      img {
        width: 30px;
        margin-left: 8px;
      }
    }
  }

  .el-loading-mask {
    background: rgba(5, 99, 153, 0.3);
  }
}
</style>
