// 资源信息管理列表 - 基础信息
export const basicInfo = [{
        label: '客户编号',
        value: '1021060146610091',
        key: '精品网用户数据_USECUST_ID'
    },
    {
        label: '省份',
        value: '内蒙古自治区',
        crmAttrValue: '10',
        key: '精品网用户数据_PROVINCE_CODE'
    },
    {
        label: '地市',
        value: '呼和浩特市',
        crmAttrValue: '101',
        key: '精品网用户数据_EPARCHY_CODE'
    },
    {
        label: '业务号码',
        value: '101JRW00703',
        key: '精品网用户数据_SERIAL_NUMBER'
    }
]

// 资源信息列表 - 列表数据
export const resourceInfoListData = [{
        userId: '4478473890955264',
        circuitCode: '北京呼和浩特ONE0066NP', // 电路代号
        aProvinceCode: '10',
        aProvinceName: '内蒙古自治区',
        aEparchyCode: '101',
        aEparchyName: '呼和浩特市',
        aAddr: '呼和浩特市新城区北二环路与万通路十字楼口东北中国联通呼和浩特云数据中心机房DC2（201）',
        aCustomerContact: '郭永刚',
        aTel: '18647102256',
        aCustomerName: '中国人民解放军新闻传播中心',
        aManager: '郭永刚',
        aMachineRoom: '呼和浩特市新城区北二环路与万通路十字楼口东北中国联通呼和浩特云数据中心机房DC2（201）',
        zProvinceCode: '11',
        zProvinceName: '北京市',
        zEparchyCode: '110',
        zEparchyName: '北京市',
        zCountyCode: '214',
        zCountyName: '西城区',
        zAddr: '北京市西城区阜外大街34号解放军新闻中心机房',
        zCustomerContact: '杨文博',
        zTel: '*********** ',
        zCustomerName: '中国人民解放军新闻传播中心',
        zManager: '12',
        zMachineRoom: '北京市西城区阜外大街34号解放军新闻中心机房',
        bandWidthCode: '1_0000066',
        bandWidth: '1G', // 固定带宽
        circuitUseCode: '1_0000117',
        circuitUse: '点对点', // 电路用途
        businessTypeCode: '110007226',
        businessType: '固定带宽模式', // 带宽业务模式
        circuitTypeCode: '110007436',
        circuitType: '以太网电路|以太网电路接口(JK)', // 承载电路类型
        tenancyCode: '1_0000115',
        tenancy: '省际长途', // 电路租用范围
    },
    // {
    //   userId: '56636661',
    //   cityName: '赤峰市',
    //   countyName: '赤峰市区',
    //   businessType: '智慧专线',
    //   sourceAddr: '中国联通赤峰分公司',
    //   destination: '中国联通赤峰分公司',
    //   businessStatus: '中断',
    //   basicBandWidth: '400M',
    //   currentBandWidth: '400M'
    // }, {
    //   userId: '56636662',
    //   cityName: '阿拉善市',
    //   countyName: '额济纳旗',
    //   businessType: '智慧专线',
    //   sourceAddr: '中国联通阿拉善分公司',
    //   destination: '中国联通阿拉善分公司',
    //   businessStatus: '正常',
    //   basicBandWidth: '100M',
    //   currentBandWidth: '100M'
    // },
]

export const cityList = [{
        label: '呼伦贝尔市',
        value: '0470',
        transCode: '108'
    },
    {
        label: '呼和浩特市',
        value: '0471',
        transCode: '101'
    },
    {
        label: '包头市',
        value: '0472',
        transCode: '102'
    },
    {
        label: '乌海市',
        value: '0473',
        transCode: '106'
    },
    {
        label: '乌兰察布市',
        value: '0474',
        transCode: '103'
    },
    {
        label: '通辽市',
        value: '0475',
        transCode: '109'
    },
    {
        label: '赤峰市',
        value: '0476',
        transCode: '107'
    },
    {
        label: '鄂尔多斯市',
        value: '0477',
        transCode: '104'
    },
    {
        label: '巴彦淖尔市',
        value: '0478',
        transCode: '105'
    },
    {
        label: '锡林郭勒盟',
        value: '0479',
        transCode: '111'
    },
    {
        label: '兴安盟',
        value: '0482',
        transCode: '113'
    },
    {
        label: '阿拉善盟',
        value: '0483',
        transCode: '114'
    },
    {
        label: '内蒙古',
        value: 'NMCU',
        transCode: '10'
    },
]

// 地市 / 区县关联表
export const cityInfo = [{
        "cityCode": "0470",
        "cityName": "呼伦贝尔市",
        "children": [{
                "countyName": "海拉尔区",
                "countyCode": "47001",
                "countySubCode": "E00B"
            },
            {
                "countyName": "牙克石市",
                "countyCode": "47002",
                "countySubCode": "E00D"
            },
            {
                "countyName": "满洲里市",
                "countyCode": "47003",
                "countySubCode": "E00C"
            },
            {
                "countyName": "扎兰屯市",
                "countyCode": "47004",
                "countySubCode": "E00E"
            },
            {
                "countyName": "鄂温克族自治旗",
                "countyCode": "47005",
                "countySubCode": "E00O"
            },
            {
                "countyName": "扎区",
                "countyCode": "47009",
                "countySubCode": "E00R"
            },
            {
                "countyName": "阿荣旗",
                "countyCode": "47010",
                "countySubCode": "E00H"
            },
            {
                "countyName": "莫旗",
                "countyCode": "47011",
                "countySubCode": "E00N"
            },
            {
                "countyName": "新巴尔虎左旗",
                "countyCode": "47012",
                "countySubCode": "E00J"
            },
            {
                "countyName": "新巴尔虎右旗",
                "countyCode": "47013",
                "countySubCode": "E00L"
            },
            {
                "countyName": "鄂伦春自治旗",
                "countyCode": "47014",
                "countySubCode": "E00M"
            },
            {
                "countyName": "根河市",
                "countyCode": "47015",
                "countySubCode": "E00G"
            },
            {
                "countyName": "额尔古纳市",
                "countyCode": "47016",
                "countySubCode": "E00F"
            },
            {
                "countyName": "大雁",
                "countyCode": "47017",
                "countySubCode": "E00Q"
            },
            {
                "countyName": "伊敏河",
                "countyCode": "47018",
                "countySubCode": "E00S"
            },
            {
                "countyName": "大杨树",
                "countyCode": "47019",
                "countySubCode": "E00P"
            },
            {
                "countyName": "陈巴尔虎旗",
                "countyCode": "47020",
                "countySubCode": "E00I"
            }
        ]
    },
    {
        "cityCode": "0471",
        "cityName": "呼和浩特市",
        "children": [{
                "countyName": "城区",
                "countyCode": "47101",
                "countySubCode": "A001"
            },
            {
                "countyName": "武川县",
                "countyCode": "47102",
                "countySubCode": "A006"
            },
            {
                "countyName": "清水河县",
                "countyCode": "47103",
                "countySubCode": "A005"
            },
            {
                "countyName": "土默特左旗",
                "countyCode": "47104",
                "countySubCode": "A002"
            },
            {
                "countyName": "托克托县",
                "countyCode": "47105",
                "countySubCode": "A003"
            },
            {
                "countyName": "和林格尔县",
                "countyCode": "47106",
                "countySubCode": "A004"
            }
        ]
    },
    {
        "cityCode": "0472",
        "cityName": "包头市",
        "children": [{
                "countyName": "城区",
                "countyCode": "47201",
                "countySubCode": "B001"
            },
            {
                "countyName": "固阳县",
                "countyCode": "47202",
                "countySubCode": "B006"
            },
            {
                "countyName": "达茂旗",
                "countyCode": "47203",
                "countySubCode": "B005"
            },
            {
                "countyName": "白云鄂博矿区",
                "countyCode": "47204",
                "countySubCode": "B004"
            },
            {
                "countyName": "石拐矿区",
                "countyCode": "47205",
                "countySubCode": "B003"
            },
            {
                "countyName": "土默特右旗",
                "countyCode": "47206",
                "countySubCode": "B002"
            }
        ]
    },
    {
        "cityCode": "0473",
        "cityName": "乌海市",
        "children": [{
                "countyName": "海勃湾区",
                "countyCode": "47301",
                "countySubCode": "C00H"
            },
            {
                "countyName": "乌达区",
                "countyCode": "47302",
                "countySubCode": "C00W"
            },
            {
                "countyName": "海南区",
                "countyCode": "47303",
                "countySubCode": "C00N"
            }
        ]
    },
    {
        "cityCode": "0474",
        "cityName": "乌兰察布市",
        "children": [{
                "countyName": "集宁",
                "countyCode": "47401",
                "countySubCode": "JJNW"
            },
            {
                "countyName": "察右前旗",
                "countyCode": "47402",
                "countySubCode": "JQQD"
            },
            {
                "countyName": "察右中旗",
                "countyCode": "47403",
                "countySubCode": "JZQE"
            },
            {
                "countyName": "察哈尔右翼后旗",
                "countyCode": "47404",
                "countySubCode": "JHQF"
            },
            {
                "countyName": "商都",
                "countyCode": "47405",
                "countySubCode": "JSDH"
            },
            {
                "countyName": "化德",
                "countyCode": "47406",
                "countySubCode": "JHDG"
            },
            {
                "countyName": "兴和",
                "countyCode": "47407",
                "countySubCode": "JXHL"
            },
            {
                "countyName": "凉城",
                "countyCode": "47408",
                "countySubCode": "JLCJ"
            },
            {
                "countyName": "丰镇",
                "countyCode": "47409",
                "countySubCode": "JFZI"
            },
            {
                "countyName": "卓资",
                "countyCode": "47410",
                "countySubCode": "JZZK"
            },
            {
                "countyName": "四子王旗",
                "countyCode": "47411",
                "countySubCode": "JSZC"
            },
            {
                "countyName": "土牧尔台",
                "countyCode": "47412",
                "countySubCode": "JTMT"
            },
            {
                "countyName": "旗下营",
                "countyCode": "47413",
                "countySubCode": "JQXY"
            }
        ]
    },
    {
        "cityCode": "0475",
        "cityName": "通辽市",
        "children": [{
                "countyName": "科尔沁区",
                "countyCode": "47501",
                "countySubCode": "G001"
            },
            {
                "countyName": "科尔沁左翼中旗",
                "countyCode": "47502",
                "countySubCode": "G003"
            },
            {
                "countyName": "奈曼旗",
                "countyCode": "47503",
                "countySubCode": "G005"
            },
            {
                "countyName": "库伦旗",
                "countyCode": "47504",
                "countySubCode": "G004"
            },
            {
                "countyName": "开鲁县",
                "countyCode": "47505",
                "countySubCode": "G008"
            },
            {
                "countyName": "扎鲁特旗",
                "countyCode": "47506",
                "countySubCode": "G006"
            },
            {
                "countyName": "科尔沁左翼后旗",
                "countyCode": "47507",
                "countySubCode": "G002"
            },
            {
                "countyName": "霍林郭勒市",
                "countyCode": "47508",
                "countySubCode": "G007"
            },
            {
                "countyName": "开发区",
                "countyCode": "47509",
                "countySubCode": "G00I"
            }
        ]
    },
    {
        "cityCode": "0476",
        "cityName": "赤峰市",
        "children": [{
                "countyName": "赤峰市区",
                "countyCode": "47601",
                "countySubCode": "D01A"
            },
            {
                "countyName": "元宝山区",
                "countyCode": "47602",
                "countySubCode": "D00C"
            },
            {
                "countyName": "松山区",
                "countyCode": "47603",
                "countySubCode": "D00M"
            },
            {
                "countyName": "平庄镇",
                "countyCode": "47604",
                "countySubCode": "D00K"
            },
            {
                "countyName": "阿鲁科尔沁旗",
                "countyCode": "47605",
                "countySubCode": "D00L"
            },
            {
                "countyName": "林西县",
                "countyCode": "47606",
                "countySubCode": "D00J"
            },
            {
                "countyName": "翁牛特旗",
                "countyCode": "47607",
                "countySubCode": "D00G"
            },
            {
                "countyName": "喀喇沁旗",
                "countyCode": "47608",
                "countySubCode": "D00F"
            },
            {
                "countyName": "巴林右旗",
                "countyCode": "47609",
                "countySubCode": "D00I"
            },
            {
                "countyName": "巴林左旗",
                "countyCode": "47610",
                "countySubCode": "D00H"
            },
            {
                "countyName": "克什克腾旗",
                "countyCode": "47611",
                "countySubCode": "D00B"
            },
            {
                "countyName": "宁城县",
                "countyCode": "47612",
                "countySubCode": "D00D"
            },
            {
                "countyName": "敖汉旗",
                "countyCode": "47613",
                "countySubCode": "D00E"
            }
        ]
    },
    {
        "cityCode": "0477",
        "cityName": "鄂尔多斯市",
        "children": [{
                "countyName": "东胜市",
                "countyCode": "47701",
                "countySubCode": "K00R"
            },
            {
                "countyName": "达拉特旗",
                "countyCode": "47702",
                "countySubCode": "K003"
            },
            {
                "countyName": "鄂托克旗",
                "countyCode": "47703",
                "countySubCode": "K008"
            },
            {
                "countyName": "鄂托克前旗",
                "countyCode": "47704",
                "countySubCode": "K007"
            },
            {
                "countyName": "杭锦旗",
                "countyCode": "47705",
                "countySubCode": "K009"
            },
            {
                "countyName": "乌审旗",
                "countyCode": "47706",
                "countySubCode": "K006"
            },
            {
                "countyName": "薛家湾",
                "countyCode": "47707",
                "countySubCode": "K010"
            },
            {
                "countyName": "伊金霍洛旗",
                "countyCode": "47708",
                "countySubCode": "K005"
            },
            {
                "countyName": "棋盘井",
                "countyCode": "47709",
                "countySubCode": "K012"
            },
            {
                "countyName": "杭锦旗巴拉贡",
                "countyCode": "47710",
                "countySubCode": "K013"
            },
            {
                "countyName": "准格尔旗",
                "countyCode": "47711",
                "countySubCode": "K004"
            },
            {
                "countyName": "上湾",
                "countyCode": "47712",
                "countySubCode": "K014"
            },
            {
                "countyName": "发展区域",
                "countyCode": "47713",
                "countySubCode": "K011"
            }
        ]
    },
    {
        "cityCode": "0478",
        "cityName": "巴盟市",
        "children": [{
                "countyName": "临河区",
                "countyCode": "47801",
                "countySubCode": "L002"
            },
            {
                "countyName": "磴口县",
                "countyCode": "47802",
                "countySubCode": "L006"
            },
            {
                "countyName": "五原县",
                "countyCode": "47803",
                "countySubCode": "L003"
            },
            {
                "countyName": "乌拉特前旗",
                "countyCode": "47804",
                "countySubCode": "L004"
            },
            {
                "countyName": "乌拉特中旗",
                "countyCode": "47805",
                "countySubCode": "L007"
            },
            {
                "countyName": "乌拉特后旗",
                "countyCode": "47806",
                "countySubCode": "L008"
            },
            {
                "countyName": "杭锦后旗",
                "countyCode": "47807",
                "countySubCode": "L005"
            }
        ]
    },
    {
        "cityCode": "0479",
        "cityName": "锡林郭勒盟",
        "children": [{
                "countyName": "锡林浩特",
                "countyCode": "47901",
                "countySubCode": "H00B"
            },
            {
                "countyName": "二连浩特市",
                "countyCode": "47902",
                "countySubCode": "H00C"
            },
            {
                "countyName": "阿巴嘎旗",
                "countyCode": "47903",
                "countySubCode": "H00F"
            },
            {
                "countyName": "苏尼特左旗",
                "countyCode": "47904",
                "countySubCode": "H00E"
            },
            {
                "countyName": "苏尼特右旗",
                "countyCode": "47905",
                "countySubCode": "H00D"
            },
            {
                "countyName": "东乌珠穆沁旗",
                "countyCode": "47906",
                "countySubCode": "H00G"
            },
            {
                "countyName": "西乌珠穆沁旗",
                "countyCode": "47907",
                "countySubCode": "H00H"
            },
            {
                "countyName": "太仆寺旗",
                "countyCode": "47908",
                "countySubCode": "H00L"
            },
            {
                "countyName": "镶黄旗",
                "countyCode": "47909",
                "countySubCode": "H00K"
            },
            {
                "countyName": "正镶白旗",
                "countyCode": "47910",
                "countySubCode": "H00J"
            },
            {
                "countyName": "正蓝旗",
                "countyCode": "47911",
                "countySubCode": "H00I"
            },
            {
                "countyName": "多伦县",
                "countyCode": "47912",
                "countySubCode": "H00M"
            },
            {
                "countyName": "乌拉盖管理区",
                "countyCode": "47913",
                "countySubCode": "H00O"
            }
        ]
    },
    {
        "cityCode": "0482",
        "cityName": "兴安盟市",
        "children": [{
                "countyName": "乌兰浩特市",
                "countyCode": "48201",
                "countySubCode": "F002"
            },
            {
                "countyName": "科尔沁右翼中旗",
                "countyCode": "48202",
                "countySubCode": "F006"
            },
            {
                "countyName": "突泉县",
                "countyCode": "48203",
                "countySubCode": "F004"
            },
            {
                "countyName": "阿尔山",
                "countyCode": "48204",
                "countySubCode": "F007"
            },
            {
                "countyName": "扎赉特旗",
                "countyCode": "48205",
                "countySubCode": "F005"
            },
            {
                "countyName": "科尔沁右翼前旗",
                "countyCode": "48206",
                "countySubCode": "F003"
            }
        ]
    },
    {
        "cityCode": "0483",
        "cityName": "阿拉善市",
        "children": [{
                "countyName": "阿拉善左旗",
                "countyCode": "48301",
                "countySubCode": "M00V"
            },
            {
                "countyName": "阿拉善右旗",
                "countyCode": "48302",
                "countySubCode": "M00C"
            },
            {
                "countyName": "额济纳旗",
                "countyCode": "48303",
                "countySubCode": "M00D"
            }
        ]
    }
]

// 业务类型
export const businessTypeList = [{
        label: '测试类型一',
        value: '1000'
    },
    {
        label: '测试类型二',
        value: '1001'
    },
    {
        label: '测试类型三',
        value: '1002'
    },
]

// 固定带宽
export const bandWidthList = [{
        value: "1_0000015",
        label: "2M"
    },
    {
        value: "1_0000017",
        label: "4M"
    },
    {
        value: "1_0000019",
        label: "6M"
    },
    {
        value: "1_0000020",
        label: "8M"
    },
    {
        value: "1_0000021",
        label: "10M"
    },
    {
        value: "1_0000023",
        label: "12M"
    },
    {
        value: "1_0000024",
        label: "14M"
    },
    {
        value: "1_0000025",
        label: "16M"
    },
    {
        value: "1_0000026",
        label: "18M"
    },
    {
        value: "1_0000027",
        label: "20M"
    },
    {
        value: "1_0000028",
        label: "22M"
    },
    {
        value: "1_0000029",
        label: "24M"
    },
    {
        value: "1_0000206",
        label: "25M"
    },
    {
        value: "1_0000030",
        label: "26M"
    },
    {
        value: "1_0000031",
        label: "28M"
    },
    {
        value: "1_0000032",
        label: "30M"
    },
    {
        value: "1_0000067",
        label: "32M"
    },
    {
        value: "1_0000033",
        label: "34M"
    },
    {
        value: "1_0000068",
        label: "36M"
    },
    {
        value: "1_0000069",
        label: "38M"
    },
    {
        value: "1_0000034",
        label: "40M"
    },
    {
        value: "1_0000035",
        label: "42M"
    },
    {
        value: "1_0000070",
        label: "44M"
    },
    {
        value: "1_0000036",
        label: "45M"
    },
    {
        value: "1_0000071",
        label: "46M"
    },
    {
        value: "1_0000072",
        label: "48M"
    },
    {
        value: "1_0000037",
        label: "50M"
    },
    {
        value: "1_0000073",
        label: "52M"
    },
    {
        value: "1_0000074",
        label: "54M"
    },
    {
        value: "1_0000075",
        label: "56M"
    },
    {
        value: "1_0000076",
        label: "58M"
    },
    {
        value: "1_0000207",
        label: "60M"
    },
    {
        value: "1_0000077",
        label: "62M"
    },
    {
        value: "1_0000038",
        label: "64M"
    },
    {
        value: "1_0000078",
        label: "66M"
    },
    {
        value: "1_0000079",
        label: "68M"
    },
    {
        value: "1_0000051",
        label: "70M"
    },
    {
        value: "1_0000080",
        label: "72M"
    },
    {
        value: "1_0000081",
        label: "74M"
    },
    {
        value: "100003845",
        label: "75M"
    },
    {
        value: "1_0000082",
        label: "76M"
    },
    {
        value: "1_0000083",
        label: "78M"
    },
    {
        value: "1_0000208",
        label: "80M"
    },
    {
        value: "1_0000084",
        label: "82M"
    },
    {
        value: "1_0000085",
        label: "84M"
    },
    {
        value: "1_0000086",
        label: "86M"
    },
    {
        value: "1_0000087",
        label: "88M"
    },
    {
        value: "1_0000052",
        label: "90M"
    },
    {
        value: "1_0000088",
        label: "92M"
    },
    {
        value: "1_0000089",
        label: "94M"
    },
    {
        value: "100003850",
        label: "95M"
    },
    {
        value: "1_0000090",
        label: "96M"
    },
    {
        value: "1_0000091",
        label: "98M"
    },
    {
        value: "1_0000040",
        label: "100M"
    },
    {
        value: "1_0000092",
        label: "102M"
    },
    {
        value: "1_0000093",
        label: "104M"
    },
    {
        value: "110017613",
        label: "105M"
    },
    {
        value: "1_0000094",
        label: "106M"
    },
    {
        value: "1_0000095",
        label: "108M"
    },
    {
        value: "1_0000041",
        label: "110M"
    },
    {
        value: "1_0000096",
        label: "112M"
    },
    {
        value: "1_0000097",
        label: "114M"
    },
    {
        value: "100002151",
        label: "115M"
    },
    {
        value: "1_0000098",
        label: "116M"
    },
    {
        value: "1_0000099",
        label: "118M"
    },
    {
        value: "1_0000100",
        label: "120M"
    },
    {
        value: "110016871",
        label: "125M"
    },
    {
        value: "100003779",
        label: "126M"
    },
    {
        value: "1_0000042",
        label: "128M"
    },
    {
        value: "1_0000371",
        label: "130M"
    },
    {
        value: "100003780",
        label: "132M"
    },
    {
        value: "100002152",
        label: "140M"
    },
    {
        value: "100002153",
        label: "142M"
    },
    {
        value: "1_0000054",
        label: "150M"
    },
    {
        value: "100003781",
        label: "154M"
    },
    {
        value: "1_0000043",
        label: "155M"
    },
    {
        value: "1_0000044",
        label: "156M"
    },
    {
        value: "100003782",
        label: "160M"
    },
    {
        value: "100003783",
        label: "162M"
    },
    {
        value: "100002154",
        label: "170M"
    },
    {
        value: "100003784",
        label: "176M"
    },
    {
        value: "100003786",
        label: "180M"
    },
    {
        value: "100003787",
        label: "190M"
    },
    {
        value: "100003788",
        label: "192M"
    },
    {
        value: "1_0000053",
        label: "200M"
    },
    {
        value: "100003790",
        label: "208M"
    },
    {
        value: "100003791",
        label: "210M"
    },
    {
        value: "100003792",
        label: "220M"
    },
    {
        value: "100003793",
        label: "228M"
    },
    {
        value: "100003794",
        label: "230M"
    },
    {
        value: "100003795",
        label: "232M"
    },
    {
        value: "100002155",
        label: "240M"
    },
    {
        value: "1_0000055",
        label: "250M"
    },
    {
        value: "1_0000045",
        label: "256M"
    },
    {
        value: "100002156",
        label: "260M"
    },
    {
        value: "100003796",
        label: "266M"
    },
    {
        value: "100003035",
        label: "270M"
    },
    {
        value: "100003797",
        label: "276M"
    },
    {
        value: "100003798",
        label: "280M"
    },
    {
        value: "100002157",
        label: "290M"
    },
    {
        value: "100003799",
        label: "296M"
    },
    {
        value: "1_0000056",
        label: "300M"
    },
    {
        value: "100003802",
        label: "310M"
    },
    {
        value: "100003803",
        label: "320M"
    },
    {
        value: "100003804",
        label: "330M"
    },
    {
        value: "100003805",
        label: "340M"
    },
    {
        value: "1_0000057",
        label: "350M"
    },
    {
        value: "100003807",
        label: "360M"
    },
    {
        value: "100003808",
        label: "370M"
    },
    {
        value: "100003809",
        label: "380M"
    },
    {
        value: "1_0000046",
        label: "384M"
    },
    {
        value: "100003810",
        label: "390M"
    },
    {
        value: "100002158",
        label: "391M"
    },
    {
        value: "100002159",
        label: "392M"
    },
    {
        value: "1_0000058",
        label: "400M"
    },
    {
        value: "100003811",
        label: "410M"
    },
    {
        value: "100003813",
        label: "420M"
    },
    {
        value: "100003814",
        label: "430M"
    },
    {
        value: "100003815",
        label: "440M"
    },
    {
        value: "100003816",
        label: "449M"
    },
    {
        value: "1_0000059",
        label: "450M"
    },
    {
        value: "100003817",
        label: "460M"
    },
    {
        value: "100003818",
        label: "470M"
    },
    {
        value: "100003819",
        label: "480M"
    },
    {
        value: "100003820",
        label: "490M"
    },
    {
        value: "1_0000060",
        label: "500M"
    },
    {
        value: "100003821",
        label: "510M"
    },
    {
        value: "1_0000047",
        label: "512M"
    },
    {
        value: "100003822",
        label: "520M"
    },
    {
        value: "100003823",
        label: "530M"
    },
    {
        value: "100003824",
        label: "540M"
    },
    {
        value: "1_0000101",
        label: "550M"
    },
    {
        value: "100003826",
        label: "560M"
    },
    {
        value: "100003827",
        label: "570M"
    },
    {
        value: "100003828",
        label: "580M"
    },
    {
        value: "100003829",
        label: "590M"
    },
    {
        value: "1_0000061",
        label: "600M"
    },
    {
        value: "100003831",
        label: "610M"
    },
    {
        value: "100003832",
        label: "620M"
    },
    {
        value: "1_0000048",
        label: "622M"
    },
    {
        value: "100003833",
        label: "630M"
    },
    {
        value: "100003834",
        label: "640M"
    },
    {
        value: "100003835",
        label: "650M"
    },
    {
        value: "100003836",
        label: "660M"
    },
    {
        value: "100003837",
        label: "670M"
    },
    {
        value: "100003838",
        label: "680M"
    },
    {
        value: "100003839",
        label: "690M"
    },
    {
        value: "1_0000062",
        label: "700M"
    },
    {
        value: "100003840",
        label: "710M"
    },
    {
        value: "100003841",
        label: "720M"
    },
    {
        value: "100003842",
        label: "730M"
    },
    {
        value: "100003843",
        label: "740M"
    },
    {
        value: "100003844",
        label: "750M"
    },
    {
        value: "100003846",
        label: "760M"
    },
    {
        value: "100003847",
        label: "770M"
    },
    {
        value: "1_0000063",
        label: "800M"
    },
    {
        value: "100003848",
        label: "870M"
    },
    {
        value: "1_0000064",
        label: "900M"
    },
    {
        value: "1_0000066",
        label: "1G"
    },
    {
        value: "100002160",
        label: "1.2G"
    },
    {
        value: "100002161",
        label: "1.25G"
    },
    {
        value: "100005459",
        label: "1.3G"
    },
    {
        value: "100003036",
        label: "1.5G"
    },
    {
        value: "100000236",
        label: "2G"
    },
    {
        value: "1_0000050",
        label: "2.5G"
    },
    {
        value: "110008314",
        label: "2.9G"
    },
    {
        value: "100000235",
        label: "3G"
    },
    {
        value: "100000170",
        label: "4G"
    },
    {
        value: "1_0000102",
        label: "5G"
    },
    {
        value: "100000237",
        label: "6G"
    },
    {
        value: "100000239",
        label: "7G"
    },
    {
        value: "1_0000103",
        label: "7.5G"
    },
    {
        value: "100000171",
        label: "8G"
    },
    {
        value: "100000238",
        label: "9G"
    },
    {
        value: "1_0000065",
        label: "10G"
    },
    {
        value: "100003591",
        label: "12G"
    },
    {
        value: "100002162",
        label: "20G"
    },
    {
        value: "100002163",
        label: "30G"
    },
    {
        value: "110015817",
        label: "35G"
    },
    {
        value: "1_0000104",
        label: "40G"
    },
    {
        value: "100001391",
        label: "50G"
    },
    {
        value: "100002998",
        label: "60G"
    },
    {
        value: "100002999",
        label: "70G"
    },
    {
        value: "100001392",
        label: "80G"
    },
    {
        value: "100003000",
        label: "90G"
    },
    {
        value: "1_0000105",
        label: "100G"
    },
    {
        value: "100003789",
        label: "200G"
    },
    {
        value: "100003599",
        label: "300G"
    }
]

// 电路用途
export const circuitUseList = [{
        value: "110007341",
        label: "多点到多点"
    },
    {
        value: "1_0000117",
        label: "点对点"
    },
    {
        value: "1_0000118",
        label: "点对多点"
    }
]

// 带宽业务模式
export const bandWidthBusTypeList = [{
    value: "110007226",
    label: "固定带宽模式"
}]

// 承载电路类型
export const circuitTypeList = [{
    value: "110007436",
    label: "以太网电路|以太网电路接口(JK)"
}]

// 电路租用范围
export const tenancyList = [{
    value: "1_0000115",
    label: "省际长途"
}]

export const detailInfo = [{
        id: '4478473890955264',
        // id: '56636661', 
        basic: [{
                label: '电路代号',
                value: '北京呼和浩特ONE0066NP'
            },
            {
                label: '客户ID',
                value: '1021060146610091'
            },
            {
                label: '省份',
                value: '内蒙古自治区',
                crmAttrValue: '10'
            },
            {
                label: '地市',
                value: '呼和浩特市',
                crmAttrValue: '101'
            },
            {
                label: '业务号码',
                value: '101JRW00703'
            },
            {
                label: '装机地址',
                value: '呼和浩特市新城区北二环路与万通路十字楼口东北中国联通呼和浩特云数据中心机房DC2（201）',
                span: 24
            },
        ],
        business: {
            basicService: [{
                    label: '固定带宽',
                    value: '1G',
                    crmAttrValue: '1_0000066'
                },
                {
                    label: '电路用途',
                    value: '点对点',
                    crmAttrValue: '1_0000117'
                },
                {
                    label: '带宽业务模式',
                    value: '固定带宽模式',
                    crmAttrValue: '110007226'
                },
                {
                    label: '承载电路类型',
                    value: '以太网电路|以太网电路接口(JK)',
                    crmAttrValue: '110007436'
                },
                {
                    label: '电路租用范围',
                    value: '省际长途',
                    crmAttrValue: '1_0000115'
                }
            ],
            aPort: [{
                    label: '省份',
                    value: '内蒙古自治区',
                    crmAttrValue: '10'
                },
                {
                    label: '地市',
                    value: '呼和浩特市',
                    crmAttrValue: '101'
                },
                {
                    label: 'A端客户联系人',
                    value: '郭永刚'
                },
                {
                    label: 'A端客户联系电话',
                    value: '18647102256'
                },
                {
                    label: 'A端客户名称',
                    value: '中国人民解放军新闻传播中心'
                },
                {
                    label: 'A端客户经理',
                    value: '郭永刚'
                },
                {
                    label: 'A端机房地址',
                    value: '呼和浩特市新城区北二环路与万通路十字楼口东北中国联通呼和浩特云数据中心机房DC2（201）',
                    span: 24
                }
            ],
            zPort: [{
                    label: '省份',
                    value: '北京市',
                    crmAttrValue: '11'
                },
                {
                    label: '地市',
                    value: '北京市',
                    crmAttrValue: '110'
                },
                {
                    label: '区县',
                    value: '西城区',
                    crmAttrValue: '214'
                },
                {
                    label: 'Z端装机地址',
                    value: '北京市西城区阜外大街34号解放军新闻中心机房',
                    span: 24
                },
                {
                    label: 'Z端客户联系人',
                    value: '杨文博'
                },
                {
                    label: 'Z端联系电话',
                    value: '***********'
                },
                {
                    label: 'Z端客户名称',
                    value: '中国人民解放军新闻传播中心'
                },
                {
                    label: 'Z端客户经理',
                    value: '12'
                },
                {
                    label: 'Z端机房地址',
                    value: '北京市西城区阜外大街34号解放军新闻中心机房',
                    span: 24
                }
            ],
        }
    },
    {
        id: '56636661',
        basic: [{
                label: '专线号',
                value: '56636661'
            },
            {
                label: '地市',
                value: '赤峰市'
            },
            {
                label: '区县',
                value: '赤峰市区'
            },
            {
                label: '业务类别',
                value: '智慧专线'
            },
            {
                label: '源端地址',
                value: '中国联通赤峰分公司'
            },
            {
                label: '业务状态',
                value: '中断 '
            },
            {
                label: '基础带宽',
                value: '400M'
            },
            {
                label: '当前带宽',
                value: '400M'
            },
            {
                label: '宿端地址',
                value: '中国联通赤峰分公司 '
            },
        ],
        business: {
            basicService: [{
                    label: '电路租用范围',
                    value: '本地区内'
                },
                {
                    label: '带宽',
                    value: '50M'
                },
                {
                    label: '电路用途',
                    value: '点对点'
                },
                {
                    label: '业务模式',
                    value: '国内业务'
                },
                {
                    label: '是否省外发起',
                    value: '否'
                }
            ],
            aPort: [{
                    label: '装机地址',
                    value: '内蒙古自治区/赤峰市/赤峰市区 新华大街110'
                },
                {
                    label: 'A端客户联系人',
                    value: '张海梅'
                },
                {
                    label: 'A端客户联系电话',
                    value: '13122233456'
                },
                {
                    label: '接口类型',
                    value: '类型名称'
                },
                {
                    label: 'A端是否入云',
                    value: '否'
                },
                {
                    label: '客户设备情况（A端）',
                    value: '光纤110口'
                },
                {
                    label: 'A端客户名称',
                    value: '王海波'
                }
            ],
            zPort: [{
                    label: '装机地址Z端',
                    value: '光纤299A口'
                },
                {
                    label: 'Z端客户联系人',
                    value: '张文霞'
                },
                {
                    label: 'Z端客户联系电话',
                    value: '***********'
                },
                {
                    label: '接口类型（Z端）',
                    value: '光缆334A口'
                },
                {
                    label: 'Z端是否入云',
                    value: '否'
                },
                {
                    label: 'Z端客户名称',
                    value: '李一文'
                }
            ],
        }
    },
    {
        id: '56636662',
        basic: [{
                label: '专线号',
                value: '56636662'
            },
            {
                label: '地市',
                value: '阿拉善市'
            },
            {
                label: '区县',
                value: '额济纳旗'
            },
            {
                label: '业务类别',
                value: '智慧专线'
            },
            {
                label: '源端地址',
                value: '中国联通阿拉善分公司'
            },
            {
                label: '业务状态',
                value: '正常'
            },
            {
                label: '基础带宽',
                value: '400M'
            },
            {
                label: '当前带宽',
                value: '400M'
            },
            {
                label: '宿端地址',
                value: '中国联通阿拉善分公司 '
            },
        ],
        business: {
            basicService: [{
                    label: '电路租用范围',
                    value: '本地区内'
                },
                {
                    label: '带宽',
                    value: '50M'
                },
                {
                    label: '电路用途',
                    value: '点对点'
                },
                {
                    label: '业务模式',
                    value: '国内业务'
                },
                {
                    label: '是否省外发起',
                    value: '否'
                }
            ],
            aPort: [{
                    label: '装机地址',
                    value: '内蒙古自治区/阿拉善市/额济纳旗 新华大街110'
                },
                {
                    label: 'A端客户联系人',
                    value: '张海梅'
                },
                {
                    label: 'A端客户联系电话',
                    value: '13122233456'
                },
                {
                    label: '接口类型',
                    value: '类型名称'
                },
                {
                    label: 'A端是否入云',
                    value: '否'
                },
                {
                    label: '客户设备情况（A端）',
                    value: '光纤110口'
                },
                {
                    label: 'A端客户名称',
                    value: '王海波'
                }
            ],
            zPort: [{
                    label: '装机地址Z端',
                    value: '光纤299A口'
                },
                {
                    label: 'Z端客户联系人',
                    value: '张文霞'
                },
                {
                    label: 'Z端客户联系电话',
                    value: '***********'
                },
                {
                    label: '接口类型（Z端）',
                    value: '光缆334A口'
                },
                {
                    label: 'Z端是否入云',
                    value: '否'
                },
                {
                    label: 'Z端客户名称',
                    value: '李一文'
                }
            ],
        }
    },
    {
        id: '56636663',
        basic: [{
                label: '专线号',
                value: '56636663'
            },
            {
                label: '地市',
                value: '乌海市'
            },
            {
                label: '区县',
                value: '海勃湾区'
            },
            {
                label: '业务类别',
                value: '智慧专线'
            },
            {
                label: '源端地址',
                value: '中国联通乌海分公司'
            },
            {
                label: '业务状态',
                value: '正常 '
            },
            {
                label: '基础带宽',
                value: '400M'
            },
            {
                label: '当前带宽',
                value: '400M'
            },
            {
                label: '宿端地址',
                value: '中国联通乌海分公司 '
            },
        ],
        business: {
            basicService: [{
                    label: '电路租用范围',
                    value: '本地区内'
                },
                {
                    label: '带宽',
                    value: '50M'
                },
                {
                    label: '电路用途',
                    value: '点对点'
                },
                {
                    label: '业务模式',
                    value: '国内业务'
                },
                {
                    label: '是否省外发起',
                    value: '否'
                }
            ],
            aPort: [{
                    label: '装机地址',
                    value: '河北省/石家庄市/长安区 新华大街110'
                },
                {
                    label: 'A端客户联系人',
                    value: '张海梅'
                },
                {
                    label: 'A端客户联系电话',
                    value: '13122233456'
                },
                {
                    label: '接口类型',
                    value: '类型名称'
                },
                {
                    label: 'A端是否入云',
                    value: '否'
                },
                {
                    label: '客户设备情况（A端）',
                    value: '光纤110口'
                },
                {
                    label: 'A端客户名称',
                    value: '王海波'
                }
            ],
            zPort: [{
                    label: '装机地址Z端',
                    value: '光纤299A口'
                },
                {
                    label: 'Z端客户联系人',
                    value: '张文霞'
                },
                {
                    label: 'Z端客户联系电话',
                    value: '***********'
                },
                {
                    label: '接口类型（Z端）',
                    value: '光缆334A口'
                },
                {
                    label: 'Z端是否入云',
                    value: '否'
                },
                {
                    label: 'Z端客户名称',
                    value: '李一文'
                }
            ],
        }
    },
    {
        id: '56636664',
        basic: [{
                label: '专线号',
                value: '56636664'
            },
            {
                label: '地市',
                value: '赤峰市'
            },
            {
                label: '区县',
                value: '松山区'
            },
            {
                label: '业务类别',
                value: '智慧专线'
            },
            {
                label: '源端地址',
                value: '中国联通赤峰分公司'
            },
            {
                label: '业务状态',
                value: '正常 '
            },
            {
                label: '基础带宽',
                value: '400M'
            },
            {
                label: '当前带宽',
                value: '400M'
            },
            {
                label: '宿端地址',
                value: '中国联通赤峰分公司 '
            },
        ],
        business: {
            basicService: [{
                    label: '电路租用范围',
                    value: '本地区内'
                },
                {
                    label: '带宽',
                    value: '50M'
                },
                {
                    label: '电路用途',
                    value: '点对点'
                },
                {
                    label: '业务模式',
                    value: '国内业务'
                },
                {
                    label: '是否省外发起',
                    value: '否'
                }
            ],
            aPort: [{
                    label: '装机地址',
                    value: '河北省/石家庄市/长安区 新华大街110'
                },
                {
                    label: 'A端客户联系人',
                    value: '张海梅'
                },
                {
                    label: 'A端客户联系电话',
                    value: '13122233456'
                },
                {
                    label: '接口类型',
                    value: '类型名称'
                },
                {
                    label: 'A端是否入云',
                    value: '否'
                },
                {
                    label: '客户设备情况（A端）',
                    value: '光纤110口'
                },
                {
                    label: 'A端客户名称',
                    value: '王海波'
                }
            ],
            zPort: [{
                    label: '装机地址Z端',
                    value: '光纤299A口'
                },
                {
                    label: 'Z端客户联系人',
                    value: '张文霞'
                },
                {
                    label: 'Z端客户联系电话',
                    value: '***********'
                },
                {
                    label: '接口类型（Z端）',
                    value: '光缆334A口'
                },
                {
                    label: 'Z端是否入云',
                    value: '否'
                },
                {
                    label: 'Z端客户名称',
                    value: '李一文'
                }
            ],
        }
    }
]

export default {
    basicInfo,
    cityList,
    cityInfo,
    businessTypeList,
    detailInfo,
    bandWidthList,
}