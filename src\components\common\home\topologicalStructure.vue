<template>
	<div>
		<div class="main-border main-all main-tuopu">
			<nav-bar title="拓扑结构"></nav-bar>
			<el-row class="tuopu-con">
				<el-col
					:span="24"
					class="tuopu-main"
					v-for="(i, index) in tuopuData"
					:key="index"
				>
					<div class="tuopu-class">
						<el-row>
							<el-col :span="12" class="tuopu-city">{{
								index == 0
									? "自治区"
									: index == 1
									? `盟市(${i.total})`
									: index == 2
									? `区/县(${i.total})`
									: ""
							}}</el-col>
							<el-col :span="6" align="center" class="tuopu-position"
								><div class="tuopu-blue">客户数量</div>
								<div class="tuopu-num">
									{{ i.custNum }}
								</div></el-col
							>
							<el-col :span="5" align="center" class="tuopu-position"
								><div class="tuopu-blue">专线数量</div>
								<div class="tuopu-num">
									{{ i.cirNum }}
								</div></el-col
							>
						</el-row>
					</div></el-col
				>
				<div class="tuopu-bg"></div>
			</el-row>
		</div>
	</div>
</template>

<script>
	import navBar from "@/components/common/home/<USER>";
	export default {
		name: "topologicalStructure",
		components: { navBar },
		props: {},
		data() {
			return {
				tuopuData: [],
			};
		},
    mounted() {
			this.setHeight();
			window.addEventListener('resize', this.setHeight);
      this.getTopologyInfo();
    },
    methods: {
			/**
			 * <AUTHOR>
			 * 动态设置元素高度
			 */
			setHeight() {
				let winH = window.innerHeight-180;
				let pad = '';
				let bgH = '';
				if(winH > 0 && winH < 600) {
					pad = '1.2vh 15px';
					bgH = '25vh'; 
				} else {
					pad = '2.3vh 15px';
					bgH = '30vh'; 
				}
				let chartItemAll = document.querySelectorAll('.tuopu-main');
				for(let i = 0; i < chartItemAll.length; i++) {
					chartItemAll[i].style.padding = pad;
				}
				document.querySelector('.tuopu-bg').style.height = bgH;
			},
      /**
       * <AUTHOR>
       * 获取拓扑结构 /kams/homepage/getTopologyStructureNum
       * 返回值:
       * {
          "PROVINCE_CUSTOMER_NUM": 7, //自治区客户数量 
          "PROVINCE_CIRCUIT_NUM": 2,  //自治区专线数量
          "CITY_NUM": 12,//盟市数量
          "CITY_CIRCUIT_NUM": 0,//盟市专线数量
          "CITY_CUSTOMER_NUM": 3,//盟市客户数量
          "COUNTY_NUM": 110,//区县数量
          "COUNTY_CUSTOMER_NUM": 0, //区县客户数量
          "COUNTY_CIRCUIT_NUM": 0 //区县专线数量   
        }
       */
      getTopologyInfo() {
        let self = this;
        nc.rapi
          .request({
            url: "/rc-rm-kams-biz/kams/homepage/getTopologyStructureNum",
            method: "post",
			data: null,// 参数
            headers: { "Content-Type": "application/json;charset=UTF-8"},
          })
          .then((res) => {
						let obj = res.data
						if(obj) {
							this.tuopuData = [
								{ city: "自治区", custNum: obj.PROVINCE_CUSTOMER_NUM, cirNum: obj.PROVINCE_CIRCUIT_NUM },
								{ city: "自治区", custNum: obj.CITY_CUSTOMER_NUM, cirNum: obj.CITY_CIRCUIT_NUM, total: obj.CITY_NUM },
								{ city: "自治区", custNum: obj.COUNTY_CUSTOMER_NUM, cirNum: obj.COUNTY_CIRCUIT_NUM, total: obj.COUNTY_NUM },
							]
						}
          })
          .catch((err) => {
            console.error(err);
            self.$message({
              showClose: true,
              duration: 2000,
              type: "error",
              message: "数据加载失败，系统异常!",
            });
          });
      },
    },
	};
</script>

<style lang="scss" scoped>
	.main-border {
		padding-bottom: 1vh;
		border: 1px solid rgba(4, 56, 226, 0.58);
		margin-top: 1vh;
		// height: 34.5vh;
		width: 100%;
		height: 100%;
	}
	.tuopu-con {
		padding-top: 1vh;
	}
	.tuopu-main {
		padding: 2.3vh 15px;
	}
	.tuopu-city {
		margin-top: 1vh;
		margin-left: 10px;
		font-size: 2vh;
		color: #fff;
	}
	.tuopu-num {
		color: #fff;
		font-size: 3vh;
		padding-top: 3px;
	}
	.tuopu-blue {
		color: rgba(2, 219, 255, 1);
		font-size: 14px;
	}
	.tuopu-position {
		position: relative;
		top: -8px;
	}
	.tuopu-class {
		background: url("../../../assets/images/tpjg-bg.png");
		background-position: center center;
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
	.tuopu-bg {
		background: url("../../../assets/images/tpjg.png");
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 30vh;
    width: 30%;
    position: absolute;
    left: 85px;
    top: 10px;
	}
</style>
