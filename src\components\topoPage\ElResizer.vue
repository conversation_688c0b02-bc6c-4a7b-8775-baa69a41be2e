<!-- src/components/ElResizer.vue -->
<template>
  <div class="el-resizer" @mousedown="startDrag">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'ElResizer',
  methods: {
    startDrag(event) {
      const startX = event.clientX;
      const startWidth = this.$el.previousElementSibling.offsetWidth;
      const onMouseMove = (event) => {
        const dx = event.clientX - startX;
        this.$el.previousElementSibling.style.width = `${startWidth + dx}px`;
      };
      const onMouseUp = () => {
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      };
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    }
  }
};
</script>

<style scoped>
.el-resizer {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: ew-resize;
  background-color: #ccc;
}
</style>