kind: Deployment
apiVersion: apps/v1
metadata:
  name: nmjpw-web-nginx
  namespace: nmjpw
  selfLink: /apis/apps/v1/namespaces/nmjpw/deployments/nmjpw-web-nginx
  generation: 1
  labels:
    app: nmjpw-web-nginx
    chart: nginx-1.20.1
    heritage: Helm
    release: nmjpw-web
  annotations:
    deployment.kubernetes.io/revision: '1'
  managedFields:
    - manager: Go-http-client
      operation: Update
      apiVersion: apps/v1
      time: '2021-10-27T03:23:54Z'
      fieldsType: FieldsV1
      fieldsV1:
        'f:metadata':
          'f:labels':
            .: {}
            'f:app': {}
            'f:chart': {}
            'f:heritage': {}
            'f:release': {}
        'f:spec':
          'f:progressDeadlineSeconds': {}
          'f:replicas': {}
          'f:revisionHistoryLimit': {}
          'f:selector':
            'f:matchLabels':
              .: {}
              'f:app': {}
              'f:release': {}
          'f:strategy':
            'f:rollingUpdate':
              .: {}
              'f:maxSurge': {}
              'f:maxUnavailable': {}
            'f:type': {}
          'f:template':
            'f:metadata':
              'f:labels':
                .: {}
                'f:app': {}
                'f:chart': {}
                'f:heritage': {}
                'f:release': {}
            'f:spec':
              'f:affinity':
                .: {}
                'f:podAntiAffinity':
                  .: {}
                  'f:preferredDuringSchedulingIgnoredDuringExecution': {}
              'f:containers':
                'k:{"name":"nginx"}':
                  .: {}
                  'f:image': {}
                  'f:imagePullPolicy': {}
                  'f:livenessProbe':
                    .: {}
                    'f:failureThreshold': {}
                    'f:httpGet':
                      .: {}
                      'f:path': {}
                      'f:port': {}
                      'f:scheme': {}
                    'f:initialDelaySeconds': {}
                    'f:periodSeconds': {}
                    'f:successThreshold': {}
                    'f:timeoutSeconds': {}
                  'f:name': {}
                  'f:ports':
                    .: {}
                    'k:{"containerPort":8080,"protocol":"TCP"}':
                      .: {}
                      'f:containerPort': {}
                      'f:name': {}
                      'f:protocol': {}
                  'f:readinessProbe':
                    .: {}
                    'f:failureThreshold': {}
                    'f:httpGet':
                      .: {}
                      'f:path': {}
                      'f:port': {}
                      'f:scheme': {}
                    'f:initialDelaySeconds': {}
                    'f:periodSeconds': {}
                    'f:successThreshold': {}
                    'f:timeoutSeconds': {}
                  'f:resources':
                    .: {}
                    'f:limits':
                      .: {}
                      'f:cpu': {}
                      'f:memory': {}
                    'f:requests':
                      .: {}
                      'f:cpu': {}
                      'f:memory': {}
                  'f:terminationMessagePath': {}
                  'f:terminationMessagePolicy': {}
                  'f:volumeMounts':
                    .: {}
                    'k:{"mountPath":"/etc/nginx/nginx.conf"}':
                      .: {}
                      'f:mountPath': {}
                      'f:name': {}
                      'f:subPath': {}
              'f:dnsPolicy': {}
              'f:restartPolicy': {}
              'f:schedulerName': {}
              'f:securityContext': {}
              'f:terminationGracePeriodSeconds': {}
              'f:volumes':
                .: {}
                'k:{"name":"nginx-server-block"}':
                  .: {}
                  'f:configMap':
                    .: {}
                    'f:defaultMode': {}
                    'f:items': {}
                    'f:name': {}
                  'f:name': {}
    - manager: kube-controller-manager
      operation: Update
      apiVersion: apps/v1
      time: '2022-04-28T02:14:57Z'
      fieldsType: FieldsV1
      fieldsV1:
        'f:metadata':
          'f:annotations':
            .: {}
            'f:deployment.kubernetes.io/revision': {}
        'f:status':
          'f:availableReplicas': {}
          'f:conditions':
            .: {}
            'k:{"type":"Available"}':
              .: {}
              'f:lastTransitionTime': {}
              'f:lastUpdateTime': {}
              'f:message': {}
              'f:reason': {}
              'f:status': {}
              'f:type': {}
            'k:{"type":"Progressing"}':
              .: {}
              'f:lastTransitionTime': {}
              'f:lastUpdateTime': {}
              'f:message': {}
              'f:reason': {}
              'f:status': {}
              'f:type': {}
          'f:observedGeneration': {}
          'f:readyReplicas': {}
          'f:replicas': {}
          'f:updatedReplicas': {}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nmjpw-web-nginx
      release: nmjpw-web
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: nmjpw-web-nginx
        chart: nginx-1.20.1
        heritage: Helm
        release: nmjpw-web
    spec:
      volumes:
        - name: nginx-server-block
          configMap:
            name: nmjpw-web-nginx-config
            items:
              - key: nginx.conf
                path: nginxConfPath
            defaultMode: 420
      containers:
        - name: nginx
          image: 'harbor.dcos.xixian.unicom.local/nmjpw/nmjpw-web:latest'
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            limits:
              cpu: 100m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 256Mi
          volumeMounts:
            - name: nginx-server-block
              mountPath: /etc/nginx/nginx.conf
              subPath: nginxConfPath
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            timeoutSeconds: 1
            periodSeconds: 20
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app: nmjpw-web-nginx
                    release: nmjpw-web
                topologyKey: kubernetes.io/hostname
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
status:
  observedGeneration: 1
  replicas: 1
  updatedReplicas: 1
  readyReplicas: 1
  availableReplicas: 1
  conditions:
    - type: Progressing
      status: 'True'
      lastUpdateTime: '2021-10-27T03:24:03Z'
      lastTransitionTime: '2021-10-27T03:23:54Z'
      reason: NewReplicaSetAvailable
      message: ReplicaSet "nmjpw-web-nginx-59bdb97d94" has successfully progressed.
    - type: Available
      status: 'True'
      lastUpdateTime: '2022-04-28T02:14:57Z'
      lastTransitionTime: '2022-04-28T02:14:57Z'
      reason: MinimumReplicasAvailable
      message: Deployment has minimum availability.
