.cp {
  cursor: pointer;
}
.cf {
  color: #ffffff;
}
.c0 {
  color: #000000;
}
.red {
  color: red;
}
.c-ed {
  color: #EDEDED;
}
.bgdark {
  color: #FFFFFF;
  background: #E8EAF1;
}
.bg-header {
  color: #FFFFFF;
  background: #262B39;
}
.bg-o {
  background: #06529d;
}
.border-o {
  border-color: #06529d;
}
.tac {
  text-align: center;
}
.tar {
  text-align: right;
}
.ai-center {
  align-items: center;
}
.lh62 {
  line-height: 62px;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.ml10 {
  margin-left: 10px;
}
.ml16 {
  margin-left: 16px;
}
.ml20 {
  margin-left: 20px;
}
.mt16 {
  margin-top: 16px;
}
.mt130 {
  margin-top: 130px;
}
.wf {
  width: 100%;
}
.hf {
  height: 100%;
}
.db {
  display: block;
}
.df {
  display: flex;
}
.dif {
  display: inline-flex;
}
.jcsb {
  justify-content: space-between;
}
.jcc {
  justify-content: center;
}
.mr16 {
  margin: 16px auto;
}
.mb16 {
  margin-bottom: 16px;
}
.fs14 {
  font-size: 14px;
}
.fs16 {
  font-size: 16px;
}
.fs18 {
  font-size: 18px;
}
.fs20 {
  font-size: 20px;
}
.br0 {
  border-radius: 0px;
}
.br8 {
  border-radius: 8px;
}

.empty-text {
  color: #A9B0B4;
  border-bottom: 1px solid #D9D9D9;
}
.shadow-radius{
  box-shadow: 0px 1px 10px 0px rgba(0,0,0,0.1);
  border-radius: 2px;
}
.el-select-dropdown {
  background: #193758 !important;
  border: 1px solid transparent !important;
}
.el-select-dropdown__item {
  color: #fff !important;
}
.el-popper .popper__arrow, .el-popper .popper__arrow::after {
  border-bottom-color: #193758 !important;
}
.el-select-dropdown__item.hover, .el-select-dropdown__item:hover {
  background: rgba(45,152,255,0.15) !important;
}
/* .el-select-dropdown__item { */
  .el-select-dropdown__item.selected,
  .el-select-dropdown__item.selected:hover {
      color: #ffffff !important;
      background: #06529d !important;
  }
  ::-webkit-scrollbar{
    width: 5px;
    height: 5px;
  }
/* } */