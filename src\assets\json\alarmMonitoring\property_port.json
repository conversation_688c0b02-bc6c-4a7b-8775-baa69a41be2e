{"queryConfig": {"columns": [{"displayName": "地市", "options": {"view": "input", "hidden": false}, "type": "string", "columnName": "city", "tableColumnName": "city"}, {"displayName": "电路名称", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "criticalName", "tableColumnName": "criticalName"}, {"displayName": "电路编码", "options": {"hidden": false, "view": "input"}, "type": "string", "columnName": "criticalCode", "tableColumnName": "criticalCode"}], "classId": "alarm", "className": "告警"}, "tableConfig": {"columns": [{"displayName": "地市", "type": "string", "columnName": "CITY"}, {"displayName": "区县", "type": "string", "columnName": "TOWN"}, {"displayName": "端口类型", "type": "string", "columnName": "PORT_TYPE"}, {"displayName": "A端名称", "type": "string", "columnName": "A_DEVICE_NAME"}, {"displayName": "A端类型", "type": "string", "columnName": "A_DEVICE_TYPE"}, {"displayName": "A端IP", "type": "string", "columnName": "A_DEVICE_IP"}, {"displayName": "A端端口", "type": "string", "columnName": "A_PORTNAME"}, {"displayName": "A端流出流量", "type": "string", "columnName": "A_OUTOCTS"}, {"displayName": "A端流出带宽利用率", "type": "string", "columnName": "A_OUTRATE"}, {"displayName": "A端流出带宽利用率峰值", "type": "string", "columnName": "A_OUTRATEMAX"}, {"displayName": "A端流入流量", "type": "string", "columnName": "A_INOCTS"}, {"displayName": "A端流入带宽利用率", "type": "string", "columnName": "A_INRATE"}, {"displayName": "A端流入带宽利用率峰值", "type": "string", "columnName": "A_INRATEMAX"}, {"displayName": "带宽", "type": "string", "columnName": "BANDWIDTH"}, {"displayName": "波长", "type": "string", "columnName": "LINE_LENGTH"}, {"displayName": "光纤距离", "type": "string", "columnName": "DISTANCE"}, {"displayName": "发送光功率", "type": "string", "columnName": "TX"}, {"displayName": "发送灵敏度", "type": "string", "columnName": "TX_LOW_THRESHOLD"}, {"displayName": "发送过载值", "type": "string", "columnName": "TX_HIGH_THRESHOLD"}, {"displayName": "接收光功率", "type": "string", "columnName": "RX"}, {"displayName": "接收灵敏度", "type": "string", "columnName": "RX_LOW_THRESHOLD"}, {"displayName": "接收过载值", "type": "string", "columnName": "RX_HIGH_THRESHOLD"}, {"displayName": "误码数", "type": "string", "columnName": "ERRORNUM"}, {"displayName": "端口状态", "type": "string", "columnName": "OPER_STATUS"}, {"displayName": "日期", "type": "string", "columnName": "DATE"}, {"displayName": "小时", "type": "string", "columnName": "HOUR"}], "selectColumns": ["CITY", "PORT_TYPE", "A_DEVICE_TYPE", "A_PORTNAME", "A_OUTRATE", "A_INOCTS", "A_INRATEMAX", "LINE_LENGTH", "TX", "TX_HIGH_THRESHOLD", "RX_LOW_THRESHOLD", "RX_HIGH_THRESHOLD", "ERRORNUM", "OPER_STATUS", "DATE", "HOUR", "TOWN", "A_DEVICE_NAME", "A_DEVICE_IP", "A_OUTOCTS", "A_OUTRATEMAX", "A_INRATE", "BANDWIDTH", "DISTANCE", "TX_LOW_THRESHOLD", "RX"]}}