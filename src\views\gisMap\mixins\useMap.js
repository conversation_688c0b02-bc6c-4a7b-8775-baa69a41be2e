import darkStyle from '../../../assets/js/darkStyle.json';
export default {
  data: () => ({
    pointLineData: [],
    darkLabel:{
          color: 'white',
          backgroundColor: 'rgba(33, 33, 33, 0.5)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          width: '200px',
          whiteSpace: 'normal',
          wordBreak: 'break-all',
          lineHeight: '16px'
        },
    whiteLabel:{
          color: 'black',
          backgroundColor: 'rgb(215,215,215)',
          border: 'none',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          width: '200px',
          whiteSpace: 'normal',
          wordBreak: 'break-all',
          lineHeight: '16px'
        }
  }),

  created() {
  },
  mounted() {
    this.initMapFn()
    this.getPointLineData();
  },
  methods: {
    // 初始化地图
    initMapFn() {
      this.map = new BMapGL.Map('allmap');
      const center = new BMapGL.Point(
        this.configEditing.mapCenter[0],
        this.configEditing.mapCenter[1]
      );
      this.map.centerAndZoom(center, this.configEditing.mapZoom);
      this.map.enableScrollWheelZoom(true);
      // this.map.setTilt(45);
      // 关闭三维楼块（城市立体图）
      this.map.setDisplayOptions({
        building: false
      });
      // 切换地图样式->dark
      if (this.circuitName) this.changeMapStyle();

      // 监听地图加载完成
      this.map.addEventListener('tilesloaded', () => {
        this.mapLoaded = true;
        this.addMapLayers()
      });
    },

    // 获取点线数据
    getPointLineData() {
      this.$api.gis_data.getMapPointLineData().then(res => {
        this.pointLineData = res;
        this.addMapLayers();
      })
    },

    // 添加地图图层
    addMapLayers() {
      this.map.clearOverlays();
      // const point = new BMapGL.Point(108.9710, 34.2258);
      // // 创建自定义图标
      // const icon = new BMapGL.Icon(
      //   require('@/assets/images/icon_1.png'), // 图标路径
      //   new BMapGL.Size(32, 32), // 图标大小
      //   {
      //     anchor: new BMapGL.Size(16, 32), // 图标锚点
      //     imageOffset: new BMapGL.Size(0, 0) // 图标偏移
      //   }
      // );
      // const marker = new BMapGL.Marker(point
      //   // , { icon }
      // ); // 创建标注，并设置图标为自定义图标
      // this.map.addOverlay(marker);



      // 西安五个地标点的坐标和信息
      const landmarks = this.pointLineData

      // 渲染所有地标
      landmarks.forEach((landmark) => {
        const point = new BMapGL.Point(landmark.lng, landmark.lat);

        // 创建自定义图标（暂时注释）
        // const icon = new BMapGL.Icon(
        //   require('@/assets/images/icon_1.png'),
        //   new BMapGL.Size(32, 32),
        //   {
        //     anchor: new BMapGL.Size(16, 32),
        //     imageOffset: new BMapGL.Size(0, 0)
        //   }
        // );

        const marker = new BMapGL.Marker(point
          // , { icon: icon }
        );

        // 添加标签
        const label = new BMapGL.Label(`${landmark.name}<br>${landmark.description}`);
        // 判断有网元数据则使用darkLabel
        this.circuitName?label.setStyle(this.darkLabel):label.setStyle(this.whiteLabel);

        label.setOffset(new BMapGL.Size(10, -40));
        marker.setLabel(label);

        this.map.addOverlay(marker);
      });

      // 添加连接小寨和大雁塔的线段
      this.addConnectionLine();
    },

    // 添加连接线段的方法
    addConnectionLine() {
      // 从 landmarks 中查找小寨和大雁塔的坐标
      const landmarks = [
        { name: '小寨', lng: 108.9434, lat: 34.2178 },
        { name: '大雁塔', lng: 108.9646, lat: 34.2192 }
      ];
      // 折线拐点（不在 landmarks 数据中，仅用于折线渲染）
      const midPoint = { lng: 108.954, lat: 34.225 };
      const points = [
        new BMapGL.Point(landmarks[0].lng, landmarks[0].lat),
        new BMapGL.Point(midPoint.lng, midPoint.lat),
        new BMapGL.Point(landmarks[1].lng, landmarks[1].lat)
      ];
      const polyline = new BMapGL.Polyline(points, {
        // strokeColor: '#FF5722',    // 线条颜色：橙红色
        strokeColor: '#11a7efff',    // 线条颜色：蓝色
         
        strokeWeight: 3,           // 线条宽度
        strokeOpacity: 0.8,        // 线条透明度
        strokeStyle: 'solid'       // 线条样式：实线
      });

      // 添加线段到地图
      this.map.addOverlay(polyline);

    },

    // 切换地图样式
    changeMapStyle() {
      this.map.setOptions({
        style: {
          styleJson: darkStyle
        },
        styleUrl: 'https://gis.10010.com:8219/dugis-baidu/baidumap/bmapgl/mapstyle/mapstyle.json'
      });
    },

  }
}
