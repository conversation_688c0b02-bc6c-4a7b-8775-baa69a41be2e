<template>
<div style="height: 70%;">
  <div class="nc-query">
    <el-form :inline="true" :model="form" label-width="100px">
      <el-form-item class="formItem" label="用户名">
        <el-input v-model="form.userName" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="归属地市">
        <el-select v-model="form.cityCode" placeholder="归属地市" @change="cityChange" clearable>
          <el-option class="infinite-list-item" v-for="option in allCityData" :key="option.districtId"
            :label="option.alias" :value="option.districtId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="归属区县">
        <el-select v-model="form.villageCode" placeholder="归属区县" @focus="getAllCounties" clearable>
          <el-option class="infinite-list-item" v-for="option in allCountiesData" :key="option.districtId"
            :label="option.alias" :value="option.districtId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
      <el-form-item style="float:right;width:5%">
        <el-button type="primary" @click="selectAllUserInfo">查询</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div style="margin:0 0 15px 0">
    <el-button type="primary" size="mini" icon="el-icon-plus" @click="addUser">新增</el-button>
  </div>
  <el-table border :data="tableBody" :header-cell-style="{
    color: '#9ED8FF',
    'background-color': 'rgba(15, 52, 124, 1)',
    'text-align': 'center',
    'font-size': '14px',
    'border-right': '1px rgba(15, 0, 137, .8) solid'
  }" height="95%" style="border-radius: 4px 4px 0 0;
      border:none;margin-top: 5px;">
    <el-table-column type="selection" width="55">
    </el-table-column>
    <el-table-column align='center' v-for="(i, idx) in tableColumns" :key="idx" :label="i.displayName"
      :prop="i.columnName" show-overflow-tooltip>
      <template slot-scope="scope">
        <el-dropdown size="mini" trigger="click" :ref="'messageDrop' + scope.row.account"
          v-if="i.columnName == 'roleList'"
          @visible-change="val => visibleChange(val, scope.row.roleList, 'tree' + scope.row.account)">
          <el-button size="mini">管理员<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-tree :data="roleTreeData" show-checkbox :default-checked-keys="scope.row[i.columnName]"
              default-expand-all node-key="id" :ref="'tree' + scope.row.account" highlight-current style="width: 140px;">
            </el-tree>
            <div style="float: right;margin-right: 10px">
              <el-button type="text" icon="el-icon-close"
                @click="closeModels('tree' + scope.row.account, scope.row.roleList, 'messageDrop' + scope.row.account)"></el-button>
              <el-button type="text" icon="el-icon-check"
                @click="getCheckedKeys('tree' + scope.row.account, scope.row.id, 'messageDrop' + scope.row.account)"></el-button>
            </div>
          </el-dropdown-menu>
        </el-dropdown>
        <span v-else-if="i.displayName == '绑定客户'">
          <el-link style="color: #05e1ff" @click="bindCustomer(scope.row)"> 绑定客户 </el-link>
        </span>
        <span v-else>{{ scope.row[i.columnName] ? scope.row[i.columnName] : "" }}</span>
      </template>

    </el-table-column>
    <el-table-column fixed="right" label="操作" align="center" width="100">
      <template slot-scope="scope">
        <el-button title="重置密码" type="text" size="mini" icon="el-icon-lock" @click="editPassword(scope.row)" />
        <el-button title="编辑" type="text" size="mini" icon="el-icon-edit" style="margin-right: 5px"
          @click="addUser(scope.row)"></el-button>
        <el-popconfirm confirm-button-text='好的' cancel-button-text='不用了' icon="el-icon-info" icon-color="red"
          title="确定删除这个用户吗？" @confirm="deleteRole(scope.row)">
          <el-button title="删除" slot="reference" type="text" size="mini" icon="el-icon-delete"></el-button>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination class="page-list" @size-change="handleSizeChange" @current-change="handleCurrentChange"
    :current-page.sync="pageNumber" :page-sizes="[1, 10, 20, 30, 40, 50]" :page-size="pageSize" background
    layout="total, prev, pager, next, sizes" :total.sync="total" style="float: right">
  </el-pagination>
  <!-- 新增/编辑用户 -->
  <el-dialog :title="`${formData.id ? '修改' : '新增'}用户`" width="600px" :visible.sync="visible" append-to-body
    :before-close="closeModel" @close="resetForm" :close-on-click-modal="false">
    <el-form ref="formData" :model="formData" label-width="110px" size="small" :rules="rules">

      <el-form-item label="账号 :" prop="account">
        <el-input v-model.trim="formData.account" style="width: 100%" placeholder="账号"
          :disabled="formData.id ? true : false" />
      </el-form-item>
      <el-form-item label="用户名 :" prop="userName">
        <el-input v-model.trim="formData.userName" style="width: 100%" placeholder="姓名" />
      </el-form-item>
      <el-form-item label="邮箱 :" prop="email">
        <el-input v-model.trim="formData.email" style="width: 100%" placeholder="邮箱" />
      </el-form-item>
      <el-form-item label="手机 :" prop="phone">
        <el-input v-model.trim="formData.phone" style="width: 100%" placeholder="手机" />
      </el-form-item>
      <el-form-item label="归属地市 :" prop="cityCode">
        <el-select v-model="formData.cityCode" style="width: 100%" @change="cityChange" placeholder="归属地市">
          <el-option class="infinite-list-item" v-for="option in allCityData" :key="option.districtId"
            :label="option.alias" :value="option.districtId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="归属区县 :" prop="villageCode">
        <el-select v-model="formData.villageCode" style="width: 100%" placeholder="归属区县" clearable>
          <el-option class="infinite-list-item" v-for="option in allCountiesData" :key="option.districtId"
            :label="option.alias" :value="option.districtId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="角色 :" prop="roleList" v-if="!formData.id">
        <el-checkbox-group v-model="formData.roleList">
          <el-checkbox v-for="(item, index) in allCustUserRoles" :key="index" :label="item">
            {{ item.roleName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- <el-form-item label="客户 :" prop="custIdList" v-if="!formData.id">
        <el-checkbox-group v-model="formData.custIdList">
          <el-checkbox v-for="(item, index) in noCustomerAdded" :key="index" :label="item">
            {{ item.custName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item> -->

      <div style="text-align: center; margin-top: 20px">
        <el-button size="small" @click="closeModel()">
          取消
        </el-button>
        <el-button type="primary" size="small" @click="subForm('formData')">
          提交
        </el-button>
      </div>
    </el-form>
  </el-dialog>
  <!-- 重置密码 -->
  <el-dialog class="password" width="700px" append-to-body :visible.sync="passwordVisible" title="重置密码">
    <div>
      <!-- :title="'注意：默认密码将改为【'+formPassData.nmmpd+'】！'"  -->
      <el-alert :title="'注意：默认密码将改为【' + formPassData.nmmpd + '】！'" type="warning" :closable="false" />
      <div class="h30" style="margin: 0 0 20px 0" />
      <div style="text-align: center">
        <el-button type="primary" @click="submitForm(formPassData)">
          确认
        </el-button>
        <el-button @click="passwordVisible = false">
          取消
        </el-button>
      </div>
    </div>
  </el-dialog>
</div>
</template>

<script>
import { isComplexPassword } from "@/utils/validate";
export default {
  data() {
    var validatePass = (rule, value, callback) => {
      if (!isComplexPassword(value)) {
        callback(new Error("密码必须大于八位，且包含大写字母、小写字母、数字、特殊字符中的任意三类"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.formPassData.nmmpd) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    return {
      passwordVisible: false,
      visible: false,
      detailModal: false,
      formPassData: {
        nmmpd: 'Qsx@56483',
        rmmpd: ''
      },
      ruserId: '',
      form: {
        userName: '',
        cityCode: '',
        villageCode: ''
      },
      formData: {
        password: "Qsx@56483",
        roleList: [],
        custIdList: []
      },
      total: 10,
      pageSize: 10,
      pageNumber: 1,
      districtId: '350002020000000000100060',
      allCityData: [],
      allCountiesData: [],
      allCustUserRoles: [],
      roleList: [],
      custIdList: [],
      tableColumns: [{
        displayName: '用户名',
        columnName: 'userName'
      }, {
        displayName: '账号',
        columnName: 'account'
      }, {
        displayName: '手机号',
        columnName: 'phone'
      }, {
        displayName: '邮箱',
        columnName: 'email'
      }, {
        displayName: '地市',
        columnName: 'cityName'
      }, {
        displayName: '区县',
        columnName: 'villageName'
      }, {
        displayName: '角色',
        columnName: 'roleList'
      },
      ],
      tableBody: [],
      userInformation: [],
      customerAdded: [],
      roleTreeData: [],
      customerName: '',
      deleteCustomer: [],
      customerNull: [],
      passRules: {
        nmmpd: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ],
        rmmpd: [
          { required: true, validator: validatePass2, trigger: 'blur' }
        ]
      },
      rules: {
        account: [
          { required: true, message: '请输入账号', trigger: 'blur' },
          { max: 10, message: "账号长度最多10个字符", trigger: "change" },
          {
            validator: function (rule, value, callback) {
              var reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
              if (reg.test(value)
              ) {
                callback(new Error("账号中不能含有中文"));
              } else {
                callback();
              }
            }, trigger: 'blur'
          }
        ],
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { max: 10, message: "角色名称长度最多10个字符", trigger: "change" }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3|5|7|8|9]\d{9}$/, message: '请输入正确的号码格式', trigger: 'change' }
        ],

        cityCode: [
          { required: true, message: '请选择归属地市', trigger: 'blur' },
        ],
        villageCode: [
          { required: true, message: '请选择归属区县', trigger: 'blur' },
        ],
        roleList: [
          { type: 'array', required: true, message: '请至少选择一个角色', trigger: 'change' }
        ],
        custIdList: [
          { type: 'array', required: true, message: '请至少选择一个客户', trigger: 'change' }
        ],
      },
    }
  },
  created() {
    this.getAllCity()
    this.getcustUserRoles()
    this.selectAllUserInfo()
  },
  methods: {
    getcustUserRoles() {
      this.$api.slaApi.getcustUserRoles()
        .then(res => {
          this.allCustUserRoles = res.data
          res.data.map(item => {
            this.roleTreeData.push({
              id: item.roleId,
              label: item.roleName
            })
          })
        })
    },
    getAllCity() {
      this.$api.slaApi.getAllCity()
        .then(res => {
          this.allCityData = res.data
        })
    },
    cityChange(val) {
      this.districtId = val;
      this.$set(this.formData, 'villageCode', '')
      this.getAllCounties()
    },
    getAllCounties() {
      this.$api.slaApi.getAllCounties(this.districtId)
        .then(res => {
          this.allCountiesData = res.data
        })
    },
    selectAllUserInfo() {
      this.$api.slaApi.selectAllUserInfo({
        "pageNum": this.pageNumber,
        "pageSize": this.pageSize,
        ...this.form
      })
        .then(res => {
          let roleId = []
          this.tableBody = res.data;
          res.data.map(item => {
            roleId = []
            if (item.roleList.length > 0) {
              item.roleList.map((i, ) => {
                roleId.push(i.roleId)
              })
              item.roleList = roleId
            }
          })
          this.total = res.total;
        })
    },
    // 重置表单
    resetQuery() {
      this.form = {
        userName: '',
        city: '',
        counties: ''
      };
      this.pageSize = 10;
      this.pageNumber = 1;
      this.selectAllUserInfo()
    },
    addUser(row) {
      if (row.id) {
        this.$set(this.formData, 'id', row.id ? row.id : '')
        this.$set(this.formData, 'account', row.account ? row.account : '')
        this.$set(this.formData, 'userName', row.userName ? row.userName : '')
        this.$set(this.formData, 'email', row.email ? row.email : '')
        this.$set(this.formData, 'phone', row.phone ? row.phone : '')
        this.$set(this.formData, 'cityCode', row.cityCode ? row.cityCode : '')
        this.districtId = row.cityCode;
        this.getAllCounties()
        this.$set(this.formData, 'villageCode', row.villageCode ? row.villageCode : '')
      } else {
        this.formData = {
          password: "Qsx@56483",
          roleList: [],
          custIdList: []
        };
      }
      this.visible = true;
    },
    subForm(formData) {
      if (this.formData.id) {
        this.$refs.formData.validate(valid => {
          if (valid) {
            this.subEditForm(formData);
          }
        });
      } else {
        this.$refs.formData.validate(valid => {
          if (valid) {
            this.subAddForm(formData);
          }
        });

      }
    },
    subAddForm() {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.$api.slaApi.insertUserInfo(this.formData)
            .then(res => {
              if (res.resultCode == 200) {
                this.$message({
                  message: res.data,
                  type: 'success'
                });
                this.visible = false;
              } else {
                this.$message.error('新增失败');
              }
              this.selectAllUserInfo()
            })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    subEditForm() {
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          console.log(this.formData);
          let editForm = {
            ...this.formData,
            "roleList": [
              {
                "roleName": "管理员",
                "roleId": 1
              }
            ],
          };
          this.$api.slaApi.updateUser(editForm)
            .then(res => {
              if (res.resultCode == 200) {
                this.$message({
                  message: res.data,
                  type: 'success'
                });
                this.visible = false;
              } else {
                this.$message.error('修改失败');
              }
              this.selectAllUserInfo()
            })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    // 深拷贝
    extendCopy(p) {
      var c = {};
      for (var i in p) {
        c[i] = p[i];
      }
      c.uber = p;
      return c;
    },
    // 重置密码
    editPassword(row) {
      if (JSON.parse(localStorage.getItem("user")) == 'admin') {
        this.passwordVisible = true;
        this.ruserId = row.account;
      } else {
        this.$message({
          type: "error",
          message: "只有超级管理员可以重置密码",
        });
      }
    },
    submitForm() {
      let params = {
        "userName": this.ruserId,
        "password": this.formPassData.nmmpd
      }
      this.$api.slaApi.changePassword(params)
        .then((result) => {
          if (result.resultCode == 200) {
            this.$message({
              showClose: true,
              duration: 2000,
              type: "success",
              message: result.data,
            });
            this.passwordVisible = false;
          } else {
            this.$message({
              showClose: true,
              duration: 2000,
              type: "warning",
              message: result.data,
            });
          }
        });
    },
    deleteRole(row) {
      this.$api.slaApi.deleteUser({ id: row.id })
        .then(res => {
          if (res.resultCode == 200) {
            this.$message({
              message: res.data,
              type: 'success'
            });
            this.visible = false;
          } else {
            this.$message.error('删除失败');
          }
          this.selectAllUserInfo()
        })
    },
    getCheckedKeys(trf, id, drop) {
      let custUserRoleRelationsList = []
      this.$refs[trf][0].getCheckedKeys().map(item => {
        custUserRoleRelationsList.push({
          "userId": id,
          "roleId": item
        })
      })

      this.$api.slaApi.updateUserInfo({ custUserRoleRelationsList: custUserRoleRelationsList })
        .then(res => {
          if (res.resultCode == 200) {
            this.$message({
              message: res.data,
              type: 'success'
            });
            this.visible = false;
            this.$refs[drop][0].hide();
          } else {
            this.$message.error('更新失败');
          }
          this.selectAllUserInfo()
        })
    },
    closeModels(tree, list, drop) {
      this.$refs[drop][0].hide();
      this.setCheckedKeys(list, tree)
    },
    setCheckedKeys(list, drop) {
      this.$refs[drop][0].setCheckedKeys(list);
    },
    visibleChange(val, list, tree) {
      if (!val) {
        this.setCheckedKeys(list, tree)
      }
    },
    /**
     * <AUTHOR>
     * 监听每页条数选择
     */
    handleSizeChange(val) {
      this.pageSize = val;
      this.selectAllUserInfo();
    },

    /**
     * <AUTHOR>
     * 监听当前页
     */
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.selectAllUserInfo();
    },
    resetForm() {
      this.$refs.formData.resetFields();
    },
    closeModel() {
      this.visible = false;
    },
    closeModelss() {
    },

  }

}
</script>

<style lang="less" scoped>
.nc-query {
  border-radius: 2px;
  background-color: rgba(23, 70, 137, 0.25);
  padding: 24px 16px 12px;
  margin: 25px 0px;
  // height: 10vh;
}

/deep/ .el-checkbox__inner {
  background-color: transparent;
  border: 1px solid #02dbff;
}

// .password {
//   ::v-deep {
//     .el-dialog {
//       // height: 20vh;
//     }
//   }

// }

::v-deep {
  .el-dropdown-menu {
    width: 120px !important;
  }

  .el-button--text {
    font-size: 18px !important;
  }

  .el-table__fixed-right-patch {
    background-color: rgba(23, 70, 137, 1);
  }

  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch {
    border: none;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }

  .el-dialog {

    // width: 60%;
    background: url("../../assets/images/dialogbg.png") center center no-repeat;
    background-size: 100% 100%;
    margin-top: 10vh !important;
  }

  .el-dialog__header {
    justify-content: left;
    background: none;
  }

  .el-dialog__title {
    justify-content: left;
    line-height: 20px;
    font-weight: 800;
    height: 20px;
    display: flex;
    color: rgba(255, 255, 255, 1);
    font-size: 22px;
  }

  .el-dialog__title::before,
  .el-dialog__title::after {
    content: none;
  }

  .el-dialog__headerbtn {
    width: 30px;
    height: 30px;
    float: right;
    top: 10px;
    right: 10px;
    background: url("../../assets/images/x.png") center center no-repeat;
    background-size: 100% 100%;
  }

  .el-dialog__body {
    height: 85%;

    .detail-info {
      max-height: 800px;
      height: 100%;
    }

    .el-form {
      min-height: 100px;
      width: 95%;
      // margin: 30px auto 20px;
      margin-top: 30px;
      text-align: center;

      .el-form-item__label {
        color: #fff;
      }

      .el-input__inner {
        color: #02dbff;
        background: none;
        border: 1px solid rgba(37, 190, 247, 0.5);
        // height: 24px !important;
        line-height: 24px;
      }

      .el-switch__label,
      .el-checkbox {
        color: #fff;
      }

      .el-button--primary {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;

        &:focus {
          background-color: #409EFF;
          border-color: #409EFF;
        }

        &:hover {
          background-color: #409EFF;
          border-color: #409EFF;
        }
      }

      .el-select-dropdown__item:hover {
        color: #409EFF;
        background: #40a0ff27;
      }

      .el-button--default {
        color: #409EFF;
        background: transparent;
        border-color: #409EFF;
      }
    }
  }
}
.el-dropdown-menu {
    background: #193758 !important;
    border: 1px solid transparent !important;
    .el-tree {
      background-color: transparent;
      color: #fff;
    }
    ::v-deep .el-tree-node__content:hover, 
    ::v-deep .el-upload-list__item:hover {
        background-color: #204a79d1!important;
    }
  }

  .el-button--text {
    font-size: 18px !important;
  }

  .el-table__fixed-right-patch {
    background-color: rgba(23, 70, 137, 1);
  }

  .el-table--border th.el-table__cell,
  .el-table__fixed-right-patch {
    border: none;
  }

  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }

.addCustomer {
  height: 5%;
  width: 6%;
  float: left;
  position: relative;
  top: 45%;
  left: 4%;
  background: url("../../assets/images/addCustomer.png") center center no-repeat;
  background-size: 100% 100%;
  transform: rotate(180deg);
}
</style>