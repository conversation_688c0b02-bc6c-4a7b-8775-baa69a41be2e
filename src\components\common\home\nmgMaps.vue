<template>
	<div class="element_num bg cf" ref="conheight">
		<search-query
			ref="searchPart"
			@transfer="getSearch"
			:cityName="cityName"
			@showMapLine="showLine"
			@clearLines="clearLines"
		></search-query>
		<div class="content tac">
			<div
				id="nmg_map"
				class="nmg-map-box"
				ref="nmgMap"
				:style="{ height: pHeight, width: pWidth }"
			></div>
		</div>
	</div>
</template>

<script>
import searchQuery from "@/components/common/home/<USER>";
import echarts from "echarts";
import { innerMongoliaMap } from "@/assets/js/nmg-map.js";
export default {
	name: "nmg_maps",
	components: {
		searchQuery,
	},
	props: {
		// 宽度
		width: {
			type: String,
			default: () => "100%",
		},
		// 高度
		height: {
			type: String,
			default: () => "",
		},
		// 指定缩放比例
		custZoom: {
			type: String,
			default: () => "0.5",
		},
	},
	data() {
		return {
			pHeight: "35vh",
			pWidth: "100%",
			// conheight: this.$refs.conheight.offsetHeight,
			cityName: "",
			cityInfo: {},
			cityIndex: 0,
			form: {
				city: undefined,
			},
			// zoom: 0.98,
			cityList: [],
			mapLineData: [],
		};
	},
	watch: {
		width(n) {
			this.pWidth = n;
			echarts.init(document.getElementById("nmg_map")).dispose();
			this.pHeight = this.pHeight;
			this.pWidth = n;
			let data = [];
			this.initMap(data);
		},
		height(n) {
			echarts.init(document.getElementById("nmg_map")).dispose();
			this.pHeight = n;
			this.pWidth = this.pWidth;
			let data = [];
			this.initMap(data);
		},
		custZoom(n) {},
		zoom(n) {},
		innerMongoliaMap: {
			handler(n, o) {
				if (n && n.length > 0) {
					this.cityList = n.map((i) => ({
						key: i.value,
						...i,
					}));
					this.changeMap("内蒙古", 0);
				}
			},
			deep: true,
		},
		cityName: {
			handler(n, o) {
				if (n) {
					this.cityName = n;
				}
			},
			deep: true,
		},
		mapLineData: {
			handler(n, o) {},
			deep: true,
		},
	},

	mounted() {
		this.$nextTick(() => {
			if (innerMongoliaMap && innerMongoliaMap.length > 0) {
				this.cityList = innerMongoliaMap.map((i) => ({
					key: i.value,
					...i,
				}));
			}
			this.changeCity("10");
		});
	},
	methods: {
		getSearch(val) {
			let cityInfo = this.cityList.filter((i) => {
				return i.value == val;
			})[0];
			this.$emit("transferVal", cityInfo.label);
			this.changeCity(val);
		},
		changeCity(cityVal) {
			if (cityVal) {
				let cityName = "";
				this.cityIndex = null;
				this.cityList.map((i, idx) => {
					if (i.value === cityVal) {
						cityName = i.label;
						this.cityIndex = idx;
						this.cityInfo = i;
						if (i.label == "内蒙古") {
							this.zoom = 0.6;
						} else {
							this.zoom = this.cityInfo.cityZoom;
						}
						this.changeMap(cityName, this.cityIndex);
						let data = [];
						echarts.registerMap(cityName, i.json);
						this.initMap(data);
					}
				});
			}
		},
		/**
		 * <AUTHOR>
		 * 渲染地图飞线
		 */
		showLine(data) {
			this.mapLineData = [
				{
					name: data.no,
					fromName: data.fromName,
					toName: data.toName,
					// coords: [[101.06241 , 41.96030], [105.65188, 38.84067]],
					coords: [
						[data.latA, data.lonA],
						[data.latZ, data.lonZ],
					],
				},
			];
			this.dots = this.getDotData(this.mapLineData);
			this.initMap();
		},
		/**
		 * <AUTHOR>
		 * 重置地图飞线
		 */
		clearLines() {
			this.mapLineData = [];
			this.dots = [];
		},
		/**
		 * <AUTHOR>
		 * 渲染线路端点
		 */
		getDotData(data) {
			var resData = [];
			data.map((i) => {
				resData.push(
					{
						name: i.fromName,
						value: i.coords[0],
					},
					{
						name: i.toName,
						value: i.coords[1],
					}
				);
			});
			return resData;
		},
		/**
		 * <AUTHOR>
		 * 切换地图
		 */
		changeMap(mapName, mapIndex) {
			echarts.registerMap(mapName, innerMongoliaMap[mapIndex].json);
		},
		/**
		 * 初始化地图
		 * <AUTHOR>
		 */
		initMap(data) {
			let cityName = this.cityInfo.label ? this.cityInfo.label : null;
			let cityZoom = '';
			// let cityZoom = this.custZoom ? this.custZoom : 0.6;
			console.log('this.cityInfo.label', this.cityInfo.label);
			if(this.cityInfo.label == "内蒙古") {
				cityZoom = this.custZoom ? this.custZoom : 0.6;
			} else {
				cityZoom = this.zoom;
			}
			let width = document.getElementById("nmg_map").clientWidth;
			let option = {
				title: {
					show: false,
				},
				tooltip: {
					show: true,
					trigger: "item",
					triggerOn: "mousemove|click",
					textStyle: {
						color: "rgba(255, 255, 255, 0.8)",
					},
					extraCssText:
						"box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",

					backgroundColor: "rgba(0, 10, 49, 0.7)",
					axisPointer: {
						type: "none", // 默认为直线，可选为：'line' | 'shadow'
					},
					triggerTooltip: true,
					confine: true, // 将 tooltip 框限制在图表的区域内
					formatter: (params) => {
						// this.currentArea = params.data;
						// let name = params.name;
						// let marker = params.marker;
						// let num = params.data.alarmNum ? params.data.alarmNum : 0;
						let str = `<div>
              <p>${params.name}</p>
            </div>`;
						return str;
					},
				},
				geo: [
					// geo中设置的map的zlevel等级为1在series map的上层
					{
						// 地图背景层
						type: "map",
						map: cityName,
						roam: false, // 不开启缩放和平移
						// center: undefined,
						zLevel: -3,
						zoom: cityZoom,
						animationDurationUpdate: 0,
						aspectScale: 1,
						layoutCenter: ["50%", "52%"],
						layoutSize: width,
						scaleLimit: {
							//滚轮缩放的极限控制
							min: 0.2,
							max: 5,
						},
						silent: true, // 图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件
						label: {
							// 城市名称
							show: false,
							emphasis: {
								// 鼠标经过样式
								show: false,
							},
						},
						itemStyle: {
							areaColor: "#1E4791",
							emphasis: {
								label: {
									show: false,
								},
								borderWidth: 0,
								areaColor: "#1E4791",
							},
						},
					},
				],
				series: [
					{
						// 飞线
						name: "",
						type: "lines",
						zlevel: 2,
						symbol: ["none", "arrow"],
						symbolSize: 5,
						effect: {
							show: true,
							period: 2,
							trailLength: 0.02,
							symbol: "arrow",
							symbolSize: 3,
						},
						lineStyle: {
							color: "#ffd000",
							type: "dotted",
							width: 2,
							opacity: 0.1, // 尾迹线条透明度
							curveness: -0.3,
						},
						label: {
							show: false,
							position: "middle",
							color: "#ffd000",
							fontSize: 16,
							fontWeight: 300,
							textBorderWidth: 0,
							textShadowBlur: 0,
							borderRadius: 5,
							padding: [6, 10, 6, 10],
							backgroundColor: "rgba(0,0,0,0.25)",
						},
						data: this.mapLineData,
					},
					{
						// 地图表层
						type: "map",
						map: cityName,
						zLevel: -1,
						layoutCenter: ["50%", "50%"],
						layoutSize: width,
						aspectScale: 1, // 地图的长宽比
						zoom: cityZoom, //当前视角的缩放比例
						animationDurationUpdate: 0,
						roam: false,
						scaleLimit: {
							min: 0.2,
							max: 5,
						},
						silent: false,
						label: {
							show: true,
							textStyle: {
								color: "#021B30",
								fontSize: 10,
							},
							emphasis: {
								// 鼠标经过样式
								color: "#021B30",
								show: true,
								fontSize: 12,
								fontWeight: "bold",
							},
						},
						// 区域颜色
						itemStyle: {
							areaColor: "#25BEF7",
							borderWidth: 1, // 区块的边界宽度
							borderColor: "rgba(162, 231, 255, 0.77)",
							emphasis: {
								borderWidth: 2, // 区块的边界颜色
								areaColor: "#FFCB31",
								borderColor: "rgba(162, 231, 255, 0.77)", // 区块的边框颜色
							},
						},
						tooltip: {
							show: true,
							trigger: "item",
							triggerOn: "mousemove|click",
							textStyle: {
								color: "rgba(255, 255, 255, 0.8)",
							},
							extraCssText:
								"box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
							backgroundColor: "rgba(0, 10, 49, 0.7)",
							shadowBlur: 10, // 背景阴影长度
							shadowOffsetX: 2, // 背景阴影 X 偏移
							shadowOffsetY: 2, // 背景阴影 Y 偏移
							textStyle: {
								color: "#FFFFFF",
							},
							formatter: (params) => {
								// console.log('params', params);
								this.currentArea = params.data;
								let name = params.name;
								let marker = params.marker;
								let str = `<div>
                  <p><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FFCB31;"></span> ${params.name}</p>
                </div>`;
								return str;
							},
						},
					},
					{
						name: "时延散点地图",
						type: "effectScatter",
						coordinateSystem: "geo",
						showEffectOn: "render",
						zlevel: 0,
						rippleEffect: {
							number: 2, // 波纹的数量。
							period: 5, // 动画的周期，秒数。
							scale: 4, // 动画中波纹的最大缩放比例。
							brushType: "stroke", //
						},
						tooltip: {
							// 时延撒点鼠标经过显示的提示框
							trigger: "item",
							textStyle: {
								color: "rgba(255, 255, 255, 0.8)",
							},
							extraCssText:
								"box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75)",
							backgroundColor: "rgba(0, 10, 49, 0.7)",
							confine: true, //是否将 tooltip 框限制在图表的区域内
							formatter: function (params, ticket, callback) {
								//根据业务自己拓展要显示的内容
								var res = "";
								var name = params.name;
								var count = params.value ? params.value : 0;
								res = `<div style="box-shadow: 0px 0px 4px 1px rgba(95, 148, 229, 0.75); padding: 10px; position: absolute; top: 0; left:0; border-radius: 4px; border: 1px solid #04b9ff; background: rgba(0, 10, 49, 0.7);">
                  <div style='color:yellow; font-size: 14px;'>${name}</div>
                  <div style="display: flex; align-items: center;padding-top: 6px;">
                    <div style="height: 6px; width: 6px; border-radius: 50%; background:yellow; margin-right: 10px;"></div>
                    <span style='color:#fff;font-size: 12px;margin-right: 20px;'>坐标</span>
                    <span style="font-size: 12px;font-family: 'PangMenZhengDao'">[${count}]</span>
                  </div>
                </div>`;
								return res;
							},
						},
						symbolSize: 6,
						label: {
							formatter: "{b}",
							position: [26, -12],
							color: "red",
							fontSize: 10,
							fontWeight: 600,
							textBorderWidth: 0,
							textShadowBlur: 0,
							borderWidth: 1,
							borderRadius: 6,
							borderColor: "#41ADCC",
							padding: [6, 10, 6, 10],
							backgroundColor: "rgba(0,0,0, 0.6)",
						},
						itemStyle: {
							color: "yellow",
						},
						data: this.dots,
						// data: this.getDotData(data),
					},
				],
			};

			let echart = echarts.init(document.getElementById("nmg_map"));
			echart.setOption(option, true);
			echart.off("click");

			// 地图点击事件
			echart.on("dblclick", (params) => {
				let val = this.cityList.filter((i) => {
					return i.label == params.name;
				});
				this.$emit("transferVal", params.name);
				this.cityName = val[0].value;
				this.changeCity(this.cityName);
				this.$refs.searchPart.changeCity(this.cityName);
			});
			// 地图外区域双击
			echart.getZr().on("dblclick", (params) => {
				if (!params.target) {
					this.$emit("transferVal", "内蒙古");
					this.cityName = "10";
					this.changeCity(this.cityName);
					this.$refs.searchPart.changeCity(this.cityName);
				}
			});
		},
	},
};
</script>

<style scoped>
.content {
	height: 39vh;
	/* width: 100%; */
	min-width: 45vw;
	display: flex;
	justify-content: center;
}
.nmg-map-box {
	width: 100%;
	min-height: 40vh;
	user-select: none;
}
</style>
