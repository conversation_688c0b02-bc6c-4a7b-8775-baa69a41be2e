<template>
  <div class="app-container">
    <el-row class="container1">
      <!-- 使用 split-pane 实现可拖拽分割 -->
      <split-pane :min-percent="25" :default-percent="30" split="vertical">
        <template slot="paneL">
          <div class="muen-left" v-loading="loading">
            <div class="avue-sidebar">
              <div style="width:100%;height:50%;">
                <div class="title">基本信息统计</div>
                <div id="chart" class="chart" ></div>
                <div class="neData">
                  <div class="neDataItem">
                    <img class="neIcon" src="@/assets/images/neicon.png" alt="">
                    <div class="num">{{neDataList.core_cnt}}</div>
                    <div class="neDataTitle">核心网网元数量</div>
                  </div>
                  <div class="neDataItem">
                    <img class="neIcon" src="@/assets/images/neicon1.png" alt="">
                    <div class="num">{{neDataList.ce_cnt}}</div>
                    <div class="neDataTitle">CE数量</div>
                  </div>
                  <div class="neDataItem">
                    <img class="neIcon" src="@/assets/images/neicon2.png" alt="">
                    <div class="num">{{neDataList.trans_cnt}}</div>
                    <div class="neDataTitle">传输网网元数量</div>
                  </div>
                </div>
              </div>
              <div style="width:100%;height:50%;">
                <div class="title">告警信息统计</div>
                <div class="alarmTotal">
                  <div class="alarmItem">
                    <img class="neIcon" src="@/assets/images/alarmIcon.png" alt="">
                    <div class="textContainer">
                      <div class="num">{{alarmDataList.ce_sum || 0}}</div>
                      <div class="neDataTitle">CE告警数量</div>
                    </div>
                  </div>
                  <div class="alarmItem">
                    <img class="neIcon" src="@/assets/images/alarmIcon1.png" alt="">
                    <div class="textContainer">
                      <div class="num">{{alarmDataList.trans_am_cnt || 0}}</div>
                      <div class="neDataTitle">传输告警数量</div>
                    </div>
                  </div>
                  <div class="alarmItem alarmItemh" @click="toggleCardFlip">
                    <span class="flip-icon">详情</span>
                    <div class="card-inner">
                      <div class="card-front">
                        <img class="neIcon" src="@/assets/images/alarmIcon2.png" alt="" style="margin-right: 10px;">
                        <div class="textContainer">
                          <div class="num">{{alarmDataList.circuit_sum || 0}}</div>
                          <div class="neDataTitle">影响电路数量</div>
                        </div>
                      </div>
                      <div class="card-back">
                        <!-- 这里是卡片背面的内容 -->
                        <div class="back-content">
                          <div class="alarm-list">
                            <el-tooltip 
                              effect="dark" 
                              :content="item" 
                              placement="top" 
                              v-for="(item,index) in alarmDataList.alarm_circuit_name" 
                              :key="item"
                            >
                              <div 
                                class="alarm-item" 
                                :class="{ 'active-circuit': activeCircuit === item }"
                                @click.stop="changeTopo(item)"
                              >
                                {{index+1}}、{{item}}
                              </div>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="alarmItem alarmItemh" @click="toggleCardFlips(alarmDataList.core_name)">
                  <!-- <div class="alarmItem alarmItemh" @click="toggleCardFlips()"> -->
                    <img class="neIcon" src="@/assets/images/alarmIcon3.png" alt="">
                    <div class="textContainer">
                      <div class="num numHover">{{alarmDataList.core_cnt || 0}}</div>
                      <div class="neDataTitle">影响核心网元数</div>
                    </div>
                  </div>
                  <div class="alarmItem alarmItemh" @click="toggleCardFlips(alarmDataList.trans_name)">
                  <!-- <div class="alarmItem alarmItemh" @click="toggleCardFlips()"> -->
                    <img class="neIcon" src="@/assets/images/alarmIcon4.png" alt="">
                    <div class="textContainer">
                      <div class="num numHover">{{alarmDataList.circuit_cnt || 0}}</div>
                      <div class="neDataTitle">流量超35%网元</div>
                    </div>
                  </div>
                  <div class="alarmItem">
                    <img class="neIcon" src="@/assets/images/alarmIcon5.png" alt="">
                    <div class="textContainer">
                      <div class="num">{{alarmDataList.port_cnt || 0}}</div>
                      <div class="neDataTitle">流量超35%端口</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template slot="paneR">
          <div class="content_right">
            <div class="absolute_btn">
              <el-button @click="goToGIS" type="primary">gis路由</el-button>
            </div>
            <div class="nc-query" v-loading="loading">
              <iframe
              ref="myIframe"
              :src="url" 
              title=""
              style="width: 100%;height: 100%;border: none;"
              :key="componentKey"
              ></iframe>
            </div>
          </div>
        </template>
      </split-pane>
    </el-row>
  </div>
</template>

<script>
import SplitPane from 'vue-splitpane'
// import Topo from './topo/index.vue'
// import {datas} from "@/assets/img/topo/topoIcon.js";
import echarts from "echarts";
export default {
  name: "bigPictureCircuitName",
  components: {  SplitPane},
  props: {
    classId: {
      type: String,
      default: "spcRoom",
    },
    tableTitle: {
      type: String,
      default: "资源",
    },
  },
  watch: {
    filterText(val) {
        this.$refs.tree.filter(val);
      },
  },
  data() {
    return {
      loading: false,
      filterText: '',
      treeExpandData: [1],
      treeData: [
        {
        id: 1,
        label: '全局电路',
        url:"http://************:8183/#/topology_component/ShannxiCoreNetTopologyIndex?type=SXHXWJKTPT-202504101111&resourceTopology=true&isLogin=false"
      }, 
      {
        id: 2,
        label: '业务电路',
        children: []
      }
      ],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'leaf'
      },
      firstCircuitName:'',
      url: 'http://************:8183/#/topology_component/ShannxiCoreNetTopologyIndex?type=SXHXWJKTPT-202504101111&resourceTopology=true&isLogin=false',
      overallUrl:"http://************:8183/#/topology_component/ShannxiCoreNetTopologyIndex?type=SXHXWJKTPT-202504101111&resourceTopology=true&isLogin=false",
      businessUrl:'http://************:8183/#/topology_component/ShannxiCoreNetEndToEndTopologyIndex?type=SXDLTP-202504100941&dataType=TOPO_CIRCUIT_DOMAIN_PHY_LINK|name||2b0619c9-e218-4578-97ab-eb0a21f79897|&dataId=12301018&isLogin=false',
      chartData:[],
      neDataList:{},
      alarmDataList:{},
      isFlipped:false,
      componentKey:0,
      activeCircuit: '',
      selectorCircuit: '',
    };
  },
  created() {
    //初始化查询配置 和tab展示列
        this.getTopoDatas()
        
  },
  mounted() {
    setTimeout(() => {
      const resizeOb = new ResizeObserver((entries) => {
        for (const entry of entries) {
          echarts.getInstanceByDom(entry.target).resize();
        }
      });
      resizeOb.observe(document.getElementById("chart"));
    }, 10);
    
    setTimeout(this.getTopoDatas(), 3000);
    this.initIframeMessageListener(); // 启动监听
  },
  methods: {
    // 初始化 iframe 消息监听
    initIframeMessageListener() {
      window.addEventListener('message', this.handleIframeMessage);
    },
    
    // 清理监听（避免内存泄漏）
    beforeDestroy() {
      window.removeEventListener('message', this.handleIframeMessage);
    },
    
    // 处理 iframe 发来的消息
    handleIframeMessage(event) {
      // ✅ 安全验证：检查消息来源是否可信
      // if (event.origin !== 'http://************') return;
      
      // console.log('收到 iframe 消息:', event.data);
      this.activeCircuit = event.data.circuitName;
      try {
        this.selectorCircuit = event.data.circuitName[0];
      } catch (error) {
        
      }
      // 根据消息类型处理
      // if (event.data.type === 'circuit_click') {
      //   this.circuitName = event.data.name; // 更新 Vue 数据
      // }
    },
    goToGIS() {
      if (this.selectorCircuit!=='') {
        this.$router.push({
          path: '/gis',
          query: {
            circuitName: this.selectorCircuit
          },
          replace: true
        });
      } else {
        this.$router.push({
          path: '/gis',
          // query: {
          //   circuitName: this.selectorCircuit
          // },
          replace: true
        });
      }
      
    },


    async getTopoDatas(){
        const res = await this.$api.transApi.getTopologyBaseStatistics();
        if (res.resultCode == 200) {
          this.neDataList = res.data;
          let fiber_circuit_rate = (res.data.fiber_circuit_cnt/res.data.circuit_cnt*100).toFixed(2);
          let trans_circuit_rate = (res.data.trans_circuit_cnt/res.data.circuit_cnt*100).toFixed(2);
          this.chartData = [{
            name: "光纤直驱电路",
            value: res.data.fiber_circuit_cnt,
            proportion:fiber_circuit_rate
          },
          {
            name: "传输承载电路",
            value: res.data.trans_circuit_cnt,
            proportion:trans_circuit_rate
          }]
          
          this.$nextTick(()=>{
            this.initChart()
          })
        }
        
        const res2 = await this.$api.transApi.getTopologyAlarmStatistics();
        if (res.resultCode == 200) {
          this.alarmDataList = res2.data;
        }
        
    },
    iframeLoaded(fatherData) {
      const iframe = this.$refs.myIframe;
      iframe.contentWindow.postMessage(fatherData, '*'); // 第二个参数是目标origin，'*'表示任何
    },
    initChart() {
      let mycenterbar = echarts.init(
        document.getElementById("chart")
      );
      const colorList = ["#44A2FF", "#27D05E", "#25FFFF", "#85F8C7", "#E1D978"];
      const dataset = this.chartData;
      const radius = ["30%", "50%", "65%"];
      const center = ["16%", "50%"];
      const unit = '';
      const imgCenter = {
                  top: "35%",
                  left: "10%",
                  width: 460,
                  height: 338
                };
      const seriesData = dataset.map((item) => {
        return {
          value: item.value,
          name: item.name,
          itemStyle: {
            borderWidth: 6,
            borderColor: "transparent"
          }
        };
      });
    
      const total = dataset.reduce((prev, cur) => {
        return prev + cur.value;
      }, 0);
      let option = {
        color: colorList,
        legend: {
          show: true,
          orient: "horizontal",
          icon: 'none',
          left: '25%',
          top: "middle",
          data:dataset,
          formatter: function (name) {
              let objItem =  dataset.find((item) => item.name === name);
              if(objItem?.proportion){
               if (name == '光纤直驱电路') {
                return `{dataValue|${objItem.value}/${(objItem.proportion )}%}\n{label| ${name}}`;
               } else if (name == '传输承载电路') {
                return `{dataValue2|${objItem.value}/${(objItem.proportion )}%}\n{label| ${name}}`;
               }
                
              }
              
          },
          itemGap:0,
          textStyle:{
              color: "#fff",
              rich: {
                label: {
                  width:80,
                  overflow:'truncate',
                  ellipsis:'...',
                  fontSize: 14,
                  fontFamily: "Microsoft YaHei",
                  fontWeight: 400,
                  color: "rgba(160, 206, 224, 1)",  
                },
                dataValue: {
                  fontSize: 18,
                  fontFamily: "D-DIN",
                  fontWeight: "bold",
                  color: "#44A2FF",
                  padding: 5,
                  verticalAlign:'bottom'
                },
                dataValue2: {
                  fontSize: 18,
                  fontFamily: "D-DIN",
                  fontWeight: "bold",
                  color: "#27D05E",
                  padding: 5,
                  verticalAlign:'bottom'
                },
              }
          },
        },
        tooltip: {
          show:false,
          trigger: "item"
        },
        graphic: {
          //图形中间图片
          elements: [
            {
              type: "image",
              style: {
                 image:'',//在这里放背景
                width:imgCenter?.width|| 400,
                height: imgCenter?.height||300
              },
              left: imgCenter?.left || 0,
              top: imgCenter?.top || 80
            }
          ]
        },
        series: [
          {
            name: "",
            type: "pie",
            radius:  ["55%", "75%"],
            center: center || ["200", "50%"],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: seriesData,
          },
          // 外圈背景
          {
            type: "pie",
            center: center,
            radius: ["60%", "85%"],
            hoverAnimation: false,
            clockWise: false,
            itemStyle: {
              normal: {
                borderWidth: 1,
                borderColor: "rgba(193, 229, 255, .1)",
                color: new echarts.graphic.LinearGradient(1, 1, 1, 0, [
                  {
                    offset: 1,
                    color: "rgba(127, 242, 255, .2)",
                  },
                  {
                    offset: 0,
                    color: "rgba(109, 195, 255, .1)",
                  },
                ]),
              },
            },
            tooltip: {
              show: false,
            },
            label: {
              show: false,
            },
            data: [100],
          },
          {
            //内圆
            name: "内圆",
            type: "pie",
            radius: radius?.[0] || "38%",
            center: center || ["200", "50%"],
            z: 1,
            itemStyle: {
              normal: {
                color: "#0A2D4F",
              
                label: {
                  show: false
                },
                labelLine: {
                  show: false
                }
              }
            },
            label: {
              normal: {
                show: true,
                position: "center", // 展示在中间位置
                formatter: () => {
                  return `{a|${total}}\n{b| ${unit || "电路总数"}}`;
                },
                rich: {
                  a: {
                    color: "#44A2FF",
                    fontSize: 18,
                    fontWeight: 900
                  },
                  b: {
                    color: "#fff",
                    fontSize: 14,
                    padding: [5, 0, 0, 0]
                  }
                }
              }
            },
            hoverAnimation: false,
            tooltip: {
              show: false
            },
            data: [100]
          }
        ],
      };
      option && mycenterbar.setOption(option);
    },
    toggleCardFlip(event) {
      // 切换flipped类
      event.currentTarget.classList.toggle('flipped');
      
      // 或者使用Vue的方式（如果使用数据驱动）
      // this.isFlipped = !this.isFlipped;
    },
    changeTopo(val){
      this.iframeLoaded({ type: 'circuit_high_light',circuitName:val})
      this.activeCircuit = val;
    },
    toggleCardFlips(val) {
      this.iframeLoaded({ type: 'ne_high_light',neName:val})
    }
  },
};
</script>

<style lang="less" scoped>
.app-container {
  overflow: auto;
  height: calc(100vh - 100px);
  display: flex;

  .container1 {
    display: flex;
    flex: 1;
    padding: 24px;
    height: 100%;
    min-height: 840px;
    width: 100%;

    // 左侧菜单样式调整
    .muen-left {
      height: 100%;
      padding: 24px;
      margin-right: 16px;
      background: url("../../assets/images/menu_left.png") center center no-repeat;
      background-size: 100% 100%;

      .avue-sidebar {
        width: 100%;
        height: 100%;
        .title {
          width: 100%;
          height: 48px;
          background: url("../../assets/images/topoTitle.png");
          background-size: 100% 100%; /* 完全填充，可能变形 */
          background-repeat: no-repeat;
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: bold;
          // font-size: 24px;
          font-size: 1.4vw;
          color: #FFFFFF;
          line-height: 48px;
          text-shadow: 0px 2px 4px rgba(14,59,93,0.3);
          text-align: center;
          font-style: normal;
        }
        .chart {
          height: 35%;
          width: 100%;
          background: #0C2B46;
          border-radius: 4px;
          border: 1px solid #143A64;
          margin: 15px 0px;
        }
        .neData {
          height: 40%;
          width: 100%;
          display: flex;
          gap: 10px;             
        }
        .neData .neDataItem {
          flex: 1 1 0%;          /* 等比例分配，基础尺寸为0% */
          background: #0C2B46;
          border-radius: 4px;
          border: 1px solid #143A64;
          align-items: center;    /* 水平居中所有子元素 */
          text-align: center;     /* 文字也居中 */
        }
        .neIcon {
          margin: 8px 0;    /* 图片和下方元素的间距 */
          width: 65%;
          max-width: 72px;
        }
        .num {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 500;
          font-size: 22px;
          color: #44E5FF;
          line-height: 33px;
          text-align: center;
          font-style: normal;
        }
        .neDataTitle {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 400;
          // font-size: 14px;
          font-size: 0.8vw;
          color: #E1E1E1;
          line-height: 16px;
          text-align: center;
          font-style: normal;
        }
      }
      .alarmTotal {
        display: flex;
        flex-wrap: wrap; /* 允许换行 */
        gap: 10px; /* 项目之间的间距 */
        margin-top: 10px;
        .alarmItem {
          display: flex;          /* 启用 Flex 布局 */
          align-items: center;  /* 子项底部对齐 */
          gap: 10px;             /* 图片和文字之间的间距 */
        }
        .alarmItem {
          flex: 1 1 calc(50% - 10px); /* 每行两个，考虑gap间距 */
          min-width: 0; /* 防止内容溢出 */
          height: 100px; /* 设置固定高度或根据内容自适应 */
          box-sizing: border-box; /* 边框和内边距计入宽度 */
          background: #0C2B46;
          border-radius: 4px;
          border: 1px solid #143A64;
          cursor: pointer;
        }
        // .alarmItemh:hover {
        //   border: 1px solid #4C9FFF;
        // }
        .alarmItemh {
          position: relative;
          border: 1px solid #4C9FFF;
        }
        .flip-icon {
          position: absolute;
          top: 0px;
          right: 2px;
          z-index: 10;
          width: 32px;
          height: 24px;
          color: #ccc;
          font-size: 8px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          box-shadow: 0 2px 5px rgba(0,0,0,0.1);
          transition: all 0.3s;
        }
        /* 卡片翻转时图标也旋转 */
        .flipped .flip-icon i {
          transform: rotate(180deg);
        }
        
        .neIcon {
          object-fit: contain;   /* 保持图片比例 */
          width: 40%;
          max-width: 70px;
        }
        
        .textContainer {
          display: flex;
          flex-direction: column; /* 垂直排列数字和标题 */
        }
        
        .num {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 500;
          // font-size: 24px;
          font-size: 1.4vw;
          color: #44E5FF;
          line-height: 36px;
          text-align: left;
          font-style: normal;
        }
        
        .neDataTitle {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 400;
          // font-size: 14px;
          font-size: 0.8vw;
          color: #E1E1E1;
          line-height: 21px;
          text-align: left;
          font-style: normal;
        }
      }
      .card-inner {
        position: relative;
        width: 100%;
        height: 100%;
        transition: transform 0.6s;
        transform-style: preserve-3d;
      }
      
      .alarmItem.flipped .card-inner {
        transform: rotateX(180deg);
      }
      
      .card-front{
        position: absolute;
        width: 100%;
        height: 100%;
        backface-visibility: hidden; /* 隐藏背面 */
        display: flex;
        align-items: center;
        // padding: 10px;
        box-sizing: border-box;
        border-radius: 4px;
        // background: #fff; /* 正面背景色 */
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }
      .card-back {
        position: absolute;
        width: 100%;
        height: 100%;
        backface-visibility: hidden; /* 隐藏背面 */
        display: flex;
        align-items: center;
        padding: 10px;
        box-sizing: border-box;
        border-radius: 4px;
        // background: #fff; /* 正面背景色 */
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      }
      
      .card-back {
        // background: #f5f5f5; /* 背面背景色 */
        transform: rotateX(180deg);
      }
      .back-content {
        height: 100%;
        width: 100%;
        .alarm-list {
          width: 100%;
          height: 100%;
          overflow: auto;
        }
        ::-webkit-scrollbar {
          width: 5px;
        }
        .alarm-item{
          width: 100%;
          height: 30px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 400;
          font-size: 0.6vw;
          color: #E1E1E1;
          line-height: 24px;
          // font-size: 1.4vw;
          text-align: left;
          font-style: normal;
          border-bottom: 1px solid #143A64;
          cursor: pointer;
          transition: background 0.3s;
        }
        .alarm-item:hover {
          color: #4C9FFF;
        }
        
        .active-circuit {
          color: #4C9FFF;
        }
        
      }
      .numHover:hover {
          color: #4C9FFF;
        }
      .card-inner {
  box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
  transition: transform 0.6s, box-shadow 0.3s;
}

.alarmItem:hover .card-inner:not(.flipped) {
  box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
}
      




      /* 引入上面定义的滚动条样式 */
      ::-webkit-scrollbar {
        width: 10px;
        background-color: #083f69;
      }

      ::-webkit-scrollbar-track {
        background: #062440;
      }

      ::-webkit-scrollbar-thumb {
        background: #1659A6;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
    }
  }



  .menu-bg {
    background: url("../../assets/images/menu-bg-yes.png") center center no-repeat;
    background-size: 100% 100%;

    height: 48px;
    // width: 255px;
    display: flex;
    text-align: center;
    color: #000a31;
    margin: 29px 25px 10px 25px;
    background-size: 100% 100%;
    background-color: transparent;
    align-items: center;
    justify-content: space-around;
    font-family: PingFangSC-Medium;
  }

  ::v-deep {
    .el-menu {
      border-right: none;
      margin: 0 25px 0 25px;

      .el-menu-item {
        color: #fff;
        font-family: PingFangSC-Regular;
        font-size: 16px;
        text-align: center;
      }

      .el-menu-item.is-active {
        color: #fff;
        text-align: center;
        background-color: rgba(37, 190, 247, 0.5);
        font-family: PingFangSC-Regular;
        font-size: 16px;
        border-right: 2px solid #169bfa;
      }

      .el-col {
        float: left;
      }
    }
  }

  .content_right {
    background: url("../../assets/images/content_right.png") center center no-repeat;
    background-size: 100% 100%;
    display: flex;
    height: 100%;
    // float: right;
    padding: 16px;
    flex: 1;
    position: relative;
    .absolute_btn{
      position: absolute;
      top: 13%;
      left: 5%;
    }
  }

  .table-title {
    color: #ffffff;
    font-size: 18px;
    line-height: 40px;

    &::before {
      content: "";
      width: 3px;
      height: 16px;
      vertical-align: middle;
      display: inline-flex;
      background: #ffffff;
      margin: 10px 8px 13px 0;
      box-shadow: 0px 0px 5px 0px #ffffff;
    }
  }

  .nc-query {
    border-radius: 2px;
    // padding: 24px 16px 12px;
    margin: 0 0 20px 0;
    width: 100%;
  }

  .formItem {
    float: left;
    width: 20%;
  }

  .page-list {
    text-align: right;
    width: 100%;
    color: #5299c9;
    margin-top: 20px;
  }



  ::v-deep .el-pagination__total {
    color: #afc5f1;
  }

  // 拖拽条样式
::v-deep .splitter-pane-resizer.vertical {
  width: 5px;
  background: rgba(37, 190, 247, 0.3);
  cursor: col-resize;
  &:hover {
    background: rgba(37, 190, 247, 0.5);
  }
}
::v-deep .vue-splitter-container {
  width: 100%;
}
::v-deep .splitter-pane-resizer.vertical{
  margin-left: -13px;
}
}
</style>

<style lang="less">
.el-loading-mask {
z-index: 998;
  background: rgba(5, 99, 153, 0.3);
}
.filter-tree {
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(22, 155, 250, 0.3) !important;
    .el-tree-node__label {
      color: #25beff;
      font-weight: bold;
    }
  }
}
</style>
