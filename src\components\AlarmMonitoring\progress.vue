<template>
  <div ref="progress" class="progress"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  props: {
    value: {
      type: Number,
      required: true
    },
    max: {
      type: Number,
      required: true
    }
  },
  mounted() {
    this.initProgress();
  },
  methods: {
    initProgress() {
      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          show: false
        },
        grid: {
          top: 0,
          left: '0%',
          right: '0%',
          bottom: '0%',
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitArea: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          axisLabel: {
            show: false,
          },
          data: [],
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [{
            // For shadow, 解决最大值接近整数时label被截断的问题
            type: 'pictorialBar',
            symbol: 'rect',
            symbolRepeat: 'fixed',
            symbolSize: [0, 35],
            symbolMargin: 2,
            symbolBoundingData: this.max,
            barWidth: '55%',
            itemStyle: {
              normal: {
                color: '#d9d9d9'
              }
            },
            label: {
              show: false,
            },
            data: [this.max],
            animation: false,
            z: 1
          },
          {
            name: "lalal",
            type: 'pictorialBar',
            symbol: 'rect',
            symbolRepeat: true,
            symbolSize: [1, 35],
            symbolMargin: 1,
            symbolBoundingData: this.max,
            barWidth: '50%',
            data: [this.value],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: '#794B2D' }, // 起始颜色
                  { offset: 1, color: '#F59350' }  // 结束颜色
                ])
              }
            },
            z: 2
          }
        ]
      };

      const myEcharts = this.$refs.progress;
      const chart = echarts.init(myEcharts);
      chart.setOption(option);
    }
  }
}
</script>

<style scoped>
.progress {
  width: 100%;
  height: 100%;
}
</style>