import axios from "@/utils/api.request";
const BASEURL = '/otnNpv'
const space = {
  // 区域
  getDistrict(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/getDistrict`,
      method: "post",
      data: inParams,
    });
  },
  // 导出区域
  exportDistrict(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/exportDistrict`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 局站
  getStation(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/getStation`,
      method: "post",
      data: inParams,
    });
  },
  // 导出局站
  exportStation(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/exportStation`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 机房
  getRoom(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/getRoom`,
      method: "post",
      data: inParams,
    });
  },
  // 导出机房
  exportRoom(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/exportRoom`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
  // 放置点
  getPosition(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/getPosition`,
      method: "post",
      data: inParams,
    });
  },
  // 导出放置点
  exportPosition(inParams) {
    return axios.request({
      url: `${BASEURL}/spatialResources/exportPosition`,
      method: "post",
      data: inParams,
      responseType: "blob",
    });
  },
}
export default space;